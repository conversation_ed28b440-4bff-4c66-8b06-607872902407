export const Programme__c: any = {
  institutions: ['School__r.Brand__c', 'COUNT(Id) total'],
  programmeDetails: [
    'Name',
    'School__r.Brand__c',
    'School__r.Name',
    'Programme_Description__c',
    'Subject__r.Name',
    'Level__r.Name',
    'Id',
  ],
  lookup: ['School__r.Id', 'School__r.Name'],
};
export const PricebookEntry: any = {
  institutions: ['count(Id)'],
  programmeDetails: [
    'Product2.Programme__r.School__r.Brand__c',
    'Product2.Programme__r.CurrencyIsoCode',
    'Product2.Programme__r.Level__r.Name level',
    'MIN(Product2.Duration__c) minDuration',
    'MAX(Product2.Duration__c) maxDuration',
    'MIN(UnitPrice) minPrice',
    'MAX(UnitPrice) maxPrice',
    'Product2.Programme__r.Name',
    'Product2.Programme__r.School__r.Name school',
    'Product2.Programme__r.Id',
    'Product2.Programme__r.Subject__r.Name subject',
  ],
  pricebookDetails: [
    'School__c',
    'CurrencyIsoCode',
    'UnitPrice',
    'UseStandardPrice',
    'PriceBook2.Id',
    'PriceBook2.Name',
    'Pricebook2.IsStandard',
    'Product2.Programme__c',
    'Product2.Name',
    'Product2.Duration__c',
    'Product2.Brand__c',
    'Product2.School__c',
    'Product2.Location__c',
    'Product2.StudyMode__c',
    'Product2.Intake__c',
    'Product2.ProgrammeCode__c',
    'Product2.StudyModeRelation__c',
    'Product2.DurationText__c',
    'Product2.BusinessUnitFilter__c',
    'Product2.HoursPerWeek__c',
    'Product2.IntakeTitle__c',
  ],
};
export const School__c: any = { lookup: ['Brand__c'] };
export const PicklistValueInfo: any = ['Label', 'Value'];
export const BusinessUnit__c: any = [
  'USP_1_Description__c',
  'USP_1_Title__c',
  'USP_2_Description__c',
  'USP_2_Title__c',
  'USP_3_Description__c',
  'USP_3_Title__c',
  'USP_Video_Link__c',
];
export const Level__c: any = ['Id', 'Name'];
export const Vertical__c: any = ['Id', 'Name'];
export const Product2: any = {
  programmeDetails: ['DurationText__c', 'Location__c', 'Delivery__r.Name'],
  comparePrograms: ['Location__c', 'Delivery__r.Name'],
  listPrograms: ['location__c', 'programme__c'],
};
export const Task: any = {
  opportunityAdmissionComments: [
    'Status',
    'Description',
    'LastModifiedDate',
    'Id',
    'WhatId',
    'Subject',
    'CreatedDate',
    'Duplicate_Task__c',
    'Document_Type__c',
    'Related_Record_Id__c',
    'Related_Record_Name__c',
    'Follow_Up__c',
  ],
};
export const OpportunityFile__c: any = {
  opportunityFilesById: [
    'Id',
    'Name',
    'ApplicationId__c',
    'CreatedDate',
    'DocumentType__c',
    'LetterType__c',
    'Opportunity__c',
    'OriginalValue__c',
    'OwnerId',
    'S3FileName__c',
    'Opportunity__r.brand__c',
    'Opportunity__r.BusinessUnitFilter__c',
    'Opportunity__r.LevelCode__c',
    'DocumentSource__c',
    'BucketName__c',
    'Additional_Info__c',
    'Related_Education_History__c',
  ],
};
export const Opportunity: any = {
  opportunityDetailsById: [
    'Id',
    'Name',
    'toLabel(Brand__c)Institution',
    'Brand__c',
    'CreatedDate',
    'Account.Name',
    'Account.PersonEmail',
    'Account.Phone',
    'StageName',
    'OwnerName__c',
    '(Select Location__c,Intake_Date__c,Product2.Duration__c,Product2.ProgrammeName__c,Product2.Campus_Days__c from OpportunityLineItems)',
    'AgentContact__c',
    'ApplicationFormId__c',
    'BusinessDeveloperName__c',
    'Admissions_Condition__c',
    'AgentAccount__r.Email__c',
    'AgentAccount__r.Phone',
    'AgentAccountName__c',
    'Agent_Contact__r.Email',
    'Agent_Contact__r.Phone',
    'Agent_Contact__r.Name',
    'RecordType.Name',
    'ApplicationSource__c',
    'OwnerEmail__c',
    'Owner.Phone',
    'BusinessUnitFilter__c',
    'Institution_Full_Name__c',
    'Owner.Appointment_Booking_Link__c',
    'DeclarationDate__c',
    'ProgrammeName__c',
    'Delivery_Mode__c',
    'Application_Submitted_Date__c',
    'OverallStartDate__c',
  ],
  opportunityDetailsByEmail: [
    'Owner.Phone',
    'OwnerEmail__c',
    'AgentAccount__r.Phone',
    'AgentAccount__r.Email__c',
    'Admissions_Condition__c',
    'BusinessDeveloperName__c',
    'ApplicationFormId__c',
    'AgentContact__c',
    'Account.Phone',
    'Account.Name',
    'Account.PersonEmail',
    'Id',
    'ApplicationId__c',
    'Brand__c',
    'CreatedDate',
    'toLabel(Brand__c)Institution',
    'Product_Intake_Date__c',
    'toLabel(Location__c)location',
    'CreatedBy.Name',
    'StageName',
    'Name',
    'toLabel(AdmissionsStage__c)',
    'ApplicationSubmitted__c',
    'ApplicationProgress__c',
    'RecordType.Name',
    '(Select Location__c,Intake_Date__c,Product2.Duration__c,Product2.ProgrammeName__c,Product2.Campus_Days__c from OpportunityLineItems)',
    'Institution_Full_Name__c',
    'AgentAccountName__c',
    'OwnerName__c',
    'Agent_Contact__r.Email',
    'Agent_Contact__r.Phone',
    'Agent_Contact__r.Name',
    'ApplicationSource__c',
    'BusinessUnitFilter__c',
    'Owner.Appointment_Booking_Link__c',
    'DeclarationDate__c',
    'ProgrammeName__c',
    'Delivery_Mode__c',
    'Application_Submitted_Date__c',
    'OverallStartDate__c',
  ],
};
export const accountDetails: string[] = [
  'Id',
  'Location__pc',
  'Name',
  'Phone',
  'PersonEmail',
  'FirstName',
  'LastName',
  'App_Hero_Can_Apply__c',
  'RecordTypeId',
];
export const LeadFieldsMap = {
  Email: 'email',
  LastName: 'lastName',
  FirstName: 'firstName',
  Country: 'country',
  City: 'city',
  State: 'state',
  PostalCode: 'zip',
  gaconnector_IP_Address__c: 'ipAddress',
  MobilePhone: 'cellPhone',
};
export const enquiryFields: string[] = [
  'Brand__c',
  'Account__c',
  'First_Name__c',
  'Last_Name__c',
  'Person_Email__c',
  'Phone__c',
  'Account_Type__c',
  'Data__c',
  'ProgrammeId__c',
];
