import { Module } from '@nestjs/common';
import { SalesforceService } from './salesforce.service';
@Module({
  providers: [
    {
      provide: 'R3_SALESFORCE_SOURCE',
      useFactory: async () => {
        return new SalesforceService(
          process.env.R3_CONSUMER_KEY,
          process.env.R3_CONSUMER_SECRET,
          process.env.R3_AUTH_URL,
          process.env.R3_ACCESS_TOKEN_SECRET,
          process.env.R3_GRANT_TYPE,
          process.env.R3_USER_NAME,
          process.env.R3_PASSWORD,
        );
      },
      inject: [],
    },
  ],
  exports: [
    {
      provide: 'R3_SALESFORCE_SOURCE',
      useFactory: async () => {
        return new SalesforceService(
          process.env.R3_CONSUMER_KEY,
          process.env.R3_CONSUMER_SECRET,
          process.env.R3_AUTH_URL,
          process.env.R3_ACCESS_TOKEN_SECRET,
          process.env.R3_GRANT_TYPE,
          process.env.R3_USER_NAME,
          process.env.R3_PASSWORD,
        );
      },
      inject: [],
    },
  ],
})
export class SalesforceModule {}
