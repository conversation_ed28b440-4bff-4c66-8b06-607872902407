import { Injectable, InternalServerErrorException } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
import {
  SecretsManagerClient,
  GetSecretValueCommand,
  UpdateSecretCommand,
  CreateSecretCommand,
} from '@aws-sdk/client-secrets-manager';
@Injectable()
export class SalesforceService {
  consumerKey: string;
  consumerSecret: string;
  authUrl: string;
  awsSecret: string;
  grantType: string;
  userName: string;
  password: string;
  constructor(
    consumerKey: string,
    consumerSecret: string,
    authUrl: string,
    awsSecret: string,
    grantType: string,
    username: string,
    password: string,
  ) {
    this.consumerKey = consumerKey;
    this.consumerSecret = consumerSecret;
    this.authUrl = authUrl;
    this.awsSecret = awsSecret;
    this.grantType = grantType;
    this.userName = username;
    this.password = password;
  }
  secretClient = new SecretsManagerClient({
    region: process.env.REGION,
  });
  async createAWSSecret(
    secretName: string,
    secretValue: string,
  ): Promise<void> {
    try {
      await this.secretClient.send(
        new CreateSecretCommand({
          Name: secretName,
          SecretString: JSON.stringify(secretValue),
        }),
      );

      // console.log(
      //   `Secret '${secretName}' created successfully. SecretKey: ${secretValue}`,
      // );
    } catch (error) {
      console.error('Error creating secret:', error);
    }
  }

  async getAWSSecret(secretName: string): Promise<string> {
    try {
      const secretValue = await this.secretClient.send(
        new GetSecretValueCommand({
          SecretId: secretName,
        }),
      );

      if (secretValue.SecretString) {
        return JSON.parse(secretValue.SecretString);
      } else {
        console.log('there is no secret Key');
      }
    } catch (error) {
      console.error('Error retrieving secret:', error);
      const errorType = error?.__type;
      if (errorType === 'ResourceNotFoundException') {
        try {
          const response = await axios.post(this.authUrl, null, {
            params: {
              grant_type: this.grantType,
              client_id: this.consumerKey,
              client_secret: this.consumerSecret,
              username: this.userName,
              password: this.password,
            },
          });
          this.createAWSSecret(secretName, response.data);
          return response.data;
        } catch (error) {
          throw error;
        }
      }
    }
  }

  async updateSecret(secretName, newSecretValue) {
    try {
      // Save the updated secret
      await this.secretClient.send(
        new UpdateSecretCommand({
          SecretId: secretName,
          SecretString: JSON.stringify(newSecretValue),
        }),
      );

      // console.log(
      //   `Secret '${secretName}' updated successfully. SecretKey: ${newSecretValue}`,
      // );
    } catch (error) {
      const errorType = error?.__type;
      console.log('Error Type:', errorType);
      if (errorType === 'ResourceNotFoundException') {
        this.createAWSSecret(secretName, newSecretValue);
      }
      console.error(`Error updating secret '${secretName}':`, error);
    }
  }

  async getCredentials(isTokenExpired = false): Promise<any> {
    let response;
    const secretKeyResponse = await this.getAWSSecret(this.awsSecret);
    if (!secretKeyResponse || isTokenExpired) {
      response = await axios.post(this.authUrl, null, {
        params: {
          grant_type: this.grantType,
          client_id: this.consumerKey,
          client_secret: this.consumerSecret,
          username: this.userName,
          password: this.password,
        },
      });

      await this.updateSecret(this.awsSecret, response.data);

      return response.data;
    }
    return secretKeyResponse;
  }
  async executeAPI(
    apiPath: string,
    method: string,
    payload = null,
    isTotalRequired = false,
    isQuery = true,
  ): Promise<any> {
    const axiosConfig = await this.buidAxiosConfig(
      apiPath,
      method,
      payload,
      isQuery,
    );
    try {
      const response = await axios(axiosConfig);
      if (isTotalRequired) {
        return response.data;
      } else if (method === 'GET') {
        return response.data.records;
      }
      return response;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        if (axiosError.response && axiosError.response.status === 401) {
          // Handle 401 Unauthorized error here, e.g., refresh the access token
          console.log(
            'Got a 401 Unauthorized error. Refreshing access token...',
          );
          await this.getCredentials(true); // Implement your token refresh logic here
          return this.executeAPI(apiPath, method, payload);
        }
        if (axiosError.response) {
          console.error(
            'Server responded with error:',
            axiosError.response.data,
          );
          console.error('HTTP status code:', axiosError.response.status);
          throw { message: axiosError.response.data };
        }
      } else if (error.request) {
        console.error('No response received from the server');
        throw new InternalServerErrorException(
          'No response received from the server',
        );
      } else {
        console.error('Error setting up the request:', error.message);
        throw new InternalServerErrorException('Error setting up the request');
      }
      throw error;
    }
  }
  async compositeRequest(requests, dependsOnPriorCall?: boolean): Promise<any> {
    try {
      const compositeResponse = [];
      const maxOperations = 25;
      for (let i = 0; i < requests.length; i += maxOperations) {
        const compositeRequestPayload = {};
        compositeRequestPayload['allOrNone'] = dependsOnPriorCall || false;
        compositeRequestPayload['compositeRequest'] = requests.slice(
          i,
          i + maxOperations,
        );
        const response = await this.executeAPI(
          'composite',
          'POST',
          compositeRequestPayload,
          true,
        );
        compositeResponse.push(...response.compositeResponse);
      }
      console.log(compositeResponse);
      return { compositeResponse };
    } catch (error) {
      throw error;
    }
  }

  async compositeRequestSobjects(requests, method = 'POST'): Promise<any> {
    try {
      const compositeRequestPayload = {};
      compositeRequestPayload['allOrNone'] = false;
      compositeRequestPayload['records'] = requests;
      const response = await this.executeAPI(
        'composite/sobjects',
        method,
        compositeRequestPayload,
        true,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  async buidAxiosConfig(endpoint, method, payload, isQuery) {
    const clientCredentials = await this.getCredentials();
    const rootPath = `${clientCredentials.instance_url}/services/data/${process.env.SALESFORCE_API_VERSION}/`;
    switch (method) {
      case 'POST':
      case 'PATCH':
        return {
          url: `${rootPath}${endpoint}`,
          method,
          headers: {
            Authorization: `Bearer ${clientCredentials.access_token}`,
          },
          data: payload,
        };
      case 'DELETE':
        return {
          url: `${rootPath}${endpoint}`,
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${clientCredentials.access_token}`,
          },
        };
      case 'GET':
        if (isQuery) {
          return {
            url: `${rootPath}query?q=${endpoint}`,
            method: 'GET',
            headers: {
              Authorization: `Bearer ${clientCredentials.access_token}`,
            },
          };
        } else {
          return {
            url: `${rootPath}${endpoint}`,
            method: 'GET',
            headers: {
              Authorization: `Bearer ${clientCredentials.access_token}`,
            },
          };
        }
    }
  }
}
