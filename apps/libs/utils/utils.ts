import { lastValueFrom } from 'rxjs';
import {
  BusinessUnit__c,
  Level__c,
  Opportunity,
  OpportunityFile__c,
  PricebookEntry,
  Product2,
  Programme__c,
  School__c,
  Task,
  Vertical__c,
} from '../salesforce/default.fields';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';

export async function queryBuilder(
  entityName: string,
  allowedEntities: any,
  object = null,
): Promise<any> {
  let matchingEntity;
  if (entityName && object) {
    matchingEntity = allowedEntities.AllowedEntities.find(
      (entity) => entity.entityName === entityName && entity.object === object,
    );
    if (!matchingEntity) {
      matchingEntity = allowedEntities.AllowedEntities.find(
        (entity) =>
          entity.entityName === entityName && entity.allowedFields === '*',
      );
    }
  } else if (entityName) {
    matchingEntity = allowedEntities.AllowedEntities.find(
      (entity) =>
        entity.entityName === entityName && entity.allowedFields === '*',
    );
    if (!matchingEntity) {
      matchingEntity = allowedEntities.AllowedEntities.find(
        (entity) =>
          entity.entityName === entityName && entity.allowedFields !== '*',
      );
    }
  }
  if (!matchingEntity) {
    throw new Error('Unauthorized');
  }
  const AllowedFields = matchingEntity.allowedFields;

  if (
    matchingEntity.BrandFieldName &&
    allowedEntities.brandAccess &&
    allowedEntities.brandAccess !== '*'
  ) {
    matchingEntity.filters = matchingEntity.filters || [];
    matchingEntity.filters.push(
      await this.createBrandFilter(
        matchingEntity.BrandFieldName,
        allowedEntities.brandAccess,
      ),
    );
  }

  let whereClause;
  if (matchingEntity.filters) {
    whereClause = await this.buildFilteredQuery(
      matchingEntity,
      matchingEntity.filters,
    );
  }

  const fieldsRequired =
    AllowedFields === '*'
      ? await this.getFieldRequired(entityName, object)
      : await this.stringToList(AllowedFields);
  const selectQuery = `SELECT ${fieldsRequired} FROM ${entityName}`;
  return { fieldsRequired: fieldsRequired, whereClause: whereClause };
}

export async function createBrandFilter(brandFieldName, brandAccess) {
  const brandQuery = {
    fieldName: brandFieldName,
    fieldType: 'String',
    fieldValue: brandAccess,
    operator: 'IN',
  };
  return brandQuery;
}

export async function buildFilteredQuery(
  entityDetails,
  filter: any[],
): Promise<string> {
  const brandFieldFilterIndex = filter.findIndex(
    (field) => field.fieldName === 'BusinessUnit__c',
  );

  if (filter.length === 1) {
    const whereClause = await this.applyFilter(filter[0]);
    return `${whereClause}`;
  } else {
    const whereClauses = await Promise.all(
      filter.map(async (field) => await this.applyFilter(field)),
    );
    return `${whereClauses.join(' AND ')}`;
  }
}
export async function fetchData(path): Promise<any> {
  try {
    const headers = {
      'Content-Type': 'application/json',
    };

    const response = await axios.get(path, {
      headers,
    });

    return response.data;
  } catch (error) {
    console.log('error: ', error);
    throw error?.response?.data;
  }
}

export async function getFieldRequired(entityName: string, object: string) {
  switch (entityName) {
    case 'BusinessUnit__c':
      return BusinessUnit__c;
    case 'Programme__c':
      return object ? Programme__c[object] : '';
    case 'PricebookEntry':
      return object ? PricebookEntry[object] : '';
    case 'Product2':
      return object ? Product2[object] : '';
    case 'Level__c':
      return Level__c;
    case 'Vertical__c':
      return Vertical__c;
    case 'School__c':
      return object ? School__c[object] : '';
    case 'Opportunity':
      return object ? Opportunity[object] : '';
    case 'Task':
      return object ? Task[object] : '';
    case 'OpportunityFile__c':
      return object ? OpportunityFile__c[object] : '';
    default:
      return '';
  }
}

export async function applyFilter(field: any): Promise<string> {
  const fieldName = field.fieldName;
  const fieldType = field.fieldType;
  let fieldValue = field.fieldValue;
  let operator = field.operator.trim();

  switch (fieldType) {
    case 'String':
      switch (operator) {
        case '=':
          operator = `= '${fieldValue}'`;
          break;
        case 'STARTS WITH':
          operator = `LIKE '${fieldValue}%'`;
          break;
        case 'ENDS WITH':
          operator = `LIKE '%${fieldValue}'`;
          break;
        case 'CONTAINS':
          operator = `LIKE '%${fieldValue}%'`;
          break;
        case 'IN':
        case 'NOT IN':
          if (!Array.isArray(fieldValue)) {
            fieldValue = [fieldValue];
          }
          operator = `${operator} (${fieldValue
            .map((value: any) => `${value}`)
            .join(', ')})`;

          break;
        default:
          throw new Error(
            `Invalid operator "${field.operator}" for String type.`,
          );
      }
      break;

    case 'Int':
    case 'Float':
      switch (field.operator) {
        case '=':
        case 'IN':
        case 'NOT IN':
        case '!=':
        case '>':
        case '<':
        case '>=':
        case '<=':
          operator = `${field.operator} ${fieldValue}`;
          break;
        default:
          throw new Error(
            `Invalid operator "${field.operator}" for Numeric type.`,
          );
      }
      break;

    default:
      throw new Error(`Invalid field type "${fieldType}".`);
  }

  return `${fieldName} ${operator}`;
}

export async function stringToList(input: string) {
  return input.split(',').map((item) => item.trim());
}

export async function getStatus(opportunity) {
  const stage = opportunity?.StageName.toLowerCase();
  const recordTypeName = opportunity?.RecordType?.Name.toLowerCase();
  const admissionsStage = opportunity?.AdmissionsStage__c?.toLowerCase();
  if (
    ['offer', 'payment', 'visa', 'accepted', 'not expected'].includes(stage)
  ) {
    return 'Offers';
  } else if (
    opportunity.ApplicationSubmitted__c &&
    (stage === 'admissions stage' ||
      admissionsStage === 'Application Under Review')
  ) {
    return 'Admissions_Review';
  } else if (
    !opportunity.ApplicationSubmitted__c &&
    recordTypeName === 'agent onboarding'
  ) {
    return 'Agents_Drafts';
  } else if (!opportunity.ApplicationSubmitted__c) {
    return 'Drafts';
  }
}

export async function makeHTTPRequest<T>(
  httpService: HttpService,
  method: 'get' | 'post' | 'put' | 'delete',
  url: string,
  data?: any,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse<T>> {
  try {
    let response: AxiosResponse<T>;

    switch (method) {
      case 'get':
        response = await lastValueFrom(httpService.get<T>(url, config));
        break;
      case 'post':
        response = await lastValueFrom(httpService.post<T>(url, data, config));
        break;
      case 'put':
        response = await lastValueFrom(httpService.put<T>(url, data, config));
        break;
      case 'delete':
        response = await lastValueFrom(httpService.delete<T>(url, config));
        break;
      default:
        throw new HttpException('Method not supported', 400);
    }

    return response;
  } catch (error) {
    const statusCode =
      error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
    const message =
      error.response?.data || error.message || 'Unknown error occurred';

    throw new HttpException(
      {
        message: `HTTP request failed`,
        details: {
          url,
          method,
          statusCode,
          error: message,
          requestConfig: error.config ?? '',
          stack: error.stack,
          code: error.code ?? 'ERR_HTTP_REQUEST_FAILED',
        },
      },
      statusCode,
    );
  }
}
