import { Controller, Get } from '@nestjs/common';
import { ApiSecurity } from '@nestjs/swagger';
import { AppService } from './app.service';

@Controller('/salesforce')
@ApiSecurity('key')
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get('/tested')
  getHello(): string {
    return this.appService.getHello();
  }
  @Get('/')
  get(): string {
    return this.appService.getHello();
  }
}
