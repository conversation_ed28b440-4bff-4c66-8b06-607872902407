import {
  DynamicModule,
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { ApiKeyCheckMiddleware } from './common/middleware/api-key-check-middleware';
import { ConsumerModule } from 'apps/src/consumer/consumer.module';
import { R3SalesforceProcessModule } from 'apps/src/process-layer/r3-salesforce-process-layer/r3.process.module';
import { R3SalesforceSystemModule } from 'apps/src/system-layer/r3-salesforce/r3.salesforce.system.module';
import { R3GusSalesforceModule } from 'apps/src/system-layer/r3-gus-salesforce/r3.gus.salesforce.module';
import { GusSalesforceSystemModule } from 'apps/src/system-layer/gus-salesforce/gus.salesforce.system.module';
import { GusSalesforceProcessModule } from 'apps/src/process-layer/gus-salesforce-process-layer/gus.salesforce.process.module';
import { StudentDetailsModule } from 'apps/src/system-layer/gus-studentdetail/gus.studentdetails.module';
import { StudentDetailModule } from 'apps/src/process-layer/gus-studentdetail-process-layer/gus.studentdetail.process.module';
import { IBATStudentApplicationModule } from 'apps/src/experience-layer/ibat-student-application-pipeline-process/module';
import { HZUSalesforceModule } from 'apps/src/system-layer/hzu-salesforce/hzu.salesforce.module';
import { HZUSalesforceProcessModule } from 'apps/src/process-layer/hzu-salesforce-process-layer/hzu.salesforce.module';
import { CobrandingModule } from './experience-layer/cobranding/module';
import { TruliooVerificationModule } from './system-layer/trulioo-verification/truliooVerification.module';
import { OauthModule } from './common/oauth/oauth.module';
import { SqsModule } from './common/sqs/sqs.module';
import { TruliooVerificationKybVerificationModule } from './process-layer/trulioo-kyb-verification-process-layer/truliookyb.verification.process.module';
import { ParameterStoreCacheModule } from './common/parameterStoreCache/parameterStoreCache.module';
import { ParameterStoreCacheService } from './common/parameterStoreCache/parameterStoreCache.service';
import { DynamoDBModule } from './common/dynamoDB/dynamoDB.module';
import { CertValidationModule } from './system-layer/cert-validation/cert.validation.module';
import { CertValidationProcessModule } from './process-layer/cert-validation-process-layer/cert.validation.process.module';

async function createParameterStoreCacheService() {
  const parameterStoreCacheService = new ParameterStoreCacheService();
  try {
    await parameterStoreCacheService.getParameter('');
  } catch (error) {
    console.log('Failed to Fetch SSM Store', error);
  }
  return parameterStoreCacheService;
}

@Module({})
export class AppModule implements NestModule {
  static async register(): Promise<DynamicModule> {
    const parameterStoreCacheService = await createParameterStoreCacheService();

    return {
      module: AppModule,
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: ['.env.local', '.env'],
        }),
        ConsumerModule,
        GusSalesforceSystemModule,
        GusSalesforceProcessModule,
        StudentDetailsModule,
        StudentDetailModule,
        IBATStudentApplicationModule,
        HZUSalesforceModule,
        HZUSalesforceProcessModule,
        R3SalesforceProcessModule,
        R3SalesforceSystemModule,
        R3GusSalesforceModule,
        CobrandingModule,
        TruliooVerificationModule.forRoot(parameterStoreCacheService),
        TruliooVerificationKybVerificationModule.forRoot(
          parameterStoreCacheService,
        ),
        OauthModule,
        SqsModule,
        ParameterStoreCacheModule,
        DynamoDBModule,
        CertValidationModule.forRoot(parameterStoreCacheService),
        CertValidationProcessModule.forRoot(parameterStoreCacheService),
      ],
      controllers: [AppController],
      providers: [AppService],
    };
  }

  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(ApiKeyCheckMiddleware)
      .exclude(
        {
          path: 'gus/trulioo/agencykyb/notifyverification/:enquiryTypeId',
          method: RequestMethod.POST,
        },
        {
          path: 'gus/trulioo/agencykybenrollment/notifyverification/:enquiryTypeId',
          method: RequestMethod.POST,
        },
        {
          path: 'gus/student/certificate/validation',
          method: RequestMethod.POST,
        },
        {
          path: 'gus/proficiencyQualification/(.*)',
          method: RequestMethod.ALL,
        },
      )
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
