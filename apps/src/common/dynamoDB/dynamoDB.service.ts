import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';

@Injectable()
export class DynamoDBService {
  private dynamoDB: AWS.DynamoDB.DocumentClient;

  constructor() {
    this.dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
  }

  async putObject(table: string, object: Record<string, any>): Promise<any> {
    const params: AWS.DynamoDB.DocumentClient.PutItemInput = {
      TableName: table,
      Item: object,
    };

    try {
      const res = await this.dynamoDB.put(params).promise();
      return res;
    } catch (err) {
      console.error(`Error putting object in DynamoDB: ${err.message}`);
      throw err;
    }
  }

  async updateObject(
    table: string,
    key: Record<string, any>,
    object: Record<string, any>,
  ): Promise<any> {
    const updatedAt = new Date().toISOString();
    object.updatedAt = updatedAt;

    const { keys, update } = this.dynamodbUpdateRequest({
      keys: key,
      values: object,
    });

    const params: AWS.DynamoDB.DocumentClient.UpdateItemInput = {
      TableName: table,
      Key: keys,
      ...update,
    };

    try {
      const res = await this.dynamoDB.update(params).promise();
      return res;
    } catch (err) {
      console.error(`Error updating object in DynamoDB: ${err.message}`);
      throw err;
    }
  }

  async getObject(
    table: string,
    keys: Record<string, any>,
    projectionExpression: string | null = null,
    expressionAttributeNames: Record<string, string> | null = null,
  ): Promise<AWS.DynamoDB.DocumentClient.GetItemOutput> {
    const params: AWS.DynamoDB.DocumentClient.GetItemInput = {
      TableName: table,
      Key: keys,
    };

    if (projectionExpression) {
      params.ProjectionExpression = projectionExpression;
    }

    if (expressionAttributeNames) {
      params.ExpressionAttributeNames = expressionAttributeNames;
    }

    try {
      const res = await this.dynamoDB.get(params).promise();
      return res;
    } catch (err) {
      console.error(`Error getting object from DynamoDB: ${err.message}`);
      throw err;
    }
  }

  async queryObjects(
    params: AWS.DynamoDB.DocumentClient.QueryInput,
  ): Promise<AWS.DynamoDB.DocumentClient.QueryOutput> {
    try {
      const res = await this.dynamoDB.query(params).promise();
      return res;
    } catch (err) {
      console.error(`Error querying objects from DynamoDB: ${err.message}`);
      throw err;
    }
  }

  private dynamodbUpdateRequest(params: {
    keys: Record<string, any>;
    values: Record<string, any>;
  }) {
    const { keys, values } = params;
    const sets: string[] = [];
    const removes: string[] = [];
    const expressionNames: Record<string, string> = {};
    const expValues: Record<string, any> = {};

    for (const [key, value] of Object.entries(values)) {
      expressionNames[`#${key}`] = key;
      if (value !== undefined && value !== null) {
        sets.push(`#${key} = :${key}`);
        expValues[`:${key}`] = value;
      } else {
        removes.push(`#${key}`);
      }
    }

    let expression = sets.length ? `SET ${sets.join(', ')}` : '';
    expression += removes.length ? ` REMOVE ${removes.join(', ')}` : '';

    return {
      keys,
      update: {
        UpdateExpression: expression,
        ExpressionAttributeNames: expressionNames,
        ExpressionAttributeValues: expValues,
      },
    };
  }
}
