import { Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import * as AWS from 'aws-sdk';
import * as uuid from 'uuid';
import * as jwt from 'jsonwebtoken';
// eslint-disable-next-line @typescript-eslint/no-var-requires
import * as AWSXRay from 'aws-xray-sdk';
// eslint-disable-next-line @typescript-eslint/no-var-requires
AWSXRay.captureHTTPsGlobal(require('http'));
// eslint-disable-next-line @typescript-eslint/no-var-requires
AWSXRay.captureHTTPsGlobal(require('https'));
AWSXRay.capturePromise();
// eslint-disable-next-line @typescript-eslint/no-var-requires
AWSXRay.captureAWS(require('aws-sdk'));
@Injectable()
export class ApiKeyCheckMiddleware implements NestMiddleware {
  async use(req: Request, res: Response, next: NextFunction) {
    try {
      if (req.headers['authorization']) {
        const authToken = req.headers['authorization'];
        const decodedToken = jwt.decode(authToken, { complete: true });
        const parts = decodedToken.payload.scope.split('_');
        const apiKey = parts[1].split('/')[0];
        req.headers['x-api-key'] = apiKey;
      }
      if (!req.headers['x-api-key']) {
        return res.status(401).json({ message: 'Unauthorized' });
      }

      const apikey = req.headers['x-api-key'];
      const requestPath = req.path.split('/salesforce/');
      let path = requestPath.length > 1 ? requestPath[1] : requestPath[0];
      if (path === 'consumer/create') {
        return next();
      }
      const requestId = req.headers['correlation-id'] || uuid.v4();
      const pathParts = path.split('/');
      const endpoint = pathParts.shift();
      const dynamicData = pathParts.join('/');
      const httpMethod = req.method;
      const allowedEndpointsWithParams = [
        'opportunities',
        'accounts',
        'programmes',
        'pricebookdetailsbyprogrammeid',
        'opportunityfilesbyopportunityid',
        'applications',
        'r3/contentdocument',
        'r3/recordtypeid',
        'r3/contactid',
        'r3/applicationid',
        'r3/consumer',
        'r3/consumers',
      ];
      if (
        allowedEndpointsWithParams.includes(endpoint) &&
        httpMethod === 'GET'
      ) {
        if (dynamicData) {
          path = endpoint;
        }
      }
      const processedApiKey =
        typeof apikey === 'string'
          ? apikey
          : Array.isArray(apikey) && apikey.length > 0
          ? apikey[0]
          : '';
      AWSXRay.captureAsyncFunc(
        'sub_segment',
        async (subsegment: AWSXRay.Subsegment) => {
          try {
            subsegment.addAnnotation('api_key', processedApiKey);
          } finally {
            subsegment.close();
          }
        },
      );
      const dynamoDB = new AWS.DynamoDB.DocumentClient({
        region: process.env.REGION,
      });
      const params = {
        TableName: process.env.CONSUMER_CONFIG_TABLE,
        Key: {
          PK: apikey,
        },
      };

      const data = await dynamoDB.get(params).promise();
      const retrievedItem = data.Item;

      if (!retrievedItem || !retrievedItem.AllowedEndpoints) {
        return res.status(401).json({ message: 'Unauthorized' });
      }

      const containsEndpoint = retrievedItem.AllowedEndpoints.some((endpoint) =>
        path.split(':')[0].toLowerCase().includes(endpoint.toLowerCase()),
      );

      if (!containsEndpoint) {
        return res.status(401).json({ message: 'Unauthorized' });
      }

      req['allowedEntities'] = retrievedItem;
      req['requestId'] = requestId;
      req['account-Id'] = req.headers['account-id'];
      next();
    } catch (error) {
      console.log('Error', error);
      return res.status(500).json({ message: 'Internal Server Error' });
    }
  }
}
