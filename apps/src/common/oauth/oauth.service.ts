// src/oauth/oauth.service.ts
import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { AxiosResponse } from 'axios';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';

@Injectable()
export class OauthService {

  constructor(private readonly httpService: HttpService) {}

  async generateToken(tokenUrl: string, clientId: string, clientSecret: string, grantType: string): Promise<string> {
    const params = new URLSearchParams();
    params.append('grant_type', grantType);
    params.append('client_id', clientId);
    params.append('client_secret', clientSecret);

    try {
      const response: AxiosResponse = await lastValueFrom(
        this.httpService.post(tokenUrl, params, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }),
      );

      return response.data.access_token; 
    } catch (error) {
      throw error;
    }
  }
}
