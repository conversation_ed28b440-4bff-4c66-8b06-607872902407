import { Injectable } from '@nestjs/common';
import { SSMClient, GetParametersCommand } from '@aws-sdk/client-ssm';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import * as uuid from 'uuid';

@Injectable()
export class ParameterStoreCacheService {
  private ssmClient: SSMClient;
  private parametersCache: { [key: string]: string } | null = null;
  private lastFetchTime: number | null = null;
  private readonly cacheTTL: number = 300000; // 5 minutes
  private readonly cloudWatchLoggerService: CloudWatchLoggerService =
    new CloudWatchLoggerService(
      process.env.REGION,
      `gus-middleware-logger-${process.env.STAGE?.toLowerCase()}`,
      process.env.STAGE?.toLowerCase() === 'dev'
        ? 'https://gus.webhook.office.com/webhookb2/7d7ac91c-96e0-4fa2-84c3-c30b54776370@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/56f1e2e9460446b1a7698af920a21130/e06d3c50-7be6-44a2-87e7-48a4696d8297/V2Kurft_UhzwbDWan4cFy7TLTZyeDq9Vy2fDTzRNKaPSY1'
        : 'https://gus.webhook.office.com/webhookb2/7d7ac91c-96e0-4fa2-84c3-c30b54776370@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/6cff5221988d460aa3db0be35a164b6a/e06d3c50-7be6-44a2-87e7-48a4696d8297/V2kBqUhpcGYQm91NiaVBhoVjLcXWZvod_R9kHKeWF6z581',
      true,
    );
  private readonly loggerEnum = new LoggerEnum();
  private correlationId: string = uuid.v4();

  constructor() {}

  private async fetchParameters(): Promise<{
    [key: string]: string | string[];
  }> {
    const now = Date.now();

    if (
      this.parametersCache &&
      this.lastFetchTime &&
      now - this.lastFetchTime < this.cacheTTL
    ) {
      return this.parametersCache;
    }

    try {
      this.ssmClient = new SSMClient({ region: process.env.region });
      const command = new GetParametersCommand({
        Names: [`/gus/eip/envvariables/${process.env.STAGE}`],
        WithDecryption: true,
      });

      const result = await this.ssmClient.send(command);
      const params = result?.Parameters?.[0]?.Value;

      try {
        this.parametersCache =
          typeof params === 'string' ? JSON.parse(params) : params;
      } catch (error) {
        this.parametersCache = {};
        await this.sendAlert(error);
      }

      this.lastFetchTime = now;
    } catch (error) {
      this.parametersCache = {};
      await this.sendAlert(error);
    }

    return this.parametersCache;
  }

  async getParameter(name: string): Promise<string | string[]> {
    const parameters = await this.fetchParameters();
    return parameters?.[name] ?? null;
  }

  private async sendAlert(error): Promise<any> {
    try {
      await this.cloudWatchLoggerService.logAlert(
        this.correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.CLOUDWATCH_LOGS,
        this.loggerEnum.Event.CREATE_TEAMS_ALERT,
        this.loggerEnum.UseCase.SEND_ALERTS,
        null,
        {
          errorStack: error.stack,
          errorMsg: error.message,
        },
        '🚨 **Error in ParameterStoreCacheService : SSM** 🚨',
        null,
        null,
        `gus-eip-alert/Internal/${this.correlationId}`,
        false,
        'Error',
      );
    } catch (error) {
      return;
    }
  }
}
