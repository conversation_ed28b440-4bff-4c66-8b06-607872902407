import { Injectable } from '@nestjs/common';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class SqsService {
    constructor() {}
    
    async sendMessage(messageBody: string, queueUrl: string, region:string, messageGroupId?: string): Promise<any> {
        const params = {
            QueueUrl: queueUrl,
            MessageBody: messageBody,
            MessageGroupId: messageGroupId, 
            MessageDeduplicationId: uuidv4(),
        };

        try {
            const sqsClient = new SQSClient({ region: region });
            const command = new SendMessageCommand(params);
            const response = await sqsClient.send(command);
            return response;
        } catch (error) {
            throw error;
        }
    }
}

