import {
  Controller,
  Patch,
  Body,
  Delete,
  Post,
  Param,
  Get,
} from '@nestjs/common';
import { ConsumerService } from './consumer.service';
@Controller('/salesforce')
export class ConsumerController {
  constructor(private readonly consumerService: ConsumerService) {}
  @Post('/consumer/create')
  async createConsumer(@Body() event: any): Promise<any> {
    return await this.consumerService.createConsumer(event);
  }
  @Patch('/consumer/update')
  async updateConsumer(@Body() event: any): Promise<any> {
    return await this.consumerService.updateConsumer(event);
  }
  @Delete('/consumer/:apiKey')
  async deleteConsumer(@Param('apiKey') apiKey: string): Promise<any> {
    return await this.consumerService.deleteConsumer(apiKey);
  }
  @Get('/consumers/:apiKey')
  async getConsumer(@Param('apiKey') apiKey: string): Promise<any> {
    return await this.consumerService.getConsumer(apiKey);
  }
}
