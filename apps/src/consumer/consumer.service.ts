import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
@Injectable()
export class ConsumerService {
  async createConsumer(consumerDetails): Promise<any> {
    try {
      const dynamoDB = new AWS.DynamoDB.DocumentClient({
        region: process.env.REGION,
      });
      const params: AWS.DynamoDB.PutItemInput = {
        TableName: process.env.CONSUMER_CONFIG_TABLE,
        Item: {
          PK: consumerDetails?.apiKey,
          ...consumerDetails,
        },
      };
      await dynamoDB.put(params).promise();
    } catch (error) {
      console.error('Error putting item to consumer Table:', error);
      // Handle the error, maybe return an appropriate response
    }
  }
  async updateConsumer(consumerDetails): Promise<any> {
    try {
      const dynamoDB = new AWS.DynamoDB.DocumentClient({
        region: process.env.REGION,
      });
      const PK = consumerDetails?.apiKey;
      const { keys, update } = this.dynamodbUpdateRequest({
        keys: { PK },
        values: consumerDetails,
      });
      const params = {
        TableName: process.env.CONSUMER_CONFIG_TABLE,
        Key: keys,
        ...update,
      };
      await dynamoDB.update(params).promise();
    } catch (error) {
      console.error('Error updating item to consumer table:', error);
      // Handle the error, maybe return an appropriate response
    }
  }
  async deleteConsumer(apiKey) {
    await this.dynamodbDeleteRequest(apiKey, process.env.CONSUMER_CONFIG_TABLE);
  }
  async getConsumer(apiKey) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const params = {
      TableName: process.env.CONSUMER_CONFIG_TABLE,
      Key: {
        PK: apiKey,
      },
    };

    const data = await dynamoDB.get(params).promise();
    const retrievedItem = data.Item;
    return retrievedItem;
  }
  async dynamodbDeleteRequest(PK, tableName) {
    const deleteDocumentParams = {
      TableName: tableName,
      Key: {
        PK: PK,
      },
    };
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    await dynamoDB.delete(deleteDocumentParams).promise();
  }
  dynamodbUpdateRequest(params) {
    const { keys, values } = params;
    const sets = [];
    const removes = [];
    const expressionNames = {};
    const expValues = {};

    for (const [key, value] of Object.entries(values)) {
      expressionNames[`#${key}`] = key;
      if (value) {
        sets.push(`#${key} = :${key}`);
        expValues[`:${key}`] = value;
      } else {
        removes.push(`#${key}`);
      }
    }

    let expression = sets.length ? `SET ${sets.join(', ')}` : '';
    expression += removes.length ? ` REMOVE ${removes.join(', ')}` : '';
    return {
      keys,
      update: {
        UpdateExpression: expression,
        ExpressionAttributeNames: expressionNames,
        ExpressionAttributeValues: expValues,
      },
    };
  }
}
