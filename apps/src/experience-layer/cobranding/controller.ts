import { Controller, Query, Get } from '@nestjs/common';
import { CobrandingService } from './service';

@Controller('')
export class CobrandingServiceController {
  constructor(private readonly cobrandingService: CobrandingService) {}
  @Get('apphero/cobrandedurl')
  async generateLink(
    @Query('email') email: string,
    @Query('brand') brand: string,
  ) {
    return await this.cobrandingService.getCobranding(email, brand);
  }
}
