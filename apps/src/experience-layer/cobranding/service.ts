import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import axios from 'axios';
@Injectable()
export class CobrandingService {
  async getCobranding(email, brand): Promise<any> {
    try {
      const response = await axios.get(
        `${process.env.APPHERO_API}/unauth/apphero/cobrandedurl?email=${email}&brand=${brand}`,
      );
      return response?.data;
    } catch (error) {
      if (error.response.status === 404) {
        throw new NotFoundException({
          message: error.response.data.message,
          messageCode: 404,
        });
      }
      if (error.response.status === 400) {
        throw new BadRequestException({
          message: error.response.data.message,
          messageCode: 400,
        });
      }
      throw error.response.data;
    }
  }
}
