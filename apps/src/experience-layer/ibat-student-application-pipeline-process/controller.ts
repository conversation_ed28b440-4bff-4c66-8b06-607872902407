import {
  Controller,
  Body,
  Post,
  ValidationPipe,
  Res,
  Query,
  Get,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import { IBATStudentApplicationService } from './service';
import { StoreRequestPayloadToS3 } from './custom.decorator';
import { StudentApplicationListDto } from 'apps/src/experience-layer/ibat-student-application-pipeline-process/dto/ibat.student.dto';
import type { Response } from 'express';

@Controller('')
export class IBATStudentApplicationController {
  constructor(
    private readonly IBATStudentApplicationService: IBATStudentApplicationService,
  ) {}
  @Post('/ibat/studentapplications')
  async processIbatStudentApplications(
    @StoreRequestPayloadToS3()
    @Body()
    event: StudentApplicationListDto,
    @Res({ passthrough: true }) res: Response,
  ): Promise<any> {
    const maxRetries = 1;
    let retries = 0;

    if (!event.gg_portal_apps_json) {
      console.log('No gg_portal_apps_json found');
      return res
        .status(400)
        .json({ messageCode: 400, messageText: 'Bad Request Found' });
    }

    const { validObjects, responseData } =
      await this.IBATStudentApplicationService.validateObjects(event);
    try {
      await this.IBATStudentApplicationService.processApplications(
        Array.from(validObjects),
      );
      res.status(200).json(responseData);
    } catch (error) {
      if (retries < maxRetries) {
        retries++;
        console.log(`Retrying... Attempt ${retries}`);
        await this.IBATStudentApplicationService.processApplications(
          Array.from(validObjects),
        );
        console.log('Successfully retried applications');
      } else {
        console.log('Failed to process applications');
        res.status(500).json({ statusCode: 500, message: 'FAILED' });
      }
    }
  }
  @Get('/ibat/gusidvalidation')
  async applicationValidation(
    @Query('gusapplicationid') id: string,
    @Query('email') email: string,
    @Res({ passthrough: true }) res: Response,
    @Req() request: Request,
  ) {
    try {
      const response =
        await this.IBATStudentApplicationService.applicationValidation(
          id,
          email,
          request,
        );
      res.status(response.statusCode).json(response.message);
    } catch (error) {
      console.log(error);
      res.status(500).json({
        messageCode: 500,
        messageText: `The validation failed for this Gus Application ID : ${id}`,
      });
    }
  }
}
