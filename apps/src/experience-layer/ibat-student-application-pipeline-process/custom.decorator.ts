import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { S3 } from 'aws-sdk';
import { Request } from 'express';

export const StoreRequestPayloadToS3 = createParamDecorator(
  async (_, ctx: ExecutionContext) => {
    const req = ctx.switchToHttp().getRequest<Request>();
    const s3 = new S3();
    const requestBodyJson = JSON.stringify(req.body);
    console.log('Enered');
    const timestamp = Date.now();

    const formattedTimestamp = new Date(timestamp)
      .toISOString()
      .replace(/[-:]/g, '')
      .replace('T', '')
      .split('.')[0];

    const params = {
      Bucket: `ibat-student-application-storage-${process.env.STAGE}`,
      Key: `inbound/_${formattedTimestamp}.json`,
      Body: requestBodyJson,
      ContentType: 'application/json',
    };

    try {
      const data = await s3.upload(params).promise();
      console.log('Request body stored in S3:', data.Location);
    } catch (err) {
      console.error('Error uploading to S3:', err);
    }

    return req.body;
  },
);
