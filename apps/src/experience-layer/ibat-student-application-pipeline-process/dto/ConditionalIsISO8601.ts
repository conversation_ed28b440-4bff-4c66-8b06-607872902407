import {
  ValidationArguments,
  ValidatorConstraintInterface,
  ValidatorConstraint,
} from 'class-validator';

@ValidatorConstraint({ name: 'isValidDate', async: false })
export class ConditionalIsISO8601 implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    if (value === null) {
      return true;
    }
    return (
      typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2}$/) !== null
    );
  }

  defaultMessage(args: ValidationArguments) {
    return `${args.property} must be a valid ISO8601 date string`;
  }
}
