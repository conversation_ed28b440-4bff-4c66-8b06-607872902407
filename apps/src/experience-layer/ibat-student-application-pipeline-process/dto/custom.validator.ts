import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';

@ValidatorConstraint({ name: 'isValidYearMonth', async: false })
export class IsValidYearMonthConstraint
  implements ValidatorConstraintInterface
{
  validate(value: string, args: ValidationArguments) {
    // Check if the length of the string is exactly 6 characters
    if (value.length !== 6) return false;

    // Parse the year and month from the value
    const year = parseInt(value.substr(0, 4));
    const month = parseInt(value.substr(4, 2));

    // Check if year and month are within valid ranges
    return year >= 1900 && year <= 9999 && month >= 1 && month <= 12;
  }

  defaultMessage(args: ValidationArguments) {
    return `${args.property} must be in the format YYYYMM`;
  }
}
