import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>rray,
  <PERSON><PERSON>teNested,
  IsEmail,
  IsString,
  IsN<PERSON>ber,
  IsBoolean,
  IsOptional,
} from 'class-validator';
import { IsValidYearMonthConstraint } from './custom.validator';
import { Type } from 'class-transformer';
import { ConditionalIsISO8601 } from './ConditionalIsISO8601';
export class StudentApplicationDto {
  @IsString()
  student_number: string;

  @IsString()
  @IsOptional()
  gus_application_id: string;

  @IsString()
  ibat_application_id: string;

  @IsString()
  student_firstname: string;

  @IsString()
  student_lastname: string;

  @Validate(ConditionalIsISO8601)
  student_dob: string;

  @IsEmail()
  @IsString()
  student_email: string;

  @IsString()
  student_type: string;

  @IsString()
  student_nationality: string;

  @IsString()
  application_submitted_country: string;

  @Validate(ConditionalIsISO8601)
  application_submitted_date: string;

  @IsString()
  application_status: string;

  @Validate(ConditionalIsISO8601)
  application_status_date: string;

  @IsString()
  product_ids: string;

  @IsString()
  product_names: string;

  @IsString()
  product_categorys: string;

  @IsString()
  latest_offer_letter_issued: string;

  @Validate(ConditionalIsISO8601)
  date_offer_letter_issued: string;

  @IsNumber()
  product_price_gross: number;

  @IsNumber()
  amount_allocated: number;

  @IsBoolean()
  deposit_paid: boolean;

  @IsNumber()
  deposit_amount: number;

  @Validate(ConditionalIsISO8601)
  deposit_paid_date: string;

  @IsNumber()
  os_balance: number;

  @IsNumber()
  scholarship_discount_amount: number;

  @Validate(IsValidYearMonthConstraint)
  intake: string;

  @IsNumber()
  product_duration: number;

  @Validate(ConditionalIsISO8601)
  product_end_date: string;

  @IsString()
  latest_admission_status: string;

  @Validate(ConditionalIsISO8601)
  latest_admission_status_date: string;

  @IsString()
  latest_visa_status: string;

  @Validate(ConditionalIsISO8601)
  latest_visa_status_date: string;

  @IsString()
  enrolment_status: string;

  @Validate(ConditionalIsISO8601)
  enrolment_status_date: string;

  @IsBoolean()
  is_enrolled: boolean;

  @IsString()
  comments: string;

  @IsString()
  primary_sales_adviser: string;

  @IsString()
  secondary_sales_adviser: string;

  @IsString()
  agent_id: string;

  @IsString()
  agent_name: string;
}
export class StudentApplicationListDto {
  @IsArray({
    message: 'No applications found. Please provide at least one application.',
  })
  @ValidateNested({ each: true })
  @Type(() => StudentApplicationDto)
  gg_portal_apps_json: StudentApplicationDto[];
}
