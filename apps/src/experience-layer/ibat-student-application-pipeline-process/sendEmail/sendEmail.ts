import * as AWS from 'aws-sdk';

AWS.config.update({ region: process.env.REGION });

export const sendEmail: any = async (errorObjects) => {
  let currentDate = new Date().toLocaleString('en-IE', {
    timeZone: 'Europe/Dublin',
    hour12: true,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
  currentDate = currentDate.replaceAll('/', '-');

  const ses_mail =
    `From: ${process.env.SES_SENDER_EMAIL}\r\n` +
    `To: ${process.env.TO_EMAIL}\r\n` +
    `Cc: ${process.env.TO_CC}\r\n` +
    `Subject: IBAT Data Pipeline - Failed records notifier\r\n` +
    `MIME-Version: 1.0\r\n` +
    `Content-Type: multipart/mixed; boundary="NextPart"\r\n\r\n` +
    `--NextPart\r\n` +
    `Content-Type: text/plain\r\n\r\n` +
    `Hi,

We received your request to process the data at ${currentDate}. We found the attached records could not be processed as they were not adhering to the data types defined. Please verify the attachment and you can change and reprocess.

Regards,
GUS EIP Team\r\n\r\n` +
    `--NextPart\r\n` +
    `Content-Type: application/json\r\n` +
    `Content-Disposition: attachment; filename="invalidObjects.json"\r\n\r\n` +
    `${JSON.stringify(errorObjects, null, 2)}\r\n` +
    `--NextPart--`;

  const params = {
    RawMessage: {
      Data: ses_mail,
    },
  };

  try {
    const ses = new AWS.SES({ apiVersion: '2010-12-01' });
    await ses.sendRawEmail(params).promise();
    console.log('Email Sent');
  } catch (err) {
    console.log('[SERVER ERROR] Error in send email: ' + err);
    return {
      message: 'Error sending email',
    };
  }
  return { message: 'Email sent successfully' };
};
