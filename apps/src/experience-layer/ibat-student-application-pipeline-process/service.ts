import { Injectable } from '@nestjs/common';
import { GusSalesforceOpportunityService } from 'apps/src/system-layer/gus-salesforce/opportunity/opportunity.salesforce.service';

import { S3 } from 'aws-sdk';
import {
  StudentApplicationDto,
  StudentApplicationListDto,
} from './dto/ibat.student.dto';
import { validateSync } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { sendEmail } from './sendEmail/sendEmail';
import * as salesForceHelper from '../../../libs/utils/utils';
@Injectable()
export class IBATStudentApplicationService {
  constructor(
    private readonly gusSalesforceOpportunityService: GusSalesforceOpportunityService,
  ) {}
  async processApplications(details): Promise<any> {
    if (!details.length) {
      console.log('No valid Objects found');
      return { message: 'No valid Objects found' };
    }
    await Promise.all(
      details.map(async (application) => {
        for (const key in application) {
          if (typeof application[key] === 'number') {
            application[key] = String(application[key]);
          }
        }
      }),
    );

    console.log(
      'Processing applications to store valid objects in s3:',
      details.length,
    );

    const timestamp = Date.now();
    const formattedTimestamp = new Date(timestamp)
      .toISOString()
      .replace(/[-:]/g, '')
      .replace('T', '')
      .split('.')[0];

    try {
      const params = {
        Bucket: `ibat-student-application-storage-${process.env.STAGE}`,
        Key: `processed/_${formattedTimestamp}.jsonl`,
        Body: details.map((x) => JSON.stringify(x)).join('\n'),
        ContentType: 'application/x-jsonlines',
      };

      const s3 = new S3();
      const data = await s3.upload(params).promise();
      console.log('Response body stored in S3:', data.Location);
    } catch (error) {
      throw error;
    }
  }
  async applicationValidation(id, email, request): Promise<any> {
    try {
      const entityName = 'Opportunity';
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          entityName,
          request?.allowedEntities,
        );

      let opportunityByIdCondition = `WHERE Id='${id}'`;

      if (whereClause) {
        opportunityByIdCondition += ` AND ${whereClause}`;
      }
      const opportunity =
        await this.gusSalesforceOpportunityService.getOpportunities(
          fieldsRequired,
          opportunityByIdCondition,
        );

      if (opportunity.length === 0) {
        console.log(
          'GUS Application ID does not exist for comibation of given email and Id',
          email,
          id,
        );

        return {
          message: {
            messageCode: 404,
            messageText: 'GUS Application ID does not exist.',
          },
          statusCode: 404,
        };
      }

      const matched = opportunity[0]?.AccountEmail__c === email;

      const response = {
        message: {
          messageCode: matched ? 200 : 422,
          messageText: matched
            ? 'Match exists for the given GUS Application ID and Email Address.'
            : 'GUS Application ID is present, but the match does not exist for the given GUS Application ID and Email Address.',
        },
        statusCode: matched ? 200 : 422,
      };

      console.log(
        `Specific application's validation result - ${email}_${id} :`,
        JSON.stringify(response),
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  async validateObjects(event: StudentApplicationListDto): Promise<any> {
    const validObjects: any[] = [];
    const invalidObjects: any[] = [];

    console.log(
      'Received JSON applications and started the validations:',
      event.gg_portal_apps_json.length,
    );

    for (const application of event.gg_portal_apps_json) {
      const data = plainToInstance(StudentApplicationDto, application);
      const errors = validateSync(data);

      if (errors.length === 0) {
        validObjects.push(application);
      } else {
        invalidObjects.push({
          ...application,
          validation_errors: errors.map((err) => ({
            property: err.property,
            constraints: err.constraints,
          })),
        });
      }
    }

    const responseData = {
      'Total no of applications received': event?.gg_portal_apps_json.length,
      'No of applications successfully processed': validObjects.length,
      'No of applications failed to be processed': invalidObjects.length,
      'Import Timestamp': new Date().toISOString(),
    };

    console.log(
      'Finished JSON application validation process',
      JSON.stringify(responseData),
    );

    if (invalidObjects.length > 0) {
      console.log('Sending email for invalid objects');
      await this.sendErrorObjects(invalidObjects);
      console.log('Sucessfully sent an email for invalid objects');
    }

    return { validObjects, responseData };
  }

  async sendErrorObjects(errorObjects) {
    try {
      const formattedErrors = errorObjects.map((errorObj) => ({
        student_number: errorObj.student_number,
        gus_application_id: errorObj?.gus_application_id,
        ibat_application_id: errorObj.ibat_application_id,
        student_email: errorObj.student_email,
        validation_errors: errorObj.validation_errors,
      }));
      await sendEmail(formattedErrors);
    } catch (err) {
      console.log('Email sending error', err);
    }
  }
}
