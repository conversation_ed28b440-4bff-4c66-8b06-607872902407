import * as express from 'express';
import { urlencoded, json } from 'express';
import { createServer, proxy } from 'aws-serverless-express';
import { NestFactory } from '@nestjs/core';
import { eventContext } from 'aws-serverless-express/middleware';
import { ExpressAdapter } from '@nestjs/platform-express';
import { AppModule } from './app.module';

// eslint-disable-next-line @typescript-eslint/no-var-requires
import * as AWSXRay from 'aws-xray-sdk';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
// import { ValidationPipe } from '@nestjs/common';
// eslint-disable-next-line @typescript-eslint/no-var-requires
AWSXRay.captureHTTPsGlobal(require('http'));
// eslint-disable-next-line @typescript-eslint/no-var-requires
AWSXRay.captureHTTPsGlobal(require('https'));
AWSXRay.capturePromise();
// eslint-disable-next-line @typescript-eslint/no-var-requires
AWSXRay.captureAWS(require('aws-sdk'));

const binaryMimeTypes: string[] = [];

let cachedServer: express.Express;

async function bootstrap(): Promise<express.Express> {
  if (!cachedServer) {
    const expressApp = express();

    const app = await NestFactory.create(
      await AppModule.register(),
      new ExpressAdapter(expressApp),
    );
    app.use(json({ limit: '50mb' }));
    app.use(urlencoded({ extended: true, limit: '50mb' }));

    // app.useGlobalPipes(
    //   new ValidationPipe({
    //     whitelist: true,
    //   }),
    // );

    const config = new DocumentBuilder()
      .setTitle('gus-AppHero API')
      .setDescription('API for managing institution programmes with student')
      .setVersion('1.0')
      .addTag('gus-AppHero')
      .addApiKey({ type: 'apiKey', name: 'x-api-key', in: 'header' }, 'key') // Define API key
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('salesforce/api', app, document);

    // Enable CORS and other middlewares
    app.use(eventContext());
    app.enableCors({
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Amz-Date',
        'X-Api-Key',
        'X-Amz-Security-Token',
      ],
      origin: '*',
      methods: ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT'],
    });

    // Apply X-Ray middleware to capture traces for each incoming request
    // expressApp.use(
    //   AWSXRay.express.openSegment('dev-gusmiddleware-services/dev'),
    // );

    await app.init();

    // Close the X-Ray segment for the current request
    // expressApp.use(AWSXRay.express.closeSegment());

    cachedServer = createServer(expressApp, undefined, binaryMimeTypes);
  }

  return cachedServer;
}

module.exports.handler = async (event, context) => {
  cachedServer = await bootstrap();
  event = JSON.parse(JSON.stringify(event).replaceAll('/noauth/', '/'));
  return proxy(cachedServer, event, context, 'PROMISE').promise;
};

// import { NestFactory } from '@nestjs/core';
// import { AppModule } from './app.module';

// async function bootstrap() {
//   const app = await NestFactory.create(AppModule);

//   // enableCors
//   app.enableCors({
//     allowedHeaders: [
//       'Content-Type',
//       'Authorization',
//       'X-Amz-Date',
//       'X-Api-Key',
//       'X-Amz-Security-Token',
//     ],
//     origin: '*',
//     methods: ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT'],
//   });
//   await app.listen(3001);
// }

// bootstrap();
