import { Body, Controller, Post, Req, UsePipes, ValidationPipe } from '@nestjs/common';
import { Request } from 'express';
import * as uuid from 'uuid';
import { CertValidationProcessService } from './cert.validation.process.service';
import { ValidationDataInput } from './dtos/validationDataInput.dto';
import { ValidationResponse } from './dtos/validationResponse.dto';
import { plainToInstance } from 'class-transformer';

@Controller('gus')
@UsePipes(new ValidationPipe({ whitelist: true }))
export class CertValidationProcessController {
  constructor(
    private readonly certValidationProcessService: CertValidationProcessService,
  ) {}
  @Post('student/certificate/validation')
  async getDocValidationsStatus(
    @Body() validationData: ValidationDataInput,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const response =
        await this.certValidationProcessService.getDocValidationsStatus(
          validationData,
          request['requestId'] ?? uuid.v4(),
        );
      return plainToInstance(ValidationResponse, response, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      throw error;
    }
  }
}
