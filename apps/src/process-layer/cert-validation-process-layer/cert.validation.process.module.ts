import { LoggerModule } from '@gus-eip/loggers';
import { DynamicModule, Module } from '@nestjs/common';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { CertValidationProcessController } from './cert.validation.process.controller';
import { CertValidationProcessService } from './cert.validation.process.service';
import { CertValidationModule } from 'apps/src/system-layer/cert-validation/cert.validation.module';

@Module({})
export class CertValidationProcessModule {
  static async forRoot(
    parameterStoreCacheService: ParameterStoreCacheService,
  ): Promise<DynamicModule> {
    const logGroupname = (await parameterStoreCacheService.getParameter(
      'GUS_MIDDLEWARE_LOGGER_LOG_GROUP_NAME',
    )) as string;

    return {
      module: CertValidationProcessModule,
      imports: [
        LoggerModule.forRoot({
          region: process.env.REGION,
          logGroupName: logGroupname,
          options: 'GusMiddlewareLogger',
          teamWebhookUrl: '',
          isAlertNeeded: false,
        }),
        CertValidationModule.forRoot(parameterStoreCacheService),
      ],
      controllers: [CertValidationProcessController],
      providers: [CertValidationProcessService],
    };
  }
}
