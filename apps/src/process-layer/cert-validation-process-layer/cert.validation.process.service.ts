import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { DuolingoValidationService } from 'apps/src/system-layer/cert-validation/duolingoValidation/duolingoValidation.service';
import { validationSource } from './enum/validationSource';
import { IELTSValidationService } from 'apps/src/system-layer/cert-validation/IELTSValidation/IELTSValidation.service';

@Injectable()
export class CertValidationProcessService {
  constructor(
    @Inject('GusMiddlewareLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly loggerEnum: LoggerEnum,
    private readonly duolingoValidationService: DuolingoValidationService,
    private readonly IELTsValidationService: IELTSValidationService,
  ) {}

  private validationServiceMap = {
    [validationSource.duolingo]: this.duolingoValidationService,
    [validationSource.ielts]: this.IELTsValidationService,
  };

  async getDocValidationsStatus(validationData: any, correlationId: string) {
    try {
      let response;
      await this.cloudWatchLoggerService.log(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.SMART_APPLY,
        this.loggerEnum.Component.SMART_APPLY,
        this.loggerEnum.Event.FETCH_EN_TEST_CERT_VALIDATION_INITIATED,
        this.loggerEnum.UseCase.CERT_VALIDATION,
        validationData,
        null,
        'Initiate certificate validation',
        validationData.brand,
        validationData.applicantId,
        `gus-middleware-service/StudentCertValidation/${validationData.opportunityId}/${correlationId}`,
        'opportunityId',
        validationData.opportunityId,
      );

      if (
        Object.values(validationSource).includes(
          validationData.proficiencyQualification,
        )
      ) {
        response = await this.validationServiceMap[
          validationData.proficiencyQualification
        ].getDocValidationsStatus(validationData);
      } else {
        throw new BadRequestException(
          'The selected Proficiency Qualification is not supported for Validation.',
        );
      }

      if (response) {
        await this.cloudWatchLoggerService.log(
          correlationId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.SMART_APPLY,
          this.loggerEnum.Component.SMART_APPLY,
          this.loggerEnum.Event.GET_EN_TEST_CERT_VALIDATION,
          this.loggerEnum.UseCase.CERT_VALIDATION,
          validationData,
          response,
          'Get Validation Data',
          validationData.brand,
          validationData.applicantId,
          `gus-middleware-service/StudentCertValidation/${validationData.opportunityId}/${correlationId}`,
          'opportunityId',
          validationData.opportunityId,
        );
      }

      return response;
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.SMART_APPLY,
        this.loggerEnum.Component.SMART_APPLY,
        this.loggerEnum.Event.GET_EN_TEST_CERT_VALIDATION,
        this.loggerEnum.UseCase.CERT_VALIDATION,
        validationData,
        null,
        error,
        validationData.brand,
        validationData.applicantId,
        `gus-middleware-service/StudentCertValidation/${validationData.opportunityId}/${correlationId}`,
        'opportunityId',
        validationData.opportunityId,
      );
      throw error.response ?? error;
    }
  }
}
