import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class ValidationDataInput {
  @IsString()
  @IsNotEmpty()
  proficiencyQualification: string;

  @IsString()
  @IsNotEmpty()
  opportunityId: string;

  @IsString()
  @IsNotEmpty()
  TrfNo: string;

  @IsString()
  @IsOptional()
  DOB: string;

  @IsString()
  @IsNotEmpty()
  brand: string;

  @IsString()
  @IsOptional()
  applicantId: string;

  @IsString()
  @IsOptional()
  testDate: string;

  @IsString()
  @IsOptional()
  testScore: string;

  @IsString()
  @IsOptional()
  testLink: string;

  @IsString()
  @IsOptional()
  status: string;
}
