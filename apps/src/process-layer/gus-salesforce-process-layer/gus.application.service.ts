import { Injectable, Inject, HttpException, HttpStatus } from '@nestjs/common';
import { GusSalesforceOpportunityService } from 'apps/src/system-layer/gus-salesforce/opportunity/opportunity.salesforce.service';
import * as salesForceHelper from '../../../libs/utils/utils';
import { GusSalesforceAccountService } from 'apps/src/system-layer/gus-salesforce/account/account.service';
import { GusSalesforceLeadService } from 'apps/src/system-layer/gus-salesforce/lead/lead.service';
import { GusSalesforceApplicationService } from 'apps/src/system-layer/gus-salesforce/application/application.service';
import { UtilitiesService } from 'apps/src/system-layer/gus-salesforce/utilities.service';
import { GusSalesforceCompositeService } from 'apps/src/system-layer/gus-salesforce/composite/composite.service';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import { GusSalesforcePricebookEntryService } from 'apps/src/system-layer/gus-salesforce/pricebookEntry/pricebookEntry.service';
import { GusSalesforceOpportunityLineItemService } from 'apps/src/system-layer/gus-salesforce/opportunityLineItem/opportunityLineItem.salesforce.service';
import { GusSalesforceIndividual } from 'apps/src/system-layer/gus-salesforce/individual/individual.service';
import { CustomException } from 'apps/src/common/customException/customexception.service';
import { ObjectConfig } from 'apps/src/system-layer/gus-salesforce/objectConfig';
import { GusSalesforceSurveyService } from 'apps/src/system-layer/gus-salesforce/survey/survey.service';
import { GusSalesforceAccountAgeContractService } from 'apps/src/system-layer/gus-salesforce/accountAgeContract/accountagecontract.service';
import { GusSalesforceIdentityInfoRecordService } from 'apps/src/system-layer/gus-salesforce/identityInfoRecord/identityInfoRecord.service';
import { GusSalesforceUserService } from 'apps/src/system-layer/gus-salesforce/user/user.service';
@Injectable()
export class GusApplicationProcessService {
  constructor(
    @Inject('CloudWatchLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly gusSalesforceOpportunityService: GusSalesforceOpportunityService,
    private readonly gusSalesforceAccountService: GusSalesforceAccountService,
    private readonly gusSalesforceLeadService: GusSalesforceLeadService,
    private readonly gusSalesforceApplicationService: GusSalesforceApplicationService,
    private readonly utilitiesService: UtilitiesService,
    private readonly gusSalesforceCompositeService: GusSalesforceCompositeService,
    private readonly gusSalesforceOpportunityLineItemService: GusSalesforceOpportunityLineItemService,
    private readonly gusSalesforcePricebookEntryService: GusSalesforcePricebookEntryService,
    private readonly gusSalesforceIndividual: GusSalesforceIndividual,
    private readonly gusSalesforceIdentityInfoRecordService: GusSalesforceIdentityInfoRecordService,
    private readonly loggerEnum: LoggerEnum,
    private readonly gusSalesforceSurveyService: GusSalesforceSurveyService,
    private readonly gusSalesforceAccountAgeContractService: GusSalesforceAccountAgeContractService,
    private readonly gusSalesforceUserService: GusSalesforceUserService,
  ) {}

  async log(
    requestId,
    timestamp,
    component,
    source,
    destination,
    event,
    usecase,
    sourcePayload,
    destinationPayload,
    logMessage,
    brand,
    secondaryKey,
    destinationObjectType,
    destinationObjectId,
    applicationId,
    response?,
    sourceObjectType?,
    sourceObjectId?,
  ) {
    await this.cloudWatchLoggerService.log(
      requestId,
      timestamp,
      component,
      source,
      destination,
      event,
      usecase,
      sourcePayload,
      destinationPayload,
      logMessage,
      brand,
      secondaryKey,
      `gus-middleware-service/${applicationId}/${requestId}`,
      'Application_Form_Id__c',
      applicationId,
      destinationObjectType,
      destinationObjectId,
      sourceObjectType || '',
      sourceObjectId || '',
      response,
    );
  }
  async getApplicationDetailsByOpportunityAndApplicationId(
    opportunityId: string,
    request,
    scenario?,
  ): Promise<any> {
    let response: any = {};

    const allowedObjectDetails = request.allowedEntities.AllowedEntities.find(
      (obj) => obj.entityName === 'getapplicationsdetails',
    );

    const opportunityDetails = await this.getOpportunityDetails(
      opportunityId,
      request,
    );

    response['Opportunity'] = opportunityDetails;
    const {
      AccountId: accountId,
      AccountEmail__c: email,
      Applications__c: applicationId,
    } = opportunityDetails;

    let objectToGetDetails = [
      { object: 'Account', condition: `Id = '${accountId}' limit 200` },
      {
        object: 'Application__c',
        condition: `Id = '${applicationId}' limit 200`,
      },
      { object: 'Lead', condition: `Email = '${email}' limit 200` },
      {
        object: 'IdentityInfoRecord__c',
        condition: `Opportunity__c = '${opportunityId}' and Account__c = '${accountId}' limit 200`,
      },
      {
        object: 'OpportunityFile__c',
        condition: `Opportunity__c = '${opportunityId}' limit 200`,
      },
      {
        object: 'EducationHistoryRecord__c',
        condition: `Opportunity__c = '${opportunityId}' and Account__c = '${accountId}' limit 200`,
      },
      {
        object: 'WorkHistoryRecord__c',
        condition: `Opportunity__c = '${opportunityId}' and Account__c = '${accountId}' limit 200`,
      },
      {
        object: 'Connection__c',
        condition: `Opportunity_c__c = '${opportunityId}' and Account__c = '${accountId}' limit 200`,
      },
      {
        object: 'LanguageProficiencyRecord__c',
        condition: `Opportunity__c = '${opportunityId}' and Account__c = '${accountId}' limit 200`,
      },
      {
        object: 'OpportunityLineItem',
        condition: `OpportunityId = '${opportunityId}' limit 1`,
        extraFields: 'Product2.Duration__c',
      },
      {
        object: 'Visa_Application__c',
        condition: `Opportunity__c = '${opportunityId}' and Account__c = '${accountId}' limit 200`,
      },
    ];

    if (allowedObjectDetails) {
      const allowedObjects = allowedObjectDetails.objects;
      objectToGetDetails = objectToGetDetails.filter((obj) =>
        allowedObjects.includes(obj.object),
      );
    }

    const MAX_COMPOSITE_REQUEST_SIZE = 4;
    const compositeResponses: any[] = [];

    const totalBatches = Math.ceil(
      objectToGetDetails.length / MAX_COMPOSITE_REQUEST_SIZE,
    );
    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const batch = objectToGetDetails.slice(
        batchIndex * MAX_COMPOSITE_REQUEST_SIZE,
        (batchIndex + 1) * MAX_COMPOSITE_REQUEST_SIZE,
      );

      const compositeQuery = await Promise.all(
        batch.map((detail) => {
          const defaultFields = 'FIELDS(ALL)';
          const fullFields = detail?.extraFields
            ? `${defaultFields}, ${detail.extraFields}`
            : defaultFields;

          return this.buildQueryCompositeRequest(
            fullFields,
            detail.condition,
            detail.object,
          );
        }),
      );
      this.logRequest(
        opportunityId,
        request,
        compositeQuery,
        'initiated',
        scenario,
      );
      const compositeResponse =
        await this.gusSalesforceCompositeService.compositeRequest(
          compositeQuery,
        );
      this.logRequest(
        opportunityId,
        request,
        compositeQuery,
        'completed',
        scenario,
      );

      compositeResponses.push(compositeResponse);
    }

    const combinedCompositeResponse = compositeResponses.reduce(
      (acc, curr) => {
        acc.compositeResponse.push(...curr.compositeResponse);
        return acc;
      },
      { compositeResponse: [] },
    );

    return {
      ...response,
      ...this.transformCompositeResponse(combinedCompositeResponse),
    };
  }

  // Helper function for logging requests
  private async logRequest(
    opportunityId,
    request,
    compositeQuery,
    status,
    usecase?,
  ) {
    await this.utilitiesService.recordGUSLogs(
      opportunityId,
      request,
      this.loggerEnum.Event.GET_APPLICATION_DETAILS_BY_OPPID,
      `get application details by opportunity id ${status}`,
      opportunityId,
      'OPPORTUNITY_LEAD_ACCOUNT',
      usecase,
      compositeQuery,
      null,
    );
  }

  transformCompositeResponse(response: any): any {
    const result = {};

    response.compositeResponse?.forEach((item) => {
      const { body, referenceId } = item;

      if (Array.isArray(body)) {
        result[referenceId] = body;
      } else if (
        body.totalSize >= 1 &&
        (referenceId === 'EducationHistoryRecord__c' ||
          referenceId === 'WorkHistoryRecord__c' ||
          referenceId === 'LanguageProficiencyRecord__c')
      ) {
        result[referenceId] = body.records;
      } else {
        result[referenceId] = body.records[0];
      }
    });

    return result;
  }
  async buildQueryCompositeRequest(fields, condition, object) {
    return {
      method: 'GET',
      url: `/services/data/${process.env.SALESFORCE_API_VERSION}/query?q=select ${fields} from ${object} where ${condition} `,
      referenceId: object,
    };
  }
  async getOpportunityDetails(opportunityId, request): Promise<any> {
    const entityName = 'Opportunity';
    const entityOperation = 'opportunityDetails';

    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );

    console.log('Opp ->', fieldsRequired, whereClause);
    let opportunityByApplicationIdCondition = `WHERE Id ='${opportunityId}'`;

    if (whereClause) {
      opportunityByApplicationIdCondition += ` AND ${whereClause}`;
    }
    try {
      await this.recordSalesforceRequest('Opportunity', 'GET', request, true);
      const opportunities =
        await this.gusSalesforceOpportunityService.getOpportunities(
          fieldsRequired,
          opportunityByApplicationIdCondition,
        );
      await this.recordSalesforceRequest('Opportunity', 'GET', request, false);
      return opportunities?.[0];
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GETOPPORTUNITY_BY_APPLICATIONID_FAILED',
        error.message ? error.message : error,
        `${fieldsRequired} ${opportunityByApplicationIdCondition}`,
        this.loggerEnum.Event.SUBMIT_APPLICATION,
      );
    }
  }
  async logApplicationEvent({
    request,
    applicationDetails,
    logMessage,
    event,
    sourcePayload = {},
    destinationPayload = {},
    secondaryKey = '',
    destinationObject = '',
    destinationObjectId = '',
    response = {},
    usecase = '',
  }) {
    await this.log(
      applicationDetails?.requestId,
      new Date().toISOString(),
      this.loggerEnum.Component.GUS_EIP_SERVICE,
      this.loggerEnum.Component.OAP_HANDLERS,
      this.loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.loggerEnum.UseCase[`${applicationDetails?.sectionLabel}`] || usecase,
      sourcePayload,
      destinationPayload,
      logMessage,
      request?.allowedEntities?.brand,
      secondaryKey,
      destinationObject,
      destinationObjectId,
      applicationDetails?.applicationId,
      response,
    );
  }
  async handleSurveyCreation(applicationDetails, request): Promise<void> {
    try {
      await this.log(
        applicationDetails.requestId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Component.GUS_SALESFORCE,
        this.loggerEnum.Event.SURVEY_CREATION_INITIATED,
        this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        'Survey creation initiated',
        request?.allowedEntities?.brand,
        applicationDetails?.email || '',
        'Survey',
        applicationDetails.applicationId,
        applicationDetails.applicationId,
      );

      const surveyResponse = await this.gusSalesforceSurveyService.createSurvey(
        applicationDetails['Survey_AP__c'],
      );

      await this.log(
        applicationDetails.requestId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Component.GUS_SALESFORCE,
        this.loggerEnum.Event.SURVEY_CREATION_COMPLETED,
        this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        surveyResponse,
        'Survey creation completed successfully',
        request?.allowedEntities?.brand,
        applicationDetails?.email || '',
        'Survey',
        applicationDetails.applicationId,
        applicationDetails.applicationId,
      );
    } catch (error) {
      await this.log(
        applicationDetails.requestId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Component.GUS_SALESFORCE,
        this.loggerEnum.Event.SURVEY_CREATION_FAILED,
        this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        JSON.stringify(error),
        'Survey creation failed',
        request?.allowedEntities?.brand,
        applicationDetails?.email || '',
        'Survey',
        applicationDetails.applicationId,
        applicationDetails.applicationId,
      );
    }
  }

  async persistUlawApplication(applicationDetails, request): Promise<any> {
    try {
      await this.log(
        applicationDetails.requestId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Component.GUS_SALESFORCE,
        this.loggerEnum.Event.SAVE_APPLICATION_INITIATED,
        this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        'Save ULaw application initiated',
        request?.allowedEntities?.brand,
        applicationDetails?.email || '',
        'Opportunity_Application_Account',
        applicationDetails.applicationId,
        applicationDetails.applicationId,
      );
      if (applicationDetails?.Application__c) {
        applicationDetails = {
          ...applicationDetails,
          Application__c: {
            ...(applicationDetails.Application__c || {}),
            Id: await this.getApplicationByApplicationId(
              applicationDetails.Opportunity.ApplicationFormId__c,
              request,
            ),
          },
        };
      }
      let response;
      const applicationId = applicationDetails?.applicationId;

      // Check for existing Opportunity
      const opportunity = await this.getOpportunityIdByApplicationId(
        applicationId,
        request,
      );

      if (opportunity) {
        await this.log(
          applicationDetails.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Component.GUS_SALESFORCE,
          this.loggerEnum.Event.UPDATE_APPLICATION_INITIATED,
          this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
          applicationDetails,
          applicationDetails,
          'Update application initiated',
          request?.allowedEntities?.brand,
          applicationDetails?.email || '',
          'Opportunity_Application_Account',
          applicationId,
          applicationId,
        );
        const personAccountId = await this.getPersonAccountId(
          applicationDetails.email,
          request,
        );
        if (!personAccountId) {
          throw new Error(
            `No person account found for email: ${applicationDetails.email}`,
          );
        }
        applicationDetails['accountId'] = personAccountId;
        applicationDetails['opportunityId'] = opportunity?.Id;
        applicationDetails['salApplicationId'] = opportunity?.Applications__c;
        console.log('applicationDetails -->', applicationDetails);
        if (applicationDetails && applicationDetails.Lead) {
          delete applicationDetails.Lead;
        }
        await this.updateApplication(
          applicationDetails,
          request,
          applicationId,
        );
      } else {
        const personAccountId = await this.getPersonAccountId(
          applicationDetails.email,
          request,
        );
        if (personAccountId) {
          delete applicationDetails.Account?.OwnerId;

          if (
            this.utilitiesService.checkRequiredField(
              applicationDetails['Opportunity'],
              'Opportunity',
            )
          ) {
            await this.log(
              applicationDetails.requestId,
              new Date().toISOString(),
              this.loggerEnum.Component.GUS_EIP_SERVICE,
              this.loggerEnum.Component.OAP_HANDLERS,
              this.loggerEnum.Component.GUS_SALESFORCE,
              this.loggerEnum.Event.CREATE_OPPORTUNITY_INITIATED,
              this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
              applicationDetails,
              applicationDetails,
              'Create opportunity initiated',
              request?.allowedEntities?.brand,
              applicationDetails?.email || '',
              'Opportunity_Application_Account',
              applicationId,
              applicationId,
            );
            console.log('create opportunity initiated');
            response = await this.createOpportunity(
              applicationDetails,
              personAccountId,
              request,
            );
          }
        } else {
          const lead = await this.getLeadByEmail(
            applicationDetails.email,
            request,
          );
          if (lead?.Id) {
            await this.log(
              applicationDetails.requestId,
              new Date().toISOString(),
              this.loggerEnum.Component.GUS_EIP_SERVICE,
              this.loggerEnum.Component.OAP_HANDLERS,
              this.loggerEnum.Component.GUS_SALESFORCE,
              this.loggerEnum.Event.CREATE_APPLICATION_WITHOUT_LEAD,
              this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
              applicationDetails,
              applicationDetails,
              'Create application without lead',
              request?.allowedEntities?.brand,
              applicationDetails?.email || '',
              'Opportunity_Application_Account',
              applicationId,
              applicationId,
            );
            applicationDetails.leadId = lead?.Id;
            applicationDetails.opportunityConversionId =
              lead?.Opportunity_Conversion_Id__c;
            console.log('create application with lead initiated');
            if (applicationDetails.Opportunity) {
              applicationDetails.Opportunity.AccountId = '@{Account.id}';
            }
            response = await this.createApplication(
              applicationDetails,
              request,
              ['Lead', 'Application__c'],
            );
          } else {
            console.log('lead not found');
            throw new Error(
              `No lead found for email: ${applicationDetails.email}`,
            );
          }
        }
      }
      return response;
    } catch (error) {
      const brand = request?.allowedEntities?.brand;
      if (error instanceof CustomException) {
        const { errorCode, errorDetails, request, eventName } =
          error?.['response'];
        this.cloudWatchLoggerService.error(
          applicationDetails.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Component.GUS_SALESFORCE,
          eventName,
          this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
          applicationDetails,
          applicationDetails,
          JSON.stringify(error?.['response']),
          brand,
          applicationDetails?.email || '',
          `gus-middleware-service/${applicationDetails.applicationId}/${applicationDetails.requestId}`,
          'Application_Form_Id__c',
          applicationDetails?.applicationId,
          'Opportunity_Application_Account',
          applicationDetails?.applicationId,
        );
      }
      throw error;
    }
  }

  async persistApplication(applicationDetails, request): Promise<any> {
    try {
      await this.log(
        applicationDetails.requestId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Component.GUS_SALESFORCE,
        this.loggerEnum.Event.SAVE_APPLICATION_INITIATED,
        this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        applicationDetails,
        applicationDetails,
        'Save application initiated',
        request?.allowedEntities?.brand,
        applicationDetails?.email || '',
        'Opportunity_Application_Account',
        applicationDetails.applicationId,
        applicationDetails.applicationId,
      );

      // Check if opportunity has already been submitted
      const opportunity = await this.getOpportunityIdByApplicationId(
        applicationDetails.applicationId,
        request,
      );

      if (opportunity?.ApplicationSubmitted__c) {
        this.cloudWatchLoggerService.error(
          applicationDetails.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Component.GUS_SALESFORCE,
          this.loggerEnum.Event.SUBMIT_APPLICATION,
          this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
          applicationDetails,
          applicationDetails,
          `Opportunity was already submitted ${applicationDetails?.email}-${opportunity?.Id}`,
          opportunity?.BusinessUnitFilter__c,
          applicationDetails?.email || '',
          `gus-middleware-service/${applicationDetails.applicationId}/${applicationDetails.requestId}`,
          'Application_Form_Id__c',
          applicationDetails.applicationId,
          'Opportunity_Application_Account',
          applicationDetails?.applicationId,
        );
        console.log(
          `Opportunity was already submitted ${applicationDetails?.email}-${opportunity?.Id}`,
        );
        throw new HttpException(
          `Opportunity was already submitted ${applicationDetails?.email}-${opportunity?.Id}`,
          HttpStatus.CONFLICT,
        );
      }

      const applicationId = applicationDetails?.applicationId;

      if ('Survey_AP__c' in applicationDetails) {
        await this.handleSurveyCreation(applicationDetails, request);
      }

      if (opportunity) {
        if (!opportunity?.Owner?.IsActive && applicationDetails.Opportunity) {
          applicationDetails.OpportunityOwnerIsNotActive = true;
          applicationDetails.Opportunity.OwnerId =
            (await this.getDefaultOwnerId(
              request?.allowedEntities?.brand,
              'AGENT',
            )) || opportunity?.OwnerId;
        }
        await this.log(
          applicationDetails.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Component.GUS_SALESFORCE,
          this.loggerEnum.Event.UPDATE_APPLICATION_INITIATED,
          this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
          applicationDetails,
          applicationDetails,
          'Update application initiated',
          request?.allowedEntities?.brand,
          applicationDetails?.email || '',
          'Opportunity_Application_Account',
          applicationId,
          applicationId,
        );
        const personAccountId = await this.getPersonAccountId(
          applicationDetails.email,
          request,
        );
        applicationDetails['accountId'] = personAccountId;
        applicationDetails['opportunityId'] = opportunity?.Id;
        applicationDetails['salApplicationId'] = opportunity?.Applications__c;
        console.log('applicationDetails -->', applicationDetails);
        if (applicationDetails && applicationDetails.Lead) {
          delete applicationDetails.Lead;
        }
        await this.updateApplication(
          applicationDetails,
          request,
          applicationId,
        );
      } else {
        if (applicationDetails?.Opportunity) {
          const checkIsOwnerActive = await this.checkIsOwnerActive(
            applicationDetails?.Opportunity?.OwnerId,
          );
          if (!checkIsOwnerActive) {
            const defaultOwnerId =
              (await this.getDefaultOwnerId(
                request?.allowedEntities?.brand,
                'AGENT',
              )) || applicationDetails?.Opportunity?.OwnerId;
            const objectsToUpdate = [
              'Opportunity',
              'Account',
              'Lead',
              'Application__c',
            ];
            objectsToUpdate.forEach((objName) => {
              if (applicationDetails[objName]) {
                applicationDetails[objName].OwnerId = defaultOwnerId;
              }
            });
          }
        }
        const personAccountId = await this.getPersonAccountId(
          applicationDetails.email,
          request,
        );
        if (personAccountId) {
          delete applicationDetails.Account?.OwnerId;

          // Fetch the Lead by email to keep it in sync with the person account
          const lead = await this.getLeadByEmail(
            applicationDetails.email,
            request,
          );

          // If Lead exists, update its status and terms & conditions if needed
          if (lead?.Id) {
            await this.updateLeadIfNeeded(
              lead,
              applicationDetails.Lead,
              request,
              applicationDetails,
              applicationDetails?.Lead?.OwnerId,
            );
          }

          if (
            this.utilitiesService.checkRequiredField(
              applicationDetails['Opportunity'],
              'Opportunity',
            )
          ) {
            await this.log(
              applicationDetails.requestId,
              new Date().toISOString(),
              this.loggerEnum.Component.GUS_EIP_SERVICE,
              this.loggerEnum.Component.OAP_HANDLERS,
              this.loggerEnum.Component.GUS_SALESFORCE,
              this.loggerEnum.Event.CREATE_OPPORTUNITY_INITIATED,
              this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
              applicationDetails,
              applicationDetails,
              'Create opportunity initiated',
              request?.allowedEntities?.brand,
              applicationDetails?.email || '',
              'Opportunity_Application_Account',
              applicationId,
              applicationId,
            );
            console.log('create opportunity initiated');
            await this.createOpportunity(
              applicationDetails,
              personAccountId,
              request,
            );
          }
        } else {
          const lead = await this.getLeadByEmail(
            applicationDetails.email,
            request,
          );
          if (lead?.Id) {
            await this.updateLeadIfNeeded(
              lead,
              applicationDetails?.Lead,
              request,
              applicationDetails,
              applicationDetails?.Lead?.OwnerId,
            );
            await this.log(
              applicationDetails.requestId,
              new Date().toISOString(),
              this.loggerEnum.Component.GUS_EIP_SERVICE,
              this.loggerEnum.Component.OAP_HANDLERS,
              this.loggerEnum.Component.GUS_SALESFORCE,
              this.loggerEnum.Event.CREATE_APPLICATION_WITHOUT_LEAD,
              this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
              applicationDetails,
              applicationDetails,
              'Create application without lead',
              request?.allowedEntities?.brand,
              applicationDetails?.email || '',
              'Opportunity_Application_Account',
              applicationId,
              applicationId,
            );
            applicationDetails.leadId = lead?.Id;
            applicationDetails.opportunityConversionId =
              lead?.Opportunity_Conversion_Id__c;
            console.log('create application with lead initiated');
            if (applicationDetails.Opportunity) {
              applicationDetails.Opportunity.AccountId = '@{Account.id}';
            }
            await this.createApplication(applicationDetails, request, ['Lead']);
          } else {
            await this.log(
              applicationDetails.requestId,
              new Date().toISOString(),
              this.loggerEnum.Component.GUS_EIP_SERVICE,
              this.loggerEnum.Component.OAP_HANDLERS,
              this.loggerEnum.Component.GUS_SALESFORCE,
              this.loggerEnum.Event.CREATE_APPLICATION_INITIATED,
              this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
              applicationDetails,
              applicationDetails,
              'Create application in salesforce',
              request?.allowedEntities?.brand,
              applicationDetails?.email || '',
              'Opportunity_Application_Account',
              applicationId,
              applicationId,
            );
            console.log('create application without lead initiated');
            if (applicationDetails.Opportunity) {
              applicationDetails.Opportunity.AccountId = '@{Account.id}';
            }
            await this.createApplication(applicationDetails, request);
          }
        }
      }
    } catch (error) {
      const brand = request?.allowedEntities?.brand;
      if (error instanceof CustomException) {
        const { errorCode, errorDetails, request, eventName } =
          error?.['response'];
        this.cloudWatchLoggerService.error(
          applicationDetails.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Component.GUS_SALESFORCE,
          eventName,
          this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
          applicationDetails,
          applicationDetails,
          JSON.stringify(error?.['response']),
          brand,
          applicationDetails?.email || '',
          `gus-middleware-service/${applicationDetails.applicationId}/${applicationDetails.requestId}`,
          'Application_Form_Id__c',
          applicationDetails?.applicationId,
          'Opportunity_Application_Account',
          applicationDetails?.applicationId,
        );
      }
      throw error;
    }
  }
  async getDefaultOwnerId(brand, type): Promise<any> {
    switch (`${brand}_${type}`) {
      case 'UCW_AGENT':
        return '0050X000008VUj8QAG';
      case 'UCW_DIRECT':
        return '0056700000CCLvpAAH';
      default:
        return null;
    }
  }
  async checkIsOwnerActive(userId): Promise<any> {
    if (userId) {
      const user = await this.gusSalesforceUserService.getUsers(
        'IsActive',
        `where Id = '${userId}'`,
      );
      return user?.IsActive;
    }
    return true;
  }

  async persistStudentApplication(applicationDetails, request): Promise<any> {
    try {
      await this.logApplicationEvent({
        request,
        applicationDetails,
        logMessage: 'INFO: Student application initiated',
        event: this.loggerEnum.Event.STUD_APPLICATION_INITIATED,
        sourcePayload: applicationDetails,
      });

      // Check if opportunity has already been submitted
      const opportunity = await this.getOpportunityIdByApplicationId(
        applicationDetails.applicationId,
        request,
      );

      console.log('opportunity', opportunity);

      if (opportunity?.ApplicationSubmitted__c) {
        this.cloudWatchLoggerService.error(
          applicationDetails.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Component.GUS_SALESFORCE,
          this.loggerEnum.Event.SUBMIT_APPLICATION,
          this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
          applicationDetails,
          applicationDetails,
          `Opportunity was already submitted ${applicationDetails?.email}-${opportunity?.Id}`,
          opportunity?.BusinessUnitFilter__c,
          applicationDetails?.email || '',
          `gus-middleware-service/${applicationDetails.applicationId}/${applicationDetails.requestId}`,
          'Application_Form_Id__c',
          applicationDetails.applicationId,
          'Opportunity_Application_Account',
          applicationDetails?.applicationId,
        );
        console.log(
          `Opportunity was already submitted ${applicationDetails?.email}-${opportunity?.Id}`,
        );
        throw new HttpException(
          `Opportunity was already submitted ${applicationDetails?.email}-${opportunity?.Id}`,
          HttpStatus.CONFLICT,
        );
      }

      if ('Survey_AP__c' in applicationDetails) {
        await this.handleSurveyCreation(applicationDetails, request);
      }

      await this.logApplicationEvent({
        request,
        applicationDetails,
        logMessage: 'EVENT COMPLETED: Fetched opportunity by application ID',
        event: this.loggerEnum.Event.FETCH_OPPORTUNITY_COMPLETED,
        sourcePayload: applicationDetails.applicationId,
        destinationPayload: opportunity,
        destinationObject: 'Opportunity',
        destinationObjectId: opportunity?.Id || 'N/A',
        response: opportunity,
        usecase: 'GET',
      });
      const personAccount = await this.getPersonAccount(
        applicationDetails.email,
        request,
      );
      await this.logApplicationEvent({
        request,
        applicationDetails,
        logMessage: 'EVENT COMPLETED: Fetched person account by email',
        event: this.loggerEnum.Event.FETCH_PERSON_ACCOUNT_COMPLETED,
        sourcePayload: applicationDetails.email,
        destinationPayload: {},
        destinationObject: 'Account',
        destinationObjectId: personAccount?.Id || 'N/A',
        response: personAccount,
        usecase: 'GET',
      });
      if (opportunity) {
        await this.updateApplicationWithOpportunity(
          opportunity,
          applicationDetails,
          request,
          personAccount,
        );
      } else {
        if (personAccount?.Id) {
          await this.createApplicationWithAccount(
            applicationDetails,
            request,
            personAccount,
          );
        } else {
          let removeObjectList = [];
          if (
            !this.utilitiesService.checkRequiredField(
              applicationDetails['Lead'],
              'Lead',
              'utmParamsRequiredFields',
            )
          ) {
            removeObjectList = ['Lead'];
          }
          const lead = await this.getLeadByEmail(
            applicationDetails.email,
            request,
          );
          console.log('lead details -->', lead);
          if (
            lead?.Id &&
            lead?.Owner?.Type === 'User' &&
            lead?.Owner?.IsActive
          ) {
            await this.createApplicationWithActiveLead(
              lead,
              applicationDetails,
              request,
              removeObjectList,
            );
            return { activeLead: true };
          } else {
            if (applicationDetails?.sectionLabel === 'PROGRAM_FILTER') {
              await this.createLead(applicationDetails, request);
            }
            return { activeLead: false };
          }
        }
      }
    } catch (error) {
      const brand = request?.allowedEntities?.brand;
      if (error instanceof CustomException) {
        const { errorCode, errorDetails, request, eventName } =
          error?.['response'];
        this.cloudWatchLoggerService.error(
          applicationDetails.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Component.GUS_SALESFORCE,
          eventName,
          this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
          applicationDetails,
          applicationDetails,
          JSON.stringify(error?.['response']),
          brand,
          applicationDetails?.email || '',
          `gus-middleware-service/${applicationDetails.applicationId}/${applicationDetails.requestId}`,
          'Application_Form_Id__c',
          applicationDetails.applicationId,
          'Opportunity_Application_Account',
          applicationDetails?.applicationId,
        );
      }
      throw error;
    }
  }
  async createLead(applicationDetails, request) {
    if (applicationDetails.Lead) {
      applicationDetails.Lead.OwnerId = process.env.LEAD_SALES_QUEUE_ID;
    }
    try {
      await this.logApplicationEvent({
        request,
        applicationDetails,
        logMessage: 'EVENT INIATED: Lead creation initiated',
        event: this.loggerEnum.Event.LEAD_CREATION_INITIATED,
        destinationPayload: applicationDetails.Lead,
        destinationObject: 'Lead',
        destinationObjectId: '',
      });
      const leadId = await this.gusSalesforceLeadService.createLead(
        applicationDetails.Lead,
      );
      await this.logApplicationEvent({
        request,
        applicationDetails,
        logMessage: 'EVENT COMPLETED: Lead creation completed',
        event: this.loggerEnum.Event.LEAD_CREATION_COMPLETED,
        destinationPayload: applicationDetails.Lead,
        destinationObject: 'Lead',
        destinationObjectId: leadId || '',
      });
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'LEAD_CREATION_FAILED',
        error.message ? error.message : error,
        applicationDetails.Lead,
        'CREATE_LEAD',
      );
    }
  }
  async updateLeadIfNotConverted(lead: any, request: any) {
    try {
      const existingLead = await this.getLeadByEmail(lead.Email, request);

      if (existingLead?.Id) {
        if (existingLead.Opportunity_Conversion_Id__c) {
          // Skip update if lead is already converted
          await this.logApplicationEvent({
            request,
            applicationDetails: lead,
            logMessage: 'SKIPPED: Lead already converted, skipping update',
            event: this.loggerEnum.Event.LEAD_UPDATE_SKIPPED,
            destinationPayload: existingLead,
            destinationObject: 'Lead',
            destinationObjectId: existingLead.Id,
          });
          return;
        }

        // Proceed with update
        await this.logApplicationEvent({
          request,
          applicationDetails: lead,
          logMessage: 'EVENT INITIATED: Lead update initiated',
          event: this.loggerEnum.Event.LEAD_UPDATE_INITIATED,
          destinationPayload: lead,
          destinationObject: 'Lead',
          destinationObjectId: existingLead.Id,
        });

        await this.gusSalesforceLeadService.updateLead(existingLead.Id, lead);

        await this.logApplicationEvent({
          request,
          applicationDetails: lead,
          logMessage: 'EVENT COMPLETED: Lead update completed',
          event: this.loggerEnum.Event.LEAD_UPDATE_COMPLETED,
          destinationPayload: lead,
          destinationObject: 'Lead',
          destinationObjectId: existingLead.Id,
        });
      } else {
        // No existing lead found
        await this.logApplicationEvent({
          request,
          applicationDetails: lead,
          logMessage: 'SKIPPED: No existing lead found to update',
          event: this.loggerEnum.Event.LEAD_UPDATE_SKIPPED,
          destinationPayload: lead,
          destinationObject: 'Lead',
          destinationObjectId: '',
        });
      }
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'LEAD_UPDATE_FAILED',
        error.message ?? error,
        lead,
        'UPDATE_LEAD',
      );
    }
  }

  async updateApplicationWithOpportunity(
    opportunity,
    applicationDetails,
    request,
    personAccount,
  ): Promise<any> {
    if (!opportunity?.Owner?.IsActive && applicationDetails.Opportunity) {
      applicationDetails.OpportunityOwnerIsNotActive = true;
      applicationDetails.Opportunity.OwnerId =
        (await this.getDefaultOwnerId(
          request?.allowedEntities?.brand,
          'DIRECT',
        )) || opportunity?.OwnerId;
    }
    delete applicationDetails.Lead;
    await this.logApplicationEvent({
      request,
      applicationDetails,
      logMessage: 'EVENT INITIATED: Update application initiated',
      event: this.loggerEnum.Event.UPDATE_APPLICATION_INITIATED,
      sourcePayload: applicationDetails.applicationId,
      usecase: 'UPDATE',
    });
    applicationDetails['accountId'] = personAccount?.Id;
    applicationDetails['opportunityId'] = opportunity?.Id;
    await this.updateApplication(
      applicationDetails,
      request,
      applicationDetails.applicationId,
    );
  }
  async createApplicationWithAccount(
    applicationDetails,
    request,
    personAccount,
  ): Promise<any> {
    if (
      this.utilitiesService.checkRequiredField(
        applicationDetails['Opportunity'],
        'Opportunity',
      )
    ) {
      await this.logApplicationEvent({
        request,
        applicationDetails,
        logMessage: 'EVENT INITIATED: Create opportunity initiated',
        event: this.loggerEnum.Event.CREATE_OPPORTUNITY_INITIATED,
        sourcePayload: applicationDetails.Opportunity,
        destinationPayload: {},
        destinationObject:
          'Opportunity, OpportunityLineItem, OpportunityTeamMember',
        destinationObjectId: personAccount?.Id,
        usecase: 'CREATE',
      });
      const lead = await this.getLeadByEmail(applicationDetails.email, request);
      console.log('lead details -->', lead);
      if (
        personAccount?.Lead_Agent__c &&
        personAccount?.Lead_Agent__r?.Type !== 'Internal Use' &&
        personAccount?.Agent_retention__c !== 'Agent Retention Expired' &&
        personAccount?.Agent_retention__c !== 'Agent Contract Expired'
      ) {
        if (
          await this.isContractNotExpired(personAccount.Lead_Agent__c, request)
        ) {
          if (applicationDetails.Opportunity) {
            applicationDetails.Opportunity.ApplicationSource__c = 'Agent Lead';
          }
          if (applicationDetails.Application__c) {
            applicationDetails.Application__c.Assignment__c = 'Unassigned';
          }
          await this.mapSalesAttributes(
            ObjectConfig.Opportunity,
            {
              BusinessDeveloper__c: lead?.BusinessDeveloper__c,
              AgentAccount__c: personAccount?.Lead_Agent__c,
            },
            applicationDetails.Opportunity,
          );
          await this.mapSalesAttributes(
            ObjectConfig.Application__c,
            {
              BusinessDeveloper__c: lead?.BusinessDeveloper__c,
              AgentAccount__c: personAccount?.AgentAccount__c,
            },
            applicationDetails.Application__c,
          );
        } else {
          if (applicationDetails.Account) {
            applicationDetails.Account.Lead_Agent__c = null;
            applicationDetails.Account.Agent_retention__c =
              'Agent Contract Expired';
          }
          if (applicationDetails.Opportunity) {
            applicationDetails.Opportunity.OwnerId = lead?.OwnerId;
            applicationDetails.Opportunity.BusinessDeveloper__c =
              lead.BusinessDeveloper__c;
          }
          delete applicationDetails.Application__c;
        }
      } else {
        if (applicationDetails.Opportunity) {
          applicationDetails.Opportunity.OwnerId = lead?.Owner?.IsActive
            ? lead?.OwnerId
            : (await this.getDefaultOwnerId(
                request?.allowedEntities?.brand,
                'DIRECT',
              )) || lead?.OwnerId;

          applicationDetails.Opportunity.BusinessDeveloper__c = lead?.Owner
            ?.IsActive
            ? lead.BusinessDeveloper__c
            : (await this.getDefaultOwnerId(
                request?.allowedEntities?.brand,
                'DIRECT',
              )) || lead.BusinessDeveloper__c;
        }

        delete applicationDetails.Application__c;
      }
      if (
        this.utilitiesService.checkRequiredField(
          applicationDetails['Lead'],
          'Lead',
          'utmParamsRequiredFields',
        )
      ) {
        console.log(
          'create lead initiated with utm params',
          applicationDetails.Lead,
        );
        const leadId = await this.gusSalesforceLeadService.createLead(
          applicationDetails.Lead,
        );
        console.log('leadId -->', leadId);
      } else {
        console.log('create lead initiated without utm params');
      }
      delete applicationDetails.Lead;
      await this.createOpportunity(
        applicationDetails,
        personAccount.Id,
        request,
      );
      await this.logApplicationEvent({
        request,
        applicationDetails,
        logMessage: 'EVENT INITIATED: Update application initiated',
        event: this.loggerEnum.Event.UPDATE_APPLICATION_INITIATED,
        sourcePayload: applicationDetails.applicationId,
        usecase: 'UPDATE',
      });
      const opportunity = await this.getOpportunityIdByApplicationId(
        applicationDetails.applicationId,
        request,
      );
      applicationDetails['accountId'] = personAccount?.Id;
      applicationDetails['opportunityId'] = opportunity?.Id;
      await this.updateApplication(
        applicationDetails,
        request,
        applicationDetails.applicationId,
      );
    } else {
      await this.logApplicationEvent({
        request,
        applicationDetails,
        logMessage: 'Missing opportunity required',
        event: this.loggerEnum.Event.OPPORTUNITY_REQUIRED_FIELD_MISSING,
      });
    }
  }

  async createApplicationWithActiveLead(
    lead,
    applicationDetails,
    request,
    removeObjectList,
  ): Promise<any> {
    // Update lead status and terms & conditions if needed
    await this.updateLeadIfNeeded(
      lead,
      applicationDetails.Lead,
      request,
      applicationDetails,
      process.env.LEAD_SALES_QUEUE_ID,
    );

    const isContractNotExpired = lead.AgentAccount__c
      ? await this.isContractNotExpired(lead.AgentAccount__c, request)
      : false;
    const isAgentLead =
      lead.AgentAccount__c &&
      lead.AgentAccount__r?.Type !== 'Internal Use' &&
      lead?.Agent_retention__c !== 'Agent Retention Expired' &&
      lead?.Agent_retention__c !== 'Agent Contract Expired' &&
      isContractNotExpired;
    if (isAgentLead) {
      if (applicationDetails.Opportunity) {
        applicationDetails.Opportunity.ApplicationSource__c = 'Agent Lead';
      }
      if (applicationDetails.Application__c) {
        applicationDetails.Application__c.Assignment__c = 'Unassigned';
      }
      await this.mapSalesAttributes(
        ObjectConfig.Account,
        lead,
        applicationDetails.Account,
      );
      await this.mapSalesAttributes(
        ObjectConfig.Opportunity,
        lead,
        applicationDetails.Opportunity,
      );
      await this.mapSalesAttributes(
        ObjectConfig.Application__c,
        lead,
        applicationDetails.Application__c,
      );
    } else {
      this.updateOwnership(applicationDetails, lead, removeObjectList);
    }
    await this.logApplicationEvent({
      request,
      applicationDetails,
      logMessage:
        'EVENT INITIATED: create application with active lead initiated',
      event: this.loggerEnum.Event.CREATE_OPPORTUNITY_ACTIVE_LEAD_INITIATED,
      usecase: 'CREATE',
    });
    applicationDetails.leadId = lead?.Id;
    applicationDetails.opportunityConversionId =
      lead?.Opportunity_Conversion_Id__c;
    if (applicationDetails.Opportunity) {
      applicationDetails.Opportunity.AccountId = '@{Account.id}';
    }
    await this.createApplication(applicationDetails, request, removeObjectList);
    await this.logApplicationEvent({
      request,
      applicationDetails,
      logMessage:
        'EVENT COMPLETED: application creation with active lead completed',
      event: this.loggerEnum.Event.CREATE_OPPORTUNITY_ACTIVE_LEAD_COMPLETED,
    });
    const opportunity = await this.getOpportunityIdByApplicationId(
      applicationDetails.applicationId,
      request,
    );
    applicationDetails['accountId'] = await this.getPersonAccountId(
      applicationDetails.email,
      request,
    );
    applicationDetails['opportunityId'] = opportunity?.Id;
    delete applicationDetails.Opportunity.AccountId;
    delete applicationDetails.Lead;
    if (
      applicationDetails['opportunityId'] &&
      applicationDetails['accountId']
    ) {
      await this.updateApplication(
        applicationDetails,
        request,
        applicationDetails.applicationId,
      );
    }
  }
  updateOwnership(applicationDetails, lead, removeObjectList) {
    if (applicationDetails.Opportunity) {
      applicationDetails.Opportunity.OwnerId = lead?.OwnerId;
      applicationDetails.Opportunity.BusinessDeveloper__c =
        lead?.BusinessDeveloper__c;
    }
    if (applicationDetails.Account) {
      applicationDetails.Account.OwnerId = lead?.OwnerId;
      applicationDetails.Account.Lead_Creation_Date__c =
        lead?.Created_Date_and_Time__c;
    }
    delete applicationDetails.Application__c;
    removeObjectList.push('Application__c');
  }
  async isContractNotExpired(agentAccountId, request): Promise<any> {
    try {
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          'Account_Age_Contract__c',
          request?.allowedEntities,
        );
      let condition = `WHERE Account__c ='${agentAccountId}'`;

      if (whereClause) {
        condition += ` AND ${whereClause}`;
      }
      const acccount =
        await this.gusSalesforceAccountAgeContractService.getAccountAgeContract(
          fieldsRequired,
          condition,
          true,
        );
      return acccount.totalSize > 0;
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GET_ACCOUNT_CONTRACT_AGE_FAILED',
        error.message ? error.message : error,
        agentAccountId,
        'COMPLETED_GET_ACCOUNT_AGE',
      );
    }
  }
  async mapSalesAttributes(objectConfig, source, target) {
    const mappings = objectConfig.SalesAttributeMappings;
    if (!mappings) return;

    Object.keys(mappings).forEach((targetField) => {
      const sourceField = mappings[targetField];
      if (
        sourceField &&
        source?.[sourceField] &&
        source[sourceField] !== undefined
      ) {
        target[targetField] = source?.[sourceField];
      }
    });
  }
  async recordSalesforceRequest(object, action, request, initiated = true) {
    const event = initiated
      ? this.loggerEnum.Event.SALESFORCE_REQUEST_INITIATED
      : this.loggerEnum.Event.SALESFORCE_REQUEST_COMPLETED;
    await this.cloudWatchLoggerService.log(
      request?.body.requestId,
      new Date().toISOString(),
      this.loggerEnum.Component.GUS_EIP_SERVICE,
      this.loggerEnum.Component.OAP_HANDLERS,
      this.loggerEnum.Component.GUS_SALESFORCE,
      event,
      action,
      request.body,
      request.body,
      'Salesforce Request',
      request?.allowedEntities?.brand,
      request?.body.email,
      request?.body?.applicationId
        ? `gus-middleware-service/${request?.body.applicationId}/${request?.body.requestId}`
        : `gus-middleware-service/${request?.requestId}`,
      request?.body.applicationId ? 'Application_Form_Id__c' : null,
      request?.body.applicationId ? request?.body.applicationId : null,
      object,
      request?.body.applicationId,
    );
  }
  async getPersonAccountId(email: string, request): Promise<any> {
    try {
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          'Account',
          request?.allowedEntities,
        );
      const personAccountsDetails =
        await this.gusSalesforceAccountService.getPersonAccount(
          encodeURIComponent(email),
          'Id',
          whereClause,
        );
      return personAccountsDetails[0]?.Id;
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GET_ACCOUNT_BY_EMAILID_FAILED',
        error.message ? error.message : error,
        email,
        this.loggerEnum.Event.SUBMIT_APPLICATION,
      );
    }
  }

  async getPersonAccount(email: string, request): Promise<any> {
    try {
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          'Account',
          request?.allowedEntities,
        );
      const personAccountsDetails =
        await this.gusSalesforceAccountService.getPersonAccount(
          encodeURIComponent(email),
          fieldsRequired,
          whereClause,
        );
      return personAccountsDetails[0];
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GET_ACCOUNT_BY_EMAILID_FAILED',
        error.message ? error.message : error,
        email,
        this.loggerEnum.Event.SUBMIT_APPLICATION,
      );
    }
  }

  async getLeadByEmail(email, request): Promise<any> {
    const entityName = 'Lead';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
    );

    let leadByEmailIdCondition = `WHERE Email ='${email}'`;

    if (whereClause) {
      leadByEmailIdCondition += ` AND ${whereClause}`;
    }
    try {
      await this.recordSalesforceRequest('Lead', 'GET', request, true);

      const lead = await this.gusSalesforceLeadService.get(
        fieldsRequired,
        leadByEmailIdCondition,
      );

      await this.recordSalesforceRequest('Lead', 'GET', request, false);
      return lead?.[0];
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GET_LEAD_BY_EMAILID_FAILED',
        error.message ? error.message : error,
        email,
        this.loggerEnum.Event.SUBMIT_APPLICATION,
      );
    }
  }
  async getLeadIdByEmail(email, request): Promise<any> {
    const entityName = 'Lead';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
    );

    let leadByEmailIdCondition = `WHERE Email ='${email}'`;

    if (whereClause) {
      leadByEmailIdCondition += ` AND ${whereClause}`;
    }
    try {
      await this.recordSalesforceRequest('Lead', 'GET', request, true);

      const lead = await this.gusSalesforceLeadService.get(
        fieldsRequired,
        leadByEmailIdCondition,
      );

      await this.recordSalesforceRequest('Lead', 'GET', request, false);
      return lead?.[0]?.Id;
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GET_LEAD_BY_EMAILID_FAILED',
        error.message ? error.message : error,
        email,
        this.loggerEnum.Event.SUBMIT_APPLICATION,
      );
    }
  }

  async getIndividualByEmail(email, request): Promise<any> {
    const entityName = 'Individual';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
    );

    let individualByEmailIdCondition = `WHERE Email__c ='${email}'`;

    if (whereClause) {
      individualByEmailIdCondition += ` AND ${whereClause}`;
    }
    try {
      await this.recordSalesforceRequest('Individual', 'GET', request, true);
      const individual = await this.gusSalesforceIndividual.get(
        fieldsRequired,
        individualByEmailIdCondition,
      );
      await this.recordSalesforceRequest('Individual', 'GET', request, false);
      return individual?.[0]?.Id;
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GET_INDIVIDUAL_BY_EMAILID_FAILED',
        error.message ? error.message : error,
        email,
        this.loggerEnum.Event.SUBMIT_APPLICATION,
      );
    }
  }

  async saveIdentityInfoRecord(applicationDetails, request) {
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      'IdentityInfoRecord__c',
      request?.allowedEntities,
    );

    const getIdentityInfoRecord =
      await this.gusSalesforceIdentityInfoRecordService.getIdentityInfoRecord(
        applicationDetails,
        fieldsRequired,
      );

    if (getIdentityInfoRecord.length == 0) {
      await this.gusSalesforceIdentityInfoRecordService.createIdentityInfoRecord(
        applicationDetails,
      );
    } else {
      await this.gusSalesforceIdentityInfoRecordService.updateIdentityInfoRecord(
        getIdentityInfoRecord[0]?.Id,
        applicationDetails,
      );
    }
  }
  async updateApplication(
    updateRequests: any,
    request: any,
    applicationId,
  ): Promise<void> {
    if (
      updateRequests?.OpportunityLineItem &&
      Array.isArray(updateRequests.OpportunityLineItem)
    ) {
      await this.enrichOpportunityLineItemsWithPricebookEntry(
        updateRequests,
        request,
      );
    }

    if (updateRequests.hasOwnProperty('Opportunity')) {
      const opportunity = updateRequests.Opportunity;
      delete opportunity.Name;
      delete opportunity.BusinessDeveloper__c;
      if (!updateRequests?.OpportunityOwnerIsNotActive) {
        delete opportunity.OwnerId;
      }
      delete opportunity.ApplicationSource__c;
    }
    if (updateRequests.hasOwnProperty('Account')) {
      const Account = updateRequests.Account;
      delete Account?.OwnerId;
    }
    const objectsUpdateDetails: any = {
      Lead: {
        getby: 'email',
        functionName: 'getLeadIdByEmail',
        functionParams: ['email', request],
        updatedByField: 'leadId',
      },
      Individual: {
        getby: 'email',
        functionName: 'getIndividualByEmail',
        functionParams: ['email', request],
        updatedByField: 'individualId',
      },
      Account: {
        getby: 'email',
        updatedByField: 'accountId',
        functionName: 'getPersonAccountId',
        functionParams: ['email', request],
      },
      Opportunity: {
        getby: 'applicationId',
        updatedByField: 'opportunityId',
      },
      Application__c: {
        getby: 'applicationId',
        updatedByField: 'salApplicationId',
        functionName: 'getApplicationByApplicationId',
        functionParams: [applicationId, request],
      },
      OpportunityFile__c: {
        updateWithList: {
          opportunityId: 'Opportunity__c',
          accountId: 'Related_Person_Account__c',
        },
        updateWithConditions: [
          {
            restrictedField: 'Related_Person_Account__c',
            restrictedByField: 'DocumentType__c',
            allowedValues: [
              'CV',
              'Visa',
              'Financial Statement Affidavit of Support',
              'Personal statement',
              'Reference letter',
              'Other certification',
              'Photograph',
              'Accreditation Disclosure Form',
              'Consumer Information Disclosure Form',
              'Enrollment Agreement',
              'International Student Statement of Understanding',
            ],
          },
        ],
      },
      Connection__c: {
        updateWithList: {
          opportunityId: 'Opportunity_c__c',
          accountId: 'Account__c',
        },
      },
      EducationHistoryRecord__c: {
        updateWithList: {
          opportunityId: 'Opportunity__c',
          accountId: 'Account__c',
        },
      },
      WorkHistoryRecord__c: {
        updateWithList: {
          opportunityId: 'Opportunity__c',
          accountId: 'Account__c',
        },
      },
      LanguageProficiencyRecord__c: {
        updateWithList: {
          opportunityId: 'Opportunity__c',
          accountId: 'Account__c',
        },
      },
      IdentityInfoRecord__c: {
        updateWithList: {
          opportunityId: 'Opportunity__c',
          accountId: 'Account__c',
        },
      },
      Visa_Application__c: {
        updateWithList: {
          opportunityId: 'Opportunity__c',
          accountId: 'Account__c',
        },
      },
      OpportunityLineItem: {
        updateWithList: {
          opportunityId: 'OpportunityId',
        },
      },
    };

    let compositeRequest: any[] = [];

    for (const updateObject in updateRequests) {
      if (updateObject in objectsUpdateDetails) {
        if (Array.isArray(updateRequests[updateObject])) {
          compositeRequest = [
            ...compositeRequest,
            ...(await this.buildCompositeRequestForList(
              updateRequests,
              updateObject,
              objectsUpdateDetails,
            )),
          ];
        } else {
          const singleCompositeRequest =
            await this.buildCompositeRequestForSingleObject(
              updateRequests,
              objectsUpdateDetails,
              updateObject,
            );
          if (singleCompositeRequest) {
            compositeRequest.push(singleCompositeRequest);
          }
        }
      }
    }

    try {
      await this.recordSalesforceRequest(
        'COMPOSITE_REQUEST',
        this.loggerEnum.UseCase[`${request?.body?.sectionLabel}`],
        request,
        true,
      );
      const { compositeResponse } =
        await this.gusSalesforceCompositeService.compositeRequest(
          compositeRequest,
        );
      if (compositeResponse) {
        await this.log(
          request?.body.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Component.GUS_SALESFORCE,
          this.loggerEnum.Event.UPDATE_APPLICATION_COMPLETED,
          this.loggerEnum.UseCase[`${request?.body.sectionLabel}`] || 'UPDATE',
          { compositeRequest },
          { compositeRequest },
          'Update salesforce application completed',
          request?.allowedEntities?.brand,
          request?.body.email,
          'Application',
          applicationId,
          applicationId,
          compositeResponse,
        );

        compositeResponse?.forEach((entry) => {
          if (entry.httpStatusCode !== 204 && entry.httpStatusCode !== 201) {
            throw new Error(
              `Updating ${
                entry.referenceId
              } failed with error: ${JSON.stringify(entry.body)}`,
            );
          }
        });

        await this.recordSalesforceRequest(
          'COMPOSITE_REQUEST',
          this.loggerEnum.UseCase[`${request?.body?.sectionLabel}`],
          request,
          false,
        );
      }
    } catch (error) {
      console.log('Err -->', error);
      console.log('Errr -->', JSON.stringify(error));
      throw new CustomException(
        this.getStatus(error),
        'UPDATE_APPLICATION_FAILED',
        error.message ? error.message : error,
        JSON.stringify(compositeRequest),
        this.loggerEnum.Event.UPDATE_APPLICATION,
      );
    }
  }
  async buildCompositeRequestForList(
    updateRequests,
    updateObject,
    objectsUpdateDetails,
  ): Promise<any> {
    const compositeRequest = [];
    if (Array.isArray(updateRequests[updateObject])) {
      const updateWith =
        updateRequests[objectsUpdateDetails[updateObject]['updateWith']];
      const mappedList = {};
      let updateWithConditions;
      if (!updateWith) {
        const updateWithList =
          objectsUpdateDetails[updateObject]['updateWithList'];
        updateWithConditions =
          objectsUpdateDetails?.[updateObject]?.['updateWithConditions'];
        for (const updateWith in updateWithList) {
          mappedList[updateWithList[updateWith]] = updateRequests[updateWith];
        }
      }
      if (updateWith || Object.keys(mappedList).length !== 0) {
        let count = 1;
        for (let item of updateRequests[updateObject]) {
          if (Object.keys(mappedList).length !== 0) {
            const updatedMappedList = { ...mappedList };
            if (updateWithConditions) {
              updateWithConditions.forEach(
                ({ restrictedField, restrictedByField, allowedValues }) => {
                  if (
                    restrictedField in updatedMappedList &&
                    updatedMappedList[restrictedField] &&
                    restrictedByField in item &&
                    !allowedValues.includes(item[restrictedByField])
                  ) {
                    delete updatedMappedList[restrictedField];
                  }
                },
              );
            }
            item = { ...item, ...updatedMappedList };
          }

          if (this.utilitiesService.checkRequiredField(item, updateObject)) {
            if (updateWith) {
              item[objectsUpdateDetails[updateObject]['updateWithFieldName']] =
                updateWith;
            }
            compositeRequest.push(
              await this.buildCompositeRequest(
                updateObject,
                `${updateObject}_${count}`,
                item,
                'POST',
              ),
            );
            ++count;
          }
        }
      }
    }
    console.log('compositeRequest', JSON.stringify(compositeRequest));
    return compositeRequest;
  }
  private async updateLeadIfNeeded(
    lead: any,
    leadPayload: any,
    request: any,
    applicationDetails: any,
    leadOwner?: any,
  ): Promise<void> {
    // Check if we need to update status (Prospect -> New) or terms and conditions
    const shouldUpdateStatus = lead?.Status === 'Prospect';
    const shouldUpdateTerms =
      leadPayload &&
      this.utilitiesService.checkRequiredField(
        leadPayload,
        'Lead',
        'termsAndCondition',
      );

    // If no updates needed, return early
    if (!shouldUpdateStatus && !shouldUpdateTerms) {
      return;
    }

    // Build the updates object
    const updates: any = {};

    // Add status update if needed
    if (shouldUpdateStatus) {
      updates.Status = 'New';
      updates.OwnerId = leadOwner;
    }

    // Add terms and conditions fields if present in payload
    if (shouldUpdateTerms && leadPayload) {
      const termsFields = ObjectConfig.Lead.termsAndCondition || [];
      termsFields.forEach((field) => {
        if (field in leadPayload) {
          updates[field] = leadPayload[field];
        }
      });
    }

    // If no updates to apply, return
    if (Object.keys(updates).length === 0) {
      return;
    }

    try {
      // Log the update initiation
      await this.logApplicationEvent({
        request,
        applicationDetails,
        logMessage: `Lead update initiated${
          shouldUpdateStatus ? ' (status: Prospect -> New)' : ''
        }${shouldUpdateTerms ? ' with terms & conditions' : ''}`,
        event: this.loggerEnum.Event.LEAD_STATUS_UPDATE_INITIATED,
        sourcePayload: { leadId: lead.Id, oldStatus: lead.Status, updates },
        destinationObject: 'Lead',
        destinationObjectId: lead.Id,
        usecase: this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
      });

      // Perform the update
      await this.gusSalesforceLeadService.updateLead(lead.Id, updates);

      // Log the update completion
      await this.logApplicationEvent({
        request,
        applicationDetails,
        logMessage: `Lead updated successfully${
          shouldUpdateStatus ? ' (status: New)' : ''
        }${shouldUpdateTerms ? ' with terms & conditions' : ''}`,
        event: this.loggerEnum.Event.LEAD_STATUS_UPDATE_COMPLETED,
        sourcePayload: { leadId: lead.Id, updates },
        destinationObject: 'Lead',
        destinationObjectId: lead.Id,
        usecase: this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
      });
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'LEAD_STATUS_UPDATE_FAILED',
        error.message || 'Failed to update lead status or terms',
        lead.Id,
        this.loggerEnum.Event.LEAD_STATUS_UPDATE_FAILED,
      );
    }
  }
  async buildCompositeRequestForSingleObject(
    updateRequests,
    objectsUpdateDetails,
    updateObject,
  ): Promise<any> {
    const updateByValue =
      updateRequests[objectsUpdateDetails?.[updateObject]?.updatedByField];

    if (updateByValue) {
      return await this.buildCompositeRequest(
        updateObject,
        `${updateObject}`,
        updateRequests[updateObject],
        'PATCH',
        updateByValue,
      );
    } else {
      const getBy = objectsUpdateDetails?.[updateObject]?.getby;
      const getByValue = updateRequests[getBy];

      if (getByValue) {
        const serviceName = objectsUpdateDetails?.[updateObject]?.service;
        const functionName = objectsUpdateDetails?.[updateObject]?.functionName;
        const updateFunction = serviceName
          ? this[serviceName][functionName]
          : this[functionName];

        if (updateFunction && typeof updateFunction === 'function') {
          const updateFunctionParams = objectsUpdateDetails?.[
            updateObject
          ]?.functionParams.map((param: any) =>
            param === getBy ? getByValue : param,
          );

          let updateById: any;

          if (!serviceName) {
            const boundUpdateFunction = updateFunction.bind(this);
            updateById = await boundUpdateFunction(...updateFunctionParams);
          } else {
            const result = updateFunction.call(
              this[serviceName],
              ...updateFunctionParams,
            );
            updateById = await result;
          }

          if (updateById) {
            updateRequests[
              objectsUpdateDetails?.[updateObject]?.updatedByField
            ] = updateById;
            return await this.buildCompositeRequest(
              updateObject,
              `${updateObject}`,
              updateRequests[updateObject],
              'PATCH',
              updateById,
            );
          }
        }
      }
    }
  }

  async buildCompositeRequest(
    object: string,
    referenceId: string,
    request: object = null,
    method: string,
    id: string | null = null,
  ): Promise<any> {
    const url = id
      ? `/services/data/${process.env.SALESFORCE_API_VERSION}/sobjects/${object}/${id}`
      : `/services/data/${process.env.SALESFORCE_API_VERSION}/sobjects/${object}`;

    return {
      method,
      url,
      referenceId,
      body: request,
    };
  }
  removeItemsFromList(list1, list2) {
    const result = list1.filter((item) => !list2.includes(item));
    return result;
  }
  async createApplication(
    applicationDetails,
    request,
    createdObjects = [],
  ): Promise<any> {
    await this.recordSalesforceRequest(
      'COMPOSITE_REQUEST',
      this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
      request,
      true,
    );
    let objectsToCreate = ['Account', 'Opportunity', 'Lead', 'Application__c'];
    objectsToCreate = this.removeItemsFromList(objectsToCreate, createdObjects);
    const compositeRequest = [];

    for (const object of objectsToCreate) {
      if (
        this.utilitiesService.checkRequiredField(
          applicationDetails[object],
          object,
        )
      ) {
        compositeRequest.push(
          await this.buildCompositeRequest(
            object,
            `${object}`,
            applicationDetails[object],
            'POST',
          ),
        );
      }
    }
    const hasAllObj = objectsToCreate.every((object) =>
      compositeRequest.some((request) => request.referenceId === object),
    );
    const mappedIds = {};
    if (hasAllObj) {
      try {
        const { compositeResponse } =
          await this.gusSalesforceCompositeService.compositeRequest(
            compositeRequest,
          );
        if (compositeResponse) {
          await this.log(
            applicationDetails.requestId,
            new Date().toISOString(),
            this.loggerEnum.Component.GUS_EIP_SERVICE,
            this.loggerEnum.Component.OAP_HANDLERS,
            this.loggerEnum.Component.GUS_SALESFORCE,
            this.loggerEnum.Event.CREATE_APPLICATION_COMPLETED,
            this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
            { compositeRequest },
            { compositeRequest },
            'Application created in salesforce',
            request?.allowedEntities?.brand,
            applicationDetails.email || '',
            'Application',
            applicationDetails.applicationId,
            applicationDetails.applicationId,
            compositeResponse,
          );
          await this.recordSalesforceRequest(
            'COMPOSITE_REQUEST',
            this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
            request,
            false,
          );

          compositeResponse?.forEach((entry) => {
            if (entry.httpStatusCode === 201) {
              mappedIds[entry.referenceId] = entry.body.id;
            } else {
              throw new Error(
                `Creating ${
                  entry.referenceId
                } failed with error: ${JSON.stringify(entry.body)} `,
              );
            }
          });
        }
      } catch (error) {
        throw new CustomException(
          this.getStatus(error),
          'CREATE_APPLICATION_FAILED',
          error.message ? error.message : error,
          JSON.stringify(compositeRequest),
          this.loggerEnum.Event.NEW_APPLICATION,
        );
      }
      if (hasAllObj && applicationDetails?.leadId) {
        mappedIds['Lead'] = applicationDetails?.leadId;
      }
      if (applicationDetails?.Application__c?.Id) {
        mappedIds['Application__c'] = applicationDetails.Application__c.Id;
      }
      const updateRequest = await this.buildUpdateRequest(
        mappedIds,
        applicationDetails,
      );
      try {
        await this.updateApplication(
          updateRequest,
          request,
          applicationDetails.applicationId,
        );
      } catch (error) {
        console.log('error in update application', error);
      }
      try {
        if (mappedIds?.['Opportunity']) {
          if (applicationDetails.OpportunityTeamMember) {
            try {
              await this.createOpportunityTeamMember(
                applicationDetails,
                mappedIds?.['Opportunity'],
                request,
              );
            } catch (error) {
              console.log('error in creating team member', error);
            }
          }
          if (applicationDetails.OpportunityLineItem) {
            applicationDetails.OpportunityLineItem.OpportunityId =
              mappedIds?.['Opportunity'];
            applicationDetails.OpportunityLineItem.PricebookEntryId =
              await this.getPriceBookEntry(
                applicationDetails.Opportunity.Pricebook2Id,
                applicationDetails.OpportunityLineItem.Product2Id,
                request,
              );
            await this.recordSalesforceRequest(
              'OpportunityLineItem',
              'CREATE',
              request,
              true,
            );
            if (applicationDetails?.OpportunityLineItem) {
              const response =
                await this.gusSalesforceOpportunityLineItemService.createOpportuntiyLineItem(
                  applicationDetails.OpportunityLineItem,
                );
              await this.log(
                applicationDetails.requestId,
                new Date().toISOString(),
                this.loggerEnum.Component.GUS_EIP_SERVICE,
                this.loggerEnum.Component.OAP_HANDLERS,
                this.loggerEnum.Component.GUS_SALESFORCE,
                this.loggerEnum.Event.CREATED_OPPORTUNITYLINEITEM,
                this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
                applicationDetails,
                applicationDetails.OpportunityLineItem,
                'Opportunity line item created',
                request?.allowedEntities?.brand,
                applicationDetails?.email || '',
                'Application',
                applicationDetails.applicationId,
                applicationDetails.applicationId,
                response,
              );
            }
            await this.recordSalesforceRequest(
              'OpportunityLineItem',
              'CREATE',
              request,
              false,
            );
          }
        }
        return mappedIds;
      } catch (error) {
        throw new CustomException(
          this.getStatus(error),
          'OPPORTUNITY_LINE_ITEM_CREATE_FAILED',
          error.message ? error.message : error,
          JSON.stringify(applicationDetails.OpportunityLineItem),
          this.loggerEnum.Event.SUBMIT_APPLICATION,
        );
      }
    } else {
      await this.log(
        applicationDetails.requestId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OAP_HANDLERS,
        this.loggerEnum.Component.GUS_SALESFORCE,
        this.loggerEnum.Event.REQUIRED_OBJECTS_CREATE_APPLICATION,
        this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
        { compositeRequest },
        { compositeRequest },
        'Application created in salesforce',
        request?.allowedEntities?.brand,
        applicationDetails.email || '',
        'Application',
        applicationDetails.applicationId,
        applicationDetails.applicationId,
        {},
      );
      return;
    }
  }
  getStatus(error): any {
    let status;
    if (error.statusCode) {
      status = error.statusCode;
    } else if (error.code) {
      status = error.code;
    } else {
      status = '500';
    }
    return status;
  }
  async buildUpdateRequest(mappedIds, applicationDetails): Promise<any> {
    const updateRequest = {};
    for (const mappedId in mappedIds) {
      const id = mappedIds[mappedId];
      const opportunityUpdateFields = {
        Account: 'AccountId',
        Application__c: 'Applications__c',
      };
      if (mappedId in opportunityUpdateFields) {
        if (mappedIds['Opportunity']) {
          const opportunity = (updateRequest['Opportunity'] ??= {});
          opportunity[opportunityUpdateFields[mappedId]] = id;
          updateRequest['opportunityId'] = mappedIds['Opportunity'];
        }
      } else if (mappedId === 'Opportunity') {
        if (mappedIds['Application__c']) {
          const application = (updateRequest['Application__c'] ??= {});
          application['Opportunity_B2C__c'] = id;
          updateRequest['salApplicationId'] = mappedIds['Application__c'];
        }
        if (mappedIds['Lead'] && !applicationDetails?.opportunityConversionId) {
          const application = (updateRequest['Lead'] ??= {});
          application['Opportunity_Conversion_Id__c'] = id;
          updateRequest['leadId'] = mappedIds['Lead'];
        }
      } else if (mappedId === 'Individual' && mappedIds['Account']) {
        const application = (updateRequest['Account'] ??= {});
        application['IndividualId__c'] = id;
        updateRequest['accountId'] = mappedIds['Account'];
      }
    }
    return updateRequest;
  }
  async getOpportunityIdByApplicationId(
    applicationId: string,
    request: any,
  ): Promise<any> {
    const entityName = 'Opportunity';
    const entityOperation = 'opportunityByApplicationId';

    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    let opportunityByApplicationIdCondition = `WHERE ApplicationFormId__c='${applicationId}'`;

    if (whereClause) {
      opportunityByApplicationIdCondition += ` AND ${whereClause}`;
    }
    try {
      await this.recordSalesforceRequest('Opportunity', 'GET', request, true);
      const opportunities =
        await this.gusSalesforceOpportunityService.getOpportunities(
          fieldsRequired,
          opportunityByApplicationIdCondition,
        );
      await this.recordSalesforceRequest('Opportunity', 'GET', request, false);
      return opportunities?.[0];
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GETOPPORTUNITY_BY_APPLICATIONID_FAILED',
        error.message ? error.message : error,
        `${fieldsRequired} ${opportunityByApplicationIdCondition}`,
        this.loggerEnum.Event.SUBMIT_APPLICATION,
      );
    }
  }
  async createOpportunity(
    applicationDetails,
    personAccountId,
    request,
  ): Promise<any> {
    await this.recordSalesforceRequest('Application__c', 'GET', request, true);
    const applicationId = await this.getApplicationByApplicationId(
      applicationDetails.Opportunity.ApplicationFormId__c,
      request,
    );
    applicationDetails.Opportunity.Applications__c = applicationId
      ? applicationId
      : await this.gusSalesforceApplicationService.createApplication(
          applicationDetails.Application__c,
        );
    await this.recordSalesforceRequest('Application__c', 'GET', request, false);
    applicationDetails.Opportunity.AccountId = personAccountId;
    applicationDetails.Opportunity.Name =
      applicationDetails.Opportunity.ApplicationFormId__c;
    let opportunityId;
    try {
      await this.recordSalesforceRequest(
        'Opportunity',
        'CREATE',
        request,
        true,
      );
      opportunityId =
        await this.gusSalesforceOpportunityService.createOpportuntiy(
          applicationDetails.Opportunity,
        );
      if (opportunityId) {
        await this.log(
          applicationDetails.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Component.GUS_SALESFORCE,
          this.loggerEnum.Event.OPPORTUNITY_CREATED,
          this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
          applicationDetails,
          {
            opportunityId: opportunityId,
            request: applicationDetails.Opportunity,
          },
          'Opportunity created successfully',
          request?.allowedEntities?.brand,
          applicationDetails.email || '',
          'Opportunity',
          applicationDetails.applicationId,
          applicationDetails.applicationId,
        );
        await this.recordSalesforceRequest(
          'Opportunity',
          'CREATE',
          request,
          false,
        );
      }
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'CREATE_OPPORTUNITY_FAILED',
        error.message ? error.message : error,
        JSON.stringify(applicationDetails.Opportunity),
        this.loggerEnum.Event.SUBMIT_APPLICATION,
      );
    }
    const mappedIds = {
      Opportunity: opportunityId.data.id,
      Application__c: applicationDetails.Opportunity.Applications__c,
    };
    const updateRequest = await this.buildUpdateRequest(
      mappedIds,
      applicationDetails,
    );
    await this.updateApplication(
      updateRequest,
      request,
      applicationDetails.applicationId,
    );
    try {
      if (opportunityId?.data?.id) {
        if (applicationDetails.OpportunityTeamMember) {
          await this.createOpportunityTeamMember(
            applicationDetails,
            opportunityId?.data?.id,
            request,
          );
        }
        if (applicationDetails?.OpportunityLineItem) {
          applicationDetails.OpportunityLineItem.OpportunityId =
            opportunityId.data.id;
          applicationDetails.OpportunityLineItem.PricebookEntryId =
            await this.getPriceBookEntry(
              applicationDetails.Opportunity.Pricebook2Id,
              applicationDetails.OpportunityLineItem.Product2Id,
              request,
            );
          await this.recordSalesforceRequest(
            'OpportunityLineItem',
            'CREATE',
            request,
            true,
          );
          const response =
            await this.gusSalesforceOpportunityLineItemService.createOpportuntiyLineItem(
              applicationDetails.OpportunityLineItem,
            );
          await this.log(
            applicationDetails.requestId,
            new Date().toISOString(),
            this.loggerEnum.Component.GUS_EIP_SERVICE,
            this.loggerEnum.Component.OAP_HANDLERS,
            this.loggerEnum.Component.GUS_SALESFORCE,
            this.loggerEnum.Event.CREATED_OPPORTUNITYLINEITEM,
            this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
            applicationDetails,
            applicationDetails.OpportunityLineItem,
            'Opportunity line item created',
            request?.allowedEntities?.brand,
            applicationDetails?.email || '',
            'Opportunity',
            applicationDetails.applicationId,
            applicationDetails.applicationId,
            response,
          );
          await this.recordSalesforceRequest(
            'OpportunityLineItem',
            'CREATE',
            request,
            false,
          );
        }
      }
      return mappedIds;
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'OPPORTUNITY_LINE_ITEM_CREATE_FAILED',
        error.message ? error.message : error,
        JSON.stringify(applicationDetails.OpportunityLineItem),
        this.loggerEnum.Event.CREATE_OPPORTUNITY_INITIATED,
      );
    }
  }
  async getApplicationByApplicationId(
    applicationId: string,
    request: any,
  ): Promise<string> {
    const entityName = 'Application__c';

    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
    );

    let applicationByEmailCondition = `WHERE Application_Form_Id__c='${applicationId}'`;

    if (whereClause) {
      applicationByEmailCondition += ` AND ${whereClause}`;
    }
    try {
      await this.recordSalesforceRequest(
        'Application__c',
        'GET',
        request,
        true,
      );
      const application =
        await this.gusSalesforceApplicationService.getApplication(
          fieldsRequired,
          applicationByEmailCondition,
        );
      await this.recordSalesforceRequest(
        'Application__c',
        'GET',
        request,
        false,
      );
      return application?.[0]?.Id;
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GET_APPLICATION_BY_EMAIL_FAILED',
        error.message ? error.message : error,
        `${fieldsRequired} ${applicationByEmailCondition}`,
        this.loggerEnum.Event.SUBMIT_APPLICATION,
      );
    }
  }
  async getPriceBookEntry(pricebookId, Product2Id, request): Promise<any> {
    const entityName = 'PricebookEntry';
    const object = 'pricebookDetails';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      object,
    );
    let pricebookEntryDetailsCondition = `where Pricebook2Id ='${pricebookId}' and Product2Id = '${Product2Id}' and IsActive = true `;
    if (whereClause) {
      pricebookEntryDetailsCondition += ` and ${whereClause}`;
    }
    try {
      await this.recordSalesforceRequest(
        'PricebookEntry',
        'GET',
        request,
        true,
      );
      const pricebookEntryDetailsResponse =
        await this.gusSalesforcePricebookEntryService.getPricebookEntry(
          fieldsRequired,
          pricebookEntryDetailsCondition,
          true,
        );
      await this.recordSalesforceRequest(
        'PricebookEntry',
        'GET',
        request,
        false,
      );
      const pricebookEntryId = pricebookEntryDetailsResponse?.records?.[0]?.Id;
      return pricebookEntryId;
    } catch (error) {
      throw new CustomException(
        error.message ? error.message : error,
        'GET_PRICEBOOK_ENTRY_FAILED',
        error.message ? error.message : error,
        `${fieldsRequired} ${pricebookEntryDetailsCondition}`,
        this.loggerEnum.Event.SUBMIT_APPLICATION,
      );
    }
  }

  async createOpportunityTeamMember(
    applicationDetails: any,
    opportunityId: string,
    request,
  ) {
    const compositeRequest = [];
    try {
      await this.recordSalesforceRequest(
        'OpportunityTeamMember',
        'CREATE',
        request,
        true,
      );
      const object = 'OpportunityTeamMember';
      let count = 0;
      for (const teamMember of applicationDetails.OpportunityTeamMember) {
        teamMember.OpportunityId = opportunityId;
        if (this.utilitiesService.checkRequiredField(teamMember, object)) {
          count = ++count;
          compositeRequest.push(
            await this.buildCompositeRequest(
              object,
              `${object}_${count}`,
              teamMember,
              'POST',
            ),
          );
        }
      }

      const { compositeResponse } =
        await this.gusSalesforceCompositeService.compositeRequest(
          compositeRequest,
        );
      if (compositeResponse) {
        await this.log(
          applicationDetails.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component.OAP_HANDLERS,
          this.loggerEnum.Component.GUS_SALESFORCE,
          this.loggerEnum.Event.CREATED_OPPORTUNITYTEAMMEMBER,
          this.loggerEnum.UseCase[`${applicationDetails.sectionLabel}`],
          { compositeRequest },
          { compositeResponse },
          'Opportunity team member created',
          request?.allowedEntities?.brand,
          applicationDetails.email || '',
          'Opportunity',
          applicationDetails.applicationId,
          applicationDetails.applicationId,
        );
        await this.recordSalesforceRequest(
          'OpportunityTeamMember',
          'CREATE',
          request,
          false,
        );
      }
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'CREATE_OPPORTUNITY_TEAM_MEMBER_FAILED',
        error.message ? error.message : error,
        JSON.stringify(compositeRequest),
        this.loggerEnum.Event.CREATE_OPPORTUNITY_INITIATED,
      );
    }
  }
  private async enrichOpportunityLineItemsWithPricebookEntry(
    updateRequests: any,
    request: any,
  ): Promise<void> {
    const pricebookId = updateRequests.Opportunity?.Pricebook2Id;
    const opportunityId = updateRequests.opportunityId;

    // Get existing opportunity line items if we have an opportunity ID
    let existingLineItems = [];
    if (opportunityId) {
      try {
        await this.recordSalesforceRequest(
          'OpportunityLineItem',
          'GET',
          request,
          true,
        );
        const existingLineItemsResponse =
          await this.gusSalesforceOpportunityLineItemService.getOpportunityLineItems(
            'Id',
            `WHERE OpportunityId = '${opportunityId}'`,
            true,
          );

        await this.recordSalesforceRequest(
          'OpportunityLineItem',
          'GET',
          request,
          false,
        );

        existingLineItems = existingLineItemsResponse?.records || [];

        // If opportunity already has line items, skip creating new ones (one line item per opportunity rule)
        if (existingLineItems.length > 0) {
          console.log(
            `Found ${existingLineItems.length} existing line item(s) for opportunity ${opportunityId}. Skipping creation of new line items to maintain one line item per opportunity rule.`,
          );
          // Clear the OpportunityLineItem array to prevent creation of new line items
          updateRequests.OpportunityLineItem = [];
          return; // Exit early since no line items need to be processed
        }
      } catch (error) {
        console.log('Error fetching existing opportunity line items:', error);
        // Continue processing even if we can't fetch existing line items
      }
    }

    // Process all line items in the request (only if no existing line items were found)
    for (const line of updateRequests.OpportunityLineItem) {
      // Enrich with PricebookEntryId if needed
      if (!line.PricebookEntryId && pricebookId && line.Product2Id) {
        line.PricebookEntryId = await this.getPriceBookEntry(
          pricebookId,
          line.Product2Id,
          request,
        );
      }
    }
  }
}
