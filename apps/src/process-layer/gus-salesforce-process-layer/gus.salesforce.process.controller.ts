import {
  Controller,
  Body,
  Post,
  Param,
  Get,
  HttpException,
  HttpStatus,
  Query,
  Req,
  Patch,
  InternalServerErrorException,
} from '@nestjs/common';
import { GusSalesforceProcessService } from './gus.salesforce.process.service';
import { Request } from 'express';
import { GusApplicationProcessService } from './gus.application.service';

// Update the existing DTO
class Product2QueryDto {
  object: string;
  customConditions?: Record<string, any>;
  groupBy?: string;
  entityName?: string;
}

@Controller('/salesforce')
export class GusSalesforceProcessController {
  constructor(
    private readonly gusSalesforceProcessService: GusSalesforceProcessService,
    private readonly gusApplicationProcessService: GusApplicationProcessService,
  ) {}
  @Get('/gus/getOpportunitiesById/:id')
  async opportunityById(
    @Param('id') id: string,
    @Req() request: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getOpportunityDetailsById(
      id,
      request,
    );
  }
  @Get('/gus/programlanguages')
  async programLanguages(
    @Query() event: any,
    @Req() request: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgramLanguagesByLevel(
      event,
      request,
    );
  }

  @Get('/gus/programbylanguage')
  async programByLanguage(
    @Query() event: any,
    @Req() request: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgramByLanguage(
      event,
      request,
    );
  }

  @Get('/gus/programlocations')
  async programLocationByProgrammeDetails(
    @Query() event: any,
    @Req() request: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgramLocationByProgramDetails(
      event,
      request,
    );
  }

  @Get('/gus/programduration')
  async programDurationByProgrammeDetails(
    @Query() event: any,
    @Req() request: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgramDurationByProgramDetails(
      event,
      request,
    );
  }

  @Get('/gus/programintake')
  async programIntakeByProgrammeDetails(
    @Query() event: any,
    @Req() request: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgramIntakeByProgramDetails(
      event,
      request,
    );
  }

  @Get('/gus/getOpportunities/:filterBy/:id')
  async opportunityByApplicId__c(
    @Param('filterBy') filterBy: string,
    @Param('id') id: string,
    @Req() request: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getOpportunityDetailsById(
      id,
      request,
      filterBy,
    );
  }
  @Post('/gus/getOpportunitiesByEmail')
  async opportunitiesByEmail(
    @Body() event,
    @Req() request: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getOpportunitiesDetailsByEmail(
      event,
      request,
    );
  }
  @Get('/gus/getdocumenttype/:brand')
  async documentTypes(@Param('brand') brand: string): Promise<any> {
    try {
      return await this.gusSalesforceProcessService.getDocumentTypesByBrand(
        brand,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Post('/gus/getprogrammeInstitutions')
  async allInstitutions(@Req() req: Request, @Body() event): Promise<any> {
    return await this.gusSalesforceProcessService.allInstitutionsByProgram(
      req,
      event,
    );
  }
  @Post('/gus/getprogrammes')
  async listPrograms(@Body() requestBody, @Req() req: Request): Promise<any> {
    try {
      return await this.gusSalesforceProcessService.getAllPrograms(
        requestBody,
        req,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/gus/compareprogrammes')
  async comparePrograms(
    @Query() event: any,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.compareProgrammes(event, req);
  }
  @Get('/gus/getprogramme/:programmeId')
  async programDetails(
    @Param('programmeId') programmeId: string,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgramDetails(
      programmeId,
      req,
    );
  }
  @Get('/gus/getopportunityfilesbydocumenttype/:id/:documenttype')
  async opportunityFilesType(
    @Param('id') id: string,
    @Param('documenttype') documentType: string,
    @Req() request: Request,
  ): Promise<any> {
    const response =
      await this.gusSalesforceProcessService.getOpportunityFilesByDocumentType(
        id,
        documentType,
        request,
      );
    return { response };
  }
  @Get('/gus/getopportunityfiles/:id')
  async opportunityFiles(
    @Param('id') id: string,
    @Req() request: Request,
  ): Promise<any> {
    const response = await this.gusSalesforceProcessService.getOpportunityFiles(
      id,
      request,
    );
    return { response };
  }
  @Get('/gus/getpricebookdetails/:programmeId')
  async programPriceBookDetails(
    @Param('programmeId') programmeId: string,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgrammePriceBookDetails(
      programmeId,
      req,
    );
  }
  @Get('/gus/getprogrammebylevel/:level')
  async getProgrammeLevel(
    @Param('level') level: string,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgrammeByLevel(
      level,
      req,
    );
  }
  @Get('/gus/getintakebyprogramme/:programme')
  async getIntakeByProgramme(
    @Param('programme') programme: string,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgrammeDetailsById(
      programme,
      req,
    );
  }
  @Get('/gus/getprogrammedetailsbyid/:programme')
  async getProgrammeDetails(
    @Param('programme') programme: string,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgrammeDetailsById(
      programme,
      req,
    );
  }
  @Get('/gus/getlocations')
  async getLocationsByBrand(
    @Req() req: Request,
    @Query('programId') programId: string,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getLocations(req, programId);
  }
  @Get('/gus/getapplicationbyopportunity/:id')
  async getApplicationByOpportunity(
    @Param('id') id: string,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getApplicationByOpportunity(
      id,
      req,
    );
  }
  @Post('/gus/getprogrammedetailsbyid/:programme')
  async getProgrammeDetailsById(
    @Param('programme') programme: string,
    @Body() filters: any,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getProgrammeFilteredDetailsById(
      programme,
      req,
      filters,
    );
  }
  @Patch('/accounts')
  async updatePersonAccountConsent(
    @Body() event: any,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.updatePersonAccount(
      event,
      req,
    );
  }
  @Post('/gus/persistapplication')
  async persistApplication(
    @Body() event,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.gusApplicationProcessService.persistApplication(
        event,
        request,
      );
    } catch (error) {
      throw error;
    }
  }
  @Post('/gus/ulaw/persistapplication')
  async ulawPersistApplication(
    @Body() event,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.gusApplicationProcessService.persistUlawApplication(
        event,
        request,
      );
    } catch (error) {
      throw error;
    }
  }
  @Post('/gus/persiststudentapplication')
  async persistStudentApplication(
    @Body() event,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.gusApplicationProcessService.persistStudentApplication(
        event,
        request,
      );
    } catch (error) {
      throw error;
    }
  }
  @Get('/gus/getapplicationsdetails/:opportunityId')
  async getApplicationDetails(
    @Param('opportunityId') opportunityId: string,
    @Req() req: Request,
    @Query('scenario') scenario?: string,
  ): Promise<any> {
    return await this.gusApplicationProcessService.getApplicationDetailsByOpportunityAndApplicationId(
      opportunityId,
      req,
      scenario,
    );
  }
  @Post('/gus/publishrejecteddocumentevent')
  async publishRejectedDocumentEvent(
    @Body() payload,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.publishRejectedDocumentEvent(
      payload,
    );
  }
  @Get('/gus/geteducationhistory')
  async getEducationHistoryByName(
    @Query() event: any,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getEducationHistoryByInstitutionName(
      event,
      req,
    );
  }
  @Get('/gus/geteducationhistorybyoppid/:opportunityId')
  async getEducationHistoryByOppId(
    @Param('opportunityId') opportunityId: string,
    @Req() req: Request,
  ): Promise<any> {
    return await this.gusSalesforceProcessService.getEducationHistoryByOpportunityId(
      opportunityId,
      req,
    );
  }

  @Patch('/gus/updateaccount')
  async updateAccount(@Body() event: any, @Req() req: Request): Promise<any> {
    try {
      return await this.gusSalesforceProcessService.updateAccountDetails(
        event,
        req,
      );
    } catch (error) {
      throw new InternalServerErrorException(error.message);
    }
  }
  @Patch('gus/updateLead')
  async updateLead(@Body() event: any, @Req() req: Request): Promise<any> {
    try {
      return await this.gusApplicationProcessService.updateLeadIfNotConverted(
        event,
        req,
      );
    } catch (error) {
      throw new InternalServerErrorException(error.message);
    }
  }
  @Post('/gus/sobject')
  async getEntityDetails(
    @Body() queryParams: Product2QueryDto,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const { object, customConditions, groupBy, entityName } = queryParams;
      return await this.gusSalesforceProcessService.getEntityDetails(
        request,
        object,
        customConditions,
        groupBy,
        entityName,
      );
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: error.message || 'Failed to fetch object details',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
