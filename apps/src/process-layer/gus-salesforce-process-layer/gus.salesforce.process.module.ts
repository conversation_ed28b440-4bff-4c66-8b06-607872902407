import { Modu<PERSON> } from '@nestjs/common';
import { GusSalesforceProcessController } from './gus.salesforce.process.controller';
import { GusSalesforceProcessService } from './gus.salesforce.process.service';
import { GusSalesforceSystemModule } from 'apps/src/system-layer/gus-salesforce/gus.salesforce.system.module';
import { GusApplicationProcessService } from './gus.application.service';
import { LoggerModule } from '@gus-eip/loggers';
import { ConfigModule } from '@nestjs/config';

@Module({
  providers: [
    GusSalesforceProcessController,
    GusSalesforceProcessService,
    GusApplicationProcessService,
  ],
  controllers: [GusSalesforceProcessController],
  exports: [GusSalesforceProcessController],
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    GusSalesforceSystemModule,
    LoggerModule.forRoot({
      region: process.env.REGION,
      logGroupName: process.env.LOGGER_LOG_GROUP_NAME,
      teamWebhookUrl: process.env.TEAMS_WEBHOOK_URL,
      isAlertNeeded: false,
      options: 'CloudWatchLogger',
    }),
  ],
})
export class GusSalesforceProcessModule {}
