import {
  Injectable,
  HttpStatus,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { GusSalesforceOpportunityService } from 'apps/src/system-layer/gus-salesforce/opportunity/opportunity.salesforce.service';
import * as salesForceHelper from '../../../libs/utils/utils';
import { GusSalesforceOpportunityDocumentTypeService } from 'apps/src/system-layer/gus-salesforce/opportunityDocumentType/opportunityDocumentType.service';
import { GusSalesforceProgrammeService } from 'apps/src/system-layer/gus-salesforce/programme/programme.service';
import { GusSalesforcePricebookEntryService } from 'apps/src/system-layer/gus-salesforce/pricebookEntry/pricebookEntry.service';
import { GusSalesforceProductService } from 'apps/src/system-layer/gus-salesforce/product2/product2.service';
import { GusSalesforceBusinessunitService } from 'apps/src/system-layer/gus-salesforce/businessunit/businessunit.service';
import { GusSalesforceOpportunityFileService } from 'apps/src/system-layer/gus-salesforce/opportunityFiles/opportunityFiles.service';
import { GusSalesforceAccountService } from 'apps/src/system-layer/gus-salesforce/account/account.service';
import { GusSalesforceCompositeService } from 'apps/src/system-layer/gus-salesforce/composite/composite.service';
import { Request } from 'express';
import { GusSalesforceApplicationService } from 'apps/src/system-layer/gus-salesforce/application/application.service';
import axios from 'axios';
import { GusSalesforceEducationHistoryService } from 'apps/src/system-layer/gus-salesforce/educationHistoryRecord/educationHistoryRecord.service';
import { GusSalesforceSobjectService  } from 'apps/src/system-layer/gus-salesforce/sobject/sobject.service';
@Injectable()
export class GusSalesforceProcessService {
  constructor(
    private readonly gusSalesforceOpportunityService: GusSalesforceOpportunityService,
    private readonly gusSalesforceProgrammeService: GusSalesforceProgrammeService,
    private readonly gusSalesforceOpportunityDocumentTypeService: GusSalesforceOpportunityDocumentTypeService,
    private readonly gusSalesforcePricebookEntryService: GusSalesforcePricebookEntryService,
    private readonly gusSalesforceProductService: GusSalesforceProductService,
    private readonly gusSalesforceBusinessunitService: GusSalesforceBusinessunitService,
    private readonly gusSalesforceOpportunityFileService: GusSalesforceOpportunityFileService,
    private readonly gusSalesforceAccountService: GusSalesforceAccountService,
    private readonly gusSalesforceCompositeService: GusSalesforceCompositeService,
    private readonly gusSalesforceApplicationService: GusSalesforceApplicationService,
    private readonly gusSalesforceEducationHistoryService: GusSalesforceEducationHistoryService,
    private readonly gusSalesforceSobjectService: GusSalesforceSobjectService,
  ) {}
  async getApplicationByOpportunity(id: string, req: unknown): Promise<any> {
    const applicationByOpportunityCondition = ` where Opportunity_B2C__c = '${id}'`;
    const application =
      await this.gusSalesforceApplicationService.getApplication(
        ' Agent_Contact__r.Email, Agent_Contact__r.Phone,Id, Agent_Contact__r.Name,Opportunity_B2C__c ',
        applicationByOpportunityCondition,
      );
    return application;
  }
  async getOpportunityDetailsById(id: string, request, filterBy?) {
    const entityName = 'Opportunity';
    const entityOperation = 'opportunityDetailsById';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    let opportunityDetailsByIdCondition;
    if (!filterBy) {
      opportunityDetailsByIdCondition = `WHERE Id='${id}'`;
    } else {
      opportunityDetailsByIdCondition = `WHERE ${filterBy}='${id}'`;
    }
    if (whereClause) {
      opportunityDetailsByIdCondition += `AND ${whereClause}`;
    }
    const opportunities =
      await this.gusSalesforceOpportunityService.getOpportunities(
        fieldsRequired,
        opportunityDetailsByIdCondition,
      );
    return opportunities;
  }
  async getProgramLanguagesByLevel(event: any, request) {
    const entityName = 'Product2';
    const entityOperation = 'programLanguagesById';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    let programLanguageCondition;
    const groupByQuery = ` group by Language__c`;
    if (whereClause) {
      programLanguageCondition = `WHERE Brand__c = ${event.brand} AND Programme__r.Level__c = ${event.level} AND ${whereClause}`;
    }
    programLanguageCondition += groupByQuery;
    console.log('Program Language Condition ->', programLanguageCondition);
    const languages = await this.gusSalesforceProductService.getProducts(
      fieldsRequired,
      programLanguageCondition,
    );
    return languages;
  }
  async getProgramByLanguage(event: any, request) {
    const entityName = 'Product2';
    const entityOperation = 'programByLanguage';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    let programByLanguageCondition;
    if (whereClause) {
      programByLanguageCondition = `WHERE Brand__c = ${event.brand} AND Language__c = ${event.language} AND Programme__r.Level__c = ${event.level} AND ${whereClause}`;
    }
    const programmes = await this.gusSalesforceProductService.getProducts(
      fieldsRequired,
      programByLanguageCondition,
    );
    return programmes;
  }
  async getProgramLocationByProgramDetails(event: any, request) {
    const entityName = 'Product2';
    const entityOperation = 'programLocationByProgramDetails';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    const groupByQuery = ` group by Location__c`;
    let programLocationCondition;
    if (whereClause) {
      programLocationCondition = `WHERE Brand__c = ${event.brand} AND Programme__c = ${event.program} AND Language__c = ${event.language} AND Programme__r.Level__c = ${event.level} AND ${whereClause}`;
    }
    programLocationCondition += groupByQuery;
    const locations = await this.gusSalesforceProductService.getProducts(
      fieldsRequired,
      programLocationCondition,
    );
    return locations;
  }
  async getProgramDurationByProgramDetails(event: any, request) {
    const entityName = 'Product2';
    const entityOperation = 'programDurationByProgramDetails';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    let programDurationCondition;
    if (whereClause) {
      programDurationCondition = `WHERE Brand__c = ${event.brand} AND Programme__c = ${event.program} AND Location__c = ${event.location} AND Language__c = ${event.language} AND Programme__r.Level__c = ${event.level} AND ${whereClause}`;
    }
    const durationInfo = await this.gusSalesforceProductService.getProducts(
      fieldsRequired,
      programDurationCondition,
    );
    return durationInfo;
  }
  async getProgramIntakeByProgramDetails(event: any, request) {
    const entityName = 'Product2';
    const entityOperation = 'programIntakeByProgramDetails';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    let programIntakeCondition;
    if (whereClause) {
      programIntakeCondition = `WHERE Brand__c = ${event.brand} AND Programme__c = ${event.program} AND Duration__c = ${event.duration} AND Location__c = ${event.location} AND Language__c = ${event.language} AND Programme__r.Level__c = ${event.level} AND ${whereClause}`;
    }
    const intakeInfo = await this.gusSalesforceProductService.getProducts(
      fieldsRequired,
      programIntakeCondition,
    );
    return intakeInfo;
  }

  getOpportunitiesDetailsByEmail: any = async (event, request) => {
    const entityName = 'Opportunity';
    const entityOperation = 'opportunityDetailsByEmail';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    let opportunityDetailsByEmailCondition = `WHERE AccountEmail__c='${event.email}' and RecordType.name!='Agent Onboarding' and RecordType.name!='Default'`;
    if (whereClause) {
      opportunityDetailsByEmailCondition += `AND ${whereClause}`;
    }
    console.log(fieldsRequired, opportunityDetailsByEmailCondition);
    const opportunities =
      await this.gusSalesforceOpportunityService.getOpportunities(
        fieldsRequired,
        opportunityDetailsByEmailCondition,
      );
    return opportunities;
  };
  async getDocumentTypesByBrand(brand) {
    let documentTypes;
    try {
      documentTypes =
        await this.gusSalesforceOpportunityDocumentTypeService.getOpportunityDocumentTypes(
          'DocumentType__c',
          `where Brand__c = '${brand}' and Required__c = false`,
          true,
        );
    } catch (error) {
      throw error;
    }
    return documentTypes;
  }
  async getProgrammeByLevel(level, request): Promise<any> {
    const entityName = 'Programme__c';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
    );
    let programmeCondition = ` where Level__c = '${level}' `;
    if (whereClause) {
      programmeCondition += ` and ${whereClause}`;
    }
    return await this.gusSalesforceProgrammeService.getProgrammes(
      fieldsRequired,
      programmeCondition,
    );
  }
  async publishRejectedDocumentEvent(eventPayload): Promise<any> {
    try {
      const clientId = process.env.GUS_CONSUMER_KEY;
      const clientSecret = process.env.GUS_CONSUMER_SECRET;
      const username = process.env.GUS_USER_NAME;
      const password = process.env.GUS_PASSWORD;
      const loginUrl = process.env.GUS_AUTH_URL;

      const tokenResponse = await axios.post(`${loginUrl}`, null, {
        params: {
          grant_type: 'password',
          client_id: clientId,
          client_secret: clientSecret,
          username: username,
          password: password,
        },
      });

      const accessToken = tokenResponse.data.access_token;
      const instanceUrl = tokenResponse.data.instance_url;

      const pubsubUrl = `${instanceUrl}/services/data/v61.0/sobjects/GUS_SF_Document_Update__e`;
      const response = await axios.post(pubsubUrl, eventPayload, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      return response.data;
    } catch (error) {
      console.log('Error', error);
      return error;
    }
  }

  async getProgrammeDetailsById(Programme, request): Promise<any> {
    const entityName = 'Product2';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
    );
    let intakeCondition = ` where Programme__c = '${Programme}' `;
    if (whereClause) {
      intakeCondition += ` and ${whereClause}`;
    }
    return await this.gusSalesforceProductService.getProducts(
      fieldsRequired,
      intakeCondition,
    );
  }
  async getLocations(request, programId = null): Promise<any> {
    const entityName = 'Product2';
    const object = 'getLocations';
    let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      object,
    );
    console.log(whereClause);
    if (programId) {
      whereClause = `${whereClause} and Programme__c = '${programId}'`;
    }
    const intakeCondition = `where ${whereClause} group by Location__c `;
    return await this.gusSalesforceProductService.getProducts(
      fieldsRequired,
      intakeCondition,
    );
  }
  async getProgrammeFilteredDetailsById(
    programmeId,
    request,
    filters,
  ): Promise<any> {
    const entityName = 'Product2';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
    );
    let productsFilterCondition = `where Programme__c = '${programmeId}'`;
    if (Object.keys(filters).length > 0) {
      for (const filter in filters) {
        if (
          typeof filters[filter] === 'number' &&
          Number.isInteger(filters[filter])
        ) {
          productsFilterCondition += ` and ${filter} = ${filters[filter]}`;
        } else {
          const filterValue =
            typeof filters[filter] === 'string'
              ? `'${filters[filter]}'`
              : filters[filter];
          productsFilterCondition += ` and ${filter} = ${filterValue}`;
        }
      }
      if (whereClause) {
        productsFilterCondition += ` and ${whereClause} `;
      }
    }

    return await this.gusSalesforceProductService.getProducts(
      fieldsRequired,
      productsFilterCondition,
    );
  }
  async getProgrammePriceBookDetails(
    programeId,
    request: Request,
  ): Promise<any> {
    const entityName = 'PricebookEntry';
    const object = 'pricebookDetails';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      object,
    );
    let pricebookDetailsCondition = `where Product2.Programme__c ='${programeId}' and  Product2.Intake__c >= TODAY `;
    if (whereClause) {
      pricebookDetailsCondition += ` and ${whereClause}`;
    }
    const pricebookDetailsResponse =
      await this.gusSalesforcePricebookEntryService.getPricebookEntry(
        fieldsRequired,
        pricebookDetailsCondition,
        true,
      );
    const pricebookDetails = pricebookDetailsResponse?.records;
    const priceEntries = pricebookDetails.map((pricebook) => ({
      location: pricebook.Product2?.Location__c,
      duration: pricebook.Product2?.DurationText__c,
      intakeDate: pricebook.Product2?.Intake__c,
      price: pricebook?.UnitPrice,
      priceBookName: pricebook?.Pricebook2.Name,
      currencyCode: pricebook?.CurrencyIsoCode,
    }));
    return {
      priceEntries: priceEntries,
      total: pricebookDetailsResponse?.totalSize,
    };
  }
  async compareProgrammes(idsList, request: Request): Promise<any> {
    try {
      const entityName = 'PricebookEntry';
      const object = 'programmeDetails';
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          entityName,
          request['allowedEntities'],
          object,
        );
      idsList = idsList.ids.split(',');
      const ids = `('${idsList.join("','")}')`;
      // const filterProgramByIdQuery = `SELECT+${fieldsRequired}+FROM+${entityName}+where Id in ${ids}`;
      let filterProgramByIdQuery = ` WHERE Product2.Programme__c IN ${ids}`;
      const groupByQuery = ` group by Product2.Programme__r.Name,Product2.Programme__r.School__r.Name,Product2.Programme__r.Subject__r.Name,Product2.Programme__r.Id,Product2.Programme__r.CurrencyIsoCode,Product2.Programme__r.Level__r.Name,Product2.Programme__r.School__r.Brand__c`;
      if (whereClause) {
        filterProgramByIdQuery += ` and ${whereClause}`;
      }
      filterProgramByIdQuery += groupByQuery;
      const allProgramsResponse =
        await this.gusSalesforcePricebookEntryService.getPricebookEntry(
          fieldsRequired,
          filterProgramByIdQuery,
        );
      const programmeDetails = [];
      const locationsObject = 'comparePrograms';
      const locationsAndDeliveryQuery = await salesForceHelper.queryBuilder(
        'Product2',
        request['allowedEntities'],
        locationsObject,
      );
      for (const program of allProgramsResponse) {
        let locationCondition = ` WHERE Programme__c = '${program?.Id}' and Location__c != null`;

        if (locationsAndDeliveryQuery?.['whereClause']) {
          locationCondition += ` and ${locationsAndDeliveryQuery['whereClause']}`;
        }
        // locationQuery += locationGroupByQuery;
        const productLocationsAndDelivery =
          await this.gusSalesforceProductService.getProducts(
            locationsAndDeliveryQuery?.['fieldsRequired'],
            locationCondition,
          );
        const distinctLocations = Array.from(
          new Set(productLocationsAndDelivery.map((item) => item.Location__c)),
        );
        const distinctDelivery = Array.from(
          new Set(
            productLocationsAndDelivery.map((item) => item?.Delivery__r?.Name),
          ),
        );
        const online = distinctLocations?.includes('Online');
        const filteredLocations = distinctLocations?.filter(
          (location) => location !== 'Online',
        );
        const programDetails = {
          Id: program?.Id,
          brand: program?.Brand__c,
          Name: program?.Name,
          minDuration: program?.minDuration,
          maxDuration: program?.maxDuration,
          minFees: program?.minPrice,
          maxFees: program?.maxPrice,
          location: filteredLocations,
          delivery: distinctDelivery,
          online: online,
          institution: program?.school,
          institutionCurrencyIsoCode: program?.CurrencyIsoCode,
          subject: program?.subject,
          level: program?.level,
          brandLogo:
            'https://dev-userprofileimages-bucket.s3.ap-south-1.amazonaws.com/gus-middleware-institution-logos/UCW.png',
        };
        programmeDetails.push(programDetails);
      }
      return programmeDetails;
    } catch (error) {
      console.log(error);
      return [];
    }
  }
  async allInstitutionsByProgram(request, event): Promise<any> {
    let allInstitutionsQuery = '';
    const entityName = 'Programme__c';
    const object = 'institutions';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      object,
    );
    let limit = event?.filterQuery?.size ? event?.filterQuery?.size : 10;
    const offset = event?.filterQuery?.from ? event?.filterQuery?.from : 0;
    limit = limit + offset;
    const filters = event?.filterQuery?.filters;
    let queryCondition = await this.filterQueryBuilder(filters);
    // const limitQuery = ` limit ${limit} offset ${offset}`;
    const groupByQuery = ` GROUP BY School__r.Brand__c`;
    if (whereClause) {
      allInstitutionsQuery += ` where ${whereClause}`;
    }
    allInstitutionsQuery += groupByQuery;
    const allInstitutionDetails =
      await this.gusSalesforceProgrammeService.getProgrammes(
        fieldsRequired,
        allInstitutionsQuery,
        true,
      );
    const allInstitutions = allInstitutionDetails?.records;
    const priceBookEntryQueryDetails = await salesForceHelper.queryBuilder(
      'PricebookEntry',
      request?.allowedEntities,
      object,
    );
    const priceBookEntryQuery = `Select ${priceBookEntryQueryDetails?.fieldsRequired} from PricebookEntry`;
    if (queryCondition && priceBookEntryQueryDetails.whereClause) {
      queryCondition += ` and ${priceBookEntryQueryDetails.whereClause}`;
    } else if (priceBookEntryQueryDetails.whereClause) {
      queryCondition = ` where ${priceBookEntryQueryDetails.whereClause}`;
    }
    const compositeQueryLists =
      await this.buildInstitutionCountCompositeRequestQuery(
        priceBookEntryQuery,
        allInstitutions,
        queryCondition,
        `group by Product2.Programme__r.Id`,
      );
    const compositeQueryResponse = [];
    for (const compositeQueryList of compositeQueryLists) {
      const response =
        await this.gusSalesforceCompositeService.compositeRequest(
          compositeQueryList,
        );
      compositeQueryResponse.push(...response?.compositeResponse);
    }
    const referenceIdToTotalSize = [];
    for (const institutionsByProgramCount of compositeQueryResponse) {
      if (institutionsByProgramCount.body?.totalSize > 0) {
        referenceIdToTotalSize.push({
          total: institutionsByProgramCount.body?.totalSize,
          Brand__c: institutionsByProgramCount.referenceId.replace(/_/g, ' '),
        });
      }
    }
    return {
      institutions: referenceIdToTotalSize.slice(offset, limit),
      total: referenceIdToTotalSize.length,
    };
  }
  async buildInstitutionCountCompositeRequestQuery(
    query,
    brands,
    condition,
    groupby,
  ): Promise<any> {
    const institutionCountCompositeRequestQueryList = [];
    for (const institutions of brands) {
      const request = {
        method: 'GET',
        url: `/services/data/${process.env.SALESFORCE_API_VERSION}/query?q=${query} ${condition} and Product2.Programme__r.School__r.Brand__c = '${institutions.Brand__c}' ${groupby}`,
        referenceId: institutions.Brand__c.replace(/ /g, '_'),
      };
      institutionCountCompositeRequestQueryList.push(request);
    }
    const compositeSubLists = await this.divideListIntoSublists(
      institutionCountCompositeRequestQueryList,
      25,
    );
    return compositeSubLists;
  }
  async divideListIntoSublists(items, sublistSize) {
    const dividedLists = [];
    for (let i = 0; i < items.length; i += sublistSize) {
      const sublist = items.slice(i, i + sublistSize);
      dividedLists.push(sublist);
    }
    return dividedLists;
  }

  async getAllPrograms(event, request): Promise<any> {
    try {
      const allProgramesDetails = [];
      const filters = event?.filterQuery?.filters;
      let allProgramesQuery = '';
      const entityName = 'PricebookEntry';
      const object = 'programmeDetails';
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          entityName,
          request?.allowedEntities,
          object,
        );
      const groupByQuery = ` group by Product2.Programme__r.Name,Product2.Programme__r.School__r.Name,Product2.Programme__r.Id,Product2.Programme__r.Level__r.Name,Product2.Programme__r.Subject__r.Name,Product2.Programme__r.CurrencyIsoCode,Product2.Programme__r.School__r.Brand__c`;
      const condition = await this.filterQueryBuilder(filters);
      let queryCondition = '';
      if (condition) {
        queryCondition = encodeURIComponent(condition);
      }
      const countQueryFields = `count(Id)`;
      let countQuery = '';
      if (queryCondition && !whereClause) {
        queryCondition += `and Product2.Programme__r.Id != null`;
        allProgramesQuery += queryCondition;
        countQuery += queryCondition;
      } else if (!queryCondition && whereClause) {
        queryCondition = ` where Product2.Programme__r.Id != null AND ${whereClause} `;
        allProgramesQuery += queryCondition;
        countQuery += queryCondition;
      } else if (queryCondition && whereClause) {
        queryCondition += `and Product2.Programme__r.Id != null and ${whereClause} `;
        allProgramesQuery += queryCondition;
        countQuery += queryCondition;
      } else if (!queryCondition && !whereClause) {
        queryCondition = ` where Product2.Programme__r.Id != null`;
        allProgramesQuery += queryCondition;
        countQuery += queryCondition;
      }
      countQuery += groupByQuery + ` limit 2000`;
      const count =
        await this.gusSalesforcePricebookEntryService.getPricebookEntry(
          countQueryFields,
          countQuery,
          true,
        );
      allProgramesQuery += groupByQuery;
      const limitQuery = event?.filterQuery?.size
        ? ` limit ${event?.filterQuery?.size}`
        : ` limit 5`;
      const offsetQuery = event?.filterQuery?.from
        ? ` offset ${event?.filterQuery?.from}`
        : ` offset 0`;
      allProgramesQuery += limitQuery + offsetQuery;
      const allProgramesResponse =
        await this.gusSalesforcePricebookEntryService.getPricebookEntry(
          fieldsRequired,
          allProgramesQuery,
          true,
        );
      const allProgrames = allProgramesResponse?.records;
      let programIds = allProgrames
        .map((program) => `'${program.Id}'`)
        .join(', ');
      if (!programIds) {
        return {
          programList: [],
          total: 0,
        };
      }
      programIds = `(${programIds})`;
      const locationQuery = await salesForceHelper.queryBuilder(
        'Product2',
        request?.allowedEntities,
        'listPrograms',
      );
      let locationsQueryCondition = `where programme__c in ${programIds}`;
      if (locationQuery?.whereClause) {
        locationsQueryCondition += ` and ${locationQuery?.whereClause}`;
      }
      const locationGroupByQuery = ` group by location__c,programme__c`;
      locationsQueryCondition += locationGroupByQuery;
      let locations = await this.gusSalesforceProductService.getProducts(
        locationQuery.fieldsRequired,
        locationsQueryCondition,
        true,
      );
      locations = locations.records;
      const programmeWithLocations = {};

      locations.forEach((record) => {
        const programme = record.Programme__c;
        const location = record.Location__c;

        if (location && programme) {
          if (!programmeWithLocations.hasOwnProperty(programme)) {
            programmeWithLocations[programme] = [];
          }

          if (!programmeWithLocations[programme].includes(location)) {
            programmeWithLocations[programme].push(location);
          }
        }
      });
      for (const program of allProgrames) {
        const programeDetails = {
          id: program?.Id,
          brand: program?.Brand__c,
          brandFullName: program?.school,
          name: program?.Name,
          price: program?.maxPrice,
          // eslint-disable-next-line prettier/prettier
          locations: programmeWithLocations?.[program?.Id]
            ? programmeWithLocations?.[program?.Id]
            : null,
          minDuration: program?.minDuration,
          maxDuration: program?.maxDuration,
          level: program?.level,
          subject: program?.subject,
          currencyCode: program?.CurrencyIsoCode,
          brandLogo:
            'https://dev-userprofileimages-bucket.s3.ap-south-1.amazonaws.com/gus-middleware-institution-logos/UCW.png',
        };
        allProgramesDetails.push(programeDetails);
      }
      return {
        programList: allProgramesDetails,
        total: count.totalSize,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  async getProgramDetails(programeId, request: Request): Promise<any> {
    const entityName = 'Programme__c';
    const object = 'programmeDetails';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      object,
    );
    let programDetailsByIdCondition = `Where Id = '${programeId}'`;
    if (whereClause) {
      programDetailsByIdCondition += `and ${whereClause}`;
    }
    const progrmDetails =
      await this.gusSalesforceProgrammeService.getProgrammes(
        fieldsRequired,
        programDetailsByIdCondition,
      );
    const productEntityName = 'Product2';
    const productObject = 'programmeDetails';
    const productQuery = await salesForceHelper.queryBuilder(
      productEntityName,
      request['allowedEntities'],
      productObject,
    );
    let locationAndDurationCondition = `WHERE Programme__c = '${programeId}'`;
    if (productQuery.whereClause) {
      locationAndDurationCondition += ` and ${productQuery.whereClause}`;
    }
    const locationsAndDurations =
      await this.gusSalesforceProductService.getProducts(
        productQuery?.fieldsRequired,
        locationAndDurationCondition,
      );
    const distinctDurations = Array.from(
      new Set(locationsAndDurations.map((item) => item.DurationText__c)),
    );
    const distinctLocations = Array.from(
      new Set(locationsAndDurations.map((item) => item.Location__c)),
    );
    const distinctDelivery = Array.from(
      new Set(locationsAndDurations.map((item) => item?.Delivery__r?.Name)),
    );
    const brandEntityName = 'BusinessUnit__c';
    const businessUnitQuery = await salesForceHelper.queryBuilder(
      brandEntityName,
      request['allowedEntities'],
    );
    let UPCDetailsCondition = `WHERE Brands__c = '${progrmDetails[0].School__r.Brand__c}'`;
    if (businessUnitQuery.whereClause) {
      UPCDetailsCondition += ` and ${businessUnitQuery.whereClause}`;
    }
    const UPCDetails =
      await this.gusSalesforceBusinessunitService.getBusinessUnits(
        businessUnitQuery.fieldsRequired,
        UPCDetailsCondition,
      );
    const details = {
      Id: progrmDetails[0]?.Id,
      brand: progrmDetails[0]?.School__r?.Brand__c,
      brandFullName: progrmDetails[0]?.School__r?.Name,
      name: progrmDetails[0]?.Name,
      level: progrmDetails[0]?.Level__r?.Name,
      subject: progrmDetails[0]?.Subject__r?.Name,
      location: distinctLocations,
      duration: distinctDurations,
      delivery: distinctDelivery,
      description: progrmDetails[0]?.Programme_Description__c,
      usp1Title: UPCDetails[0]?.USP_1_Title__c,
      usp1Description: UPCDetails[0]?.USP_1_Description__c,
      usp2Title: UPCDetails[0]?.USP_2_Title__c,
      usp2Description: UPCDetails[0]?.USP_2_Description__c,
      usp3Title: UPCDetails[0]?.USP_3_Title__c,
      usp3Description: UPCDetails[0]?.USP_3_Description__c,
      uspVideoLink: UPCDetails[0]?.USP_Video_Link__c,
    };
    return details;
  }
  async filterQueryBuilder(filters): Promise<any> {
    let queryCondition;
    let filterDates;
    if (filters) {
      let count = 0;
      queryCondition = ` where`;
      for (const filterField in filters) {
        switch (filterField) {
          case 'Institution':
            const formattedInstitutionCondition = `(${filters.Institution.values
              .map((value) => `'${value}'`)
              .join(', ')})`;
            if (count > 0) {
              queryCondition += ` and Product2.Programme__r.School__r.Brand__c IN ${formattedInstitutionCondition}`;
            } else {
              queryCondition += ` Product2.Programme__r.School__r.Brand__c IN ${formattedInstitutionCondition}`;
            }
            count = count + 1;
            break;
          case 'Level':
            const formattedLevelCondition = `(${filters.Level.values
              .map((value) => `'${value}'`)
              .join(', ')})`;
            if (count > 0) {
              queryCondition += ` and Product2.Programme__r.Level__r.Name IN ${formattedLevelCondition}`;
            } else {
              queryCondition += ` Product2.Programme__r.Level__r.Name IN ${formattedLevelCondition}`;
            }
            count = count + 1;
            break;
          case 'Subject':
            const formattedSubjectCondition = `(${filters.Subject.values
              .map((value) => `'${value}'`)
              .join(', ')})`;
            if (count > 0) {
              queryCondition += ` and Product2.Programme__r.Subject__r.Name IN ${formattedSubjectCondition}`;
            } else {
              queryCondition += ` Product2.Programme__r.Subject__r.Name IN ${formattedSubjectCondition}`;
            }
            count = count + 1;
            break;
          case 'Intake':
            const filterDatesList = [];
            for (const intakeDate of filters?.Intake?.values) {
              const [month, year] = intakeDate.split(' ');
              const monthNumber = this.months[month];
              const formattedDate = `(CALENDAR_YEAR(Product2.Intake__c) = ${year} AND CALENDAR_MONTH(Product2.Intake__c) = ${monthNumber})`;
              filterDatesList.push(formattedDate);
            }
            filterDates = filterDatesList.join(' OR ');
            filterDates = `(${filterDates})`;
            if (count > 0) {
              queryCondition += `and ${filterDates}`;
            } else {
              queryCondition += ` ${filterDates}`;
            }
            count = count + 1;
            break;
          case 'School':
            const formattedSchoolCondition = `(${filters.School.values
              .map((value) => `'${value}'`)
              .join(', ')})`;
            if (count > 0) {
              queryCondition += ` and Product2.Programme__r.School__r.Name IN ${formattedSchoolCondition}`;
            } else {
              queryCondition += ` Product2.Programme__r.School__r.Name IN ${formattedSchoolCondition}`;
            }
            count = count + 1;
            break;
          default:
            if (count > 0) {
              queryCondition = ` where Product2.Intake__c >= TODAY `;
            }
        }
      }
    }
    return queryCondition;
  }
  async getOpportunityFilesByDocumentType(
    id,
    documentType,
    request,
  ): Promise<any> {
    const entityName = 'OpportunityFile__c';
    const entityOperation = 'opportunityFilesByDocumentType';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );

    let fileCondition =
      documentType === 'Degree docs'
        ? `WHERE Opportunity__c = '${id}' AND DocumentType__c IN ('Degree transcripts','Degree certificate') `
        : `WHERE Opportunity__c = '${id}' AND DocumentType__c = '${documentType}' `;
    if (whereClause) {
      fileCondition += `AND ${whereClause}`;
    }
    fileCondition += ' ORDER BY CreatedDate DESC';
    const opportunityFiles =
      await this.gusSalesforceOpportunityFileService.getOpportunityFiles(
        fieldsRequired,
        fileCondition,
      );

    console.log(opportunityFiles);
    return opportunityFiles;
  }
  async getOpportunityFiles(id, request): Promise<any> {
    const entityName = 'OpportunityFile__c';
    const entityOperation = 'opportunityFilesById';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    let fileCondition = `WHERE Opportunity__c = '${id}'`;
    if (whereClause) {
      fileCondition += `AND ${whereClause}`;
    }
    const opportunityFiles =
      await this.gusSalesforceOpportunityFileService.getOpportunityFiles(
        fieldsRequired,
        fileCondition,
      );
    return opportunityFiles;
  }
  months = {
    January: '1',
    February: '2',
    March: '3',
    April: '4',
    May: '5',
    June: '6',
    July: '7',
    August: '8',
    September: '9',
    October: '10',
    November: '11',
    December: '12',
  };
  async filterInstitutionsQueryBuilder(filters): Promise<any> {
    let queryCondition;
    if (filters && Object.keys(filters).length !== 0) {
      let count = 0;
      queryCondition = ` where`;
      for (const filterField in filters) {
        switch (filterField) {
          case 'Institution':
            const formattedInstitutionCondition = `(${filters.Institution.values
              .map((value) => `'${value}'`)
              .join(', ')})`;
            if (count > 0) {
              queryCondition += ` and School__r.Brand__c IN ${formattedInstitutionCondition}`;
            } else {
              queryCondition += ` School__r.Brand__c IN ${formattedInstitutionCondition}`;
            }
            count = count + 1;
            break;
          case 'Level':
            const formattedLevelCondition = `(${filters.Level.values
              .map((value) => `'${value}'`)
              .join(', ')})`;
            if (count > 0) {
              queryCondition += ` and Level__r.Name IN ${formattedLevelCondition}`;
            } else {
              queryCondition += ` Level__r.Name IN ${formattedLevelCondition}`;
            }
            count = count + 1;
            break;
          case 'Subject':
            const formattedSubjectCondition = `(${filters.Subject.values
              .map((value) => `'${value}'`)
              .join(', ')})`;
            if (count > 0) {
              queryCondition += ` and Subject__r.Name IN ${formattedSubjectCondition}`;
            } else {
              queryCondition += ` Subject__r.Name IN ${formattedSubjectCondition}`;
            }
            count = count + 1;
            break;
        }
      }
    }
    return queryCondition;
  }
  async updatePersonAccount(accountDetails, request): Promise<any> {
    try {
      const entityName = 'Opportunity';
      const object = 'checkSignUp';
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          entityName,
          request['allowedEntities'],
          object,
        );
      const response =
        await this.gusSalesforceOpportunityService.getOpportunities(
          fieldsRequired,
          ` where ${whereClause} and AccountEmail__c = '${accountDetails?.email}'`,
          true,
        );
      if (response.totalSize > 0) {
        const personEmail = encodeURIComponent(accountDetails.email);
        let personAccountsDetails, accountUpdateCompositeRequest;
        if (!accountDetails.Ids || accountDetails.Ids?.length === 0) {
          accountDetails.Ids = [];
          personAccountsDetails =
            await this.gusSalesforceAccountService.getPersonAccount(
              personEmail,
              'Id,PersonEmail, RecordTypeId, App_Hero_Consent__c, App_Hero_Can_Apply__c',
            );

          personAccountsDetails.forEach((account) => {
            accountDetails.Ids.push(account.Id);
          });
        } else {
          personAccountsDetails = { alreadyExist: true };
        }

        if (accountDetails.Ids?.length != 0) {
          accountUpdateCompositeRequest = accountDetails.Ids?.map((id) => ({
            method: 'PATCH',
            url: `/services/data/${process.env.SALESFORCE_API_VERSION}/sobjects/Account/${id}`,
            referenceId: id,
            body: {
              App_Hero_Consent__c: accountDetails.consent,
            },
          }));

          const response =
            await this.gusSalesforceCompositeService.compositeRequest(
              accountUpdateCompositeRequest,
            );

          console.log(
            'Person Accounts Consent Update Response',
            JSON.stringify(response),
          );

          if (
            response.compositeResponse.every(
              (response) => response.httpStatusCode === HttpStatus.NO_CONTENT,
            )
          ) {
            return personAccountsDetails;
          } else {
            throw new InternalServerErrorException(
              `Person Accounts Consent Update is Failed for this email - ${accountDetails.email}`,
            );
          }
        } else {
          personAccountsDetails = [];

          return personAccountsDetails;
        }
      } else {
        return [];
      }
    } catch (error) {
      console.log(
        'Person Accounts Consent Update is Failed with',
        JSON.stringify(error),
      );
      throw new InternalServerErrorException(
        `Person Accounts Consent Update is Failed for this email - ${accountDetails.email}`,
      );
    }
  }
  async getEducationHistoryByInstitutionName(event, request): Promise<any> {
    const entityName = 'EducationHistoryRecord__c';
    const entityOperation = 'EducationHistoryByInstitutionName';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    let condition = `WHERE InstitutionName__c ='${this.escapeSpecialCharacters(
      event.institutionName,
    )}' and Opportunity__c ='${event.opportunityId}'`;
    if (whereClause) {
      condition += ` AND ${whereClause}`;
    }
    try {
      const responses =
        await this.gusSalesforceEducationHistoryService.getEducationHistory(
          fieldsRequired,
          condition,
        );
      return responses?.[0];
    } catch (error) {
      throw error;
    }
  }
  async getEducationHistoryByOpportunityId(
    opportunityId,
    request,
  ): Promise<any> {
    const entityName = 'EducationHistoryRecord__c';
    const entityOperation = 'EducationHistoryByOpportunityId';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.allowedEntities,
      entityOperation,
    );
    let condition = `WHERE Opportunity__c ='${opportunityId}'`;
    if (whereClause) {
      condition += ` AND ${whereClause}`;
    }
    try {
      const responses =
        await this.gusSalesforceEducationHistoryService.getEducationHistory(
          fieldsRequired,
          condition,
        );
      return responses;
    } catch (error) {
      throw error;
    }
  }
  escapeSpecialCharacters(input) {
    return input.replace(/['"\\]/g, '\\$&');
  }

  async updateAccountDetails(event: any, request): Promise<any> {
    try {
      if (
        !event.email ||
        !event.businessUnitFilter ||
        !event.updateAccountDetails
      ) {
        throw Error('Missing Required Fields');
      }

      const { email, businessUnitFilter, updateAccountDetails } = event;

      const personAccounts =
        await this.gusSalesforceAccountService.getPersonAccount(
          encodeURIComponent(email),
          'Id',
          `BusinessUnitFilter__c = '${businessUnitFilter}'`,
        );

      if (!personAccounts || personAccounts.length === 0) {
        throw Error(
          `Person Account Not Found - ${event.email} and ${event.businessUnitFilter}`,
        );
      }

      for (const account of personAccounts) {
        const personAccountId = account?.Id;
        console.log('Processing personAccountId', personAccountId);

        if (!personAccountId) {
          console.warn(`Skipping account without an Id`);
          continue;
        }

        await this.gusSalesforceAccountService.updateAccount(
          updateAccountDetails,
          personAccountId,
        );
      }
      console.log('Complete - Updated Account details');
    } catch (error) {
      throw error;
    }
  }

  async getEntityDetails(
    request: any,
    object: string = null,
    customConditions: { [key: string]: any } = null,
    groupBy: string = null,
    entityName: string = null,
  ): Promise<any> {
    try {
      // Get fields and where clause from queryBuilder
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          entityName,
          request?.allowedEntities,
          object,
        );

      // Build the base condition
      let queryCondition = '';

      // Add where clause if exists
      if (whereClause) {
        queryCondition = `WHERE ${whereClause}`;
      }

      // Add custom conditions if provided
      if (customConditions && Object.keys(customConditions).length > 0) {
        const conditions = Object.entries(customConditions).map(
          ([field, value]) => {
            // Handle different value types
            const formattedValue =
              typeof value === 'string' ? `'${value}'` : value;

            return whereClause
              ? `AND ${field} = ${formattedValue}`
              : `WHERE ${field} = ${formattedValue}`;
          },
        );

        queryCondition += ` ${conditions.join(' ')}`;
      }

      // Add group by if provided
      if (groupBy) {
        queryCondition += ` GROUP BY ${groupBy}`;
      }

      // Call the service to get products
      const result = await this.gusSalesforceSobjectService.getObjectDetials(
        entityName,
        fieldsRequired,
        queryCondition,
      );
      return result;
    } catch (error) {
      throw new Error(`Failed to fetch Object details: ${error.message}`);
    }
  }
}
