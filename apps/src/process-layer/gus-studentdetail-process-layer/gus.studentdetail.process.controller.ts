import {
  <PERSON>,
  Get,
  Param,
  BadRequestException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { StudentDetailService } from './gus.studentdetails.process.service';

@Controller('')
export class StudentDetailController {
  constructor(private readonly studentDetailService: StudentDetailService) {}
  @Get('/profile/getstudentdetail/:email')
  async getStudentDetail(@Param('email') email: string): Promise<any> {
    try {
      if (email && email != null && email != undefined) {
        return await this.studentDetailService.getStudentDetail(email);
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
