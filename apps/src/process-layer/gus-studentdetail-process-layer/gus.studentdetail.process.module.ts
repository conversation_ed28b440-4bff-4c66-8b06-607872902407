import { Module } from '@nestjs/common';
import { StudentDetailController } from './gus.studentdetail.process.controller';
import { StudentDetailService } from './gus.studentdetails.process.service';
import { StudentDetailsModule } from 'apps/src/system-layer/gus-studentdetail/gus.studentdetails.module';

@Module({
  providers: [StudentDetailController, StudentDetailService],
  controllers: [StudentDetailController],
  exports: [StudentDetailController, StudentDetailService],
  imports: [StudentDetailsModule],
})
export class StudentDetailModule {}
