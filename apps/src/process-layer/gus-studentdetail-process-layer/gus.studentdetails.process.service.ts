import { Injectable } from '@nestjs/common';
import { StudentDetailsService } from 'apps/src/system-layer/gus-studentdetail/studentdetails/studentdetails.service';
import { StudentDocumentService } from 'apps/src/system-layer/gus-studentdetail/studentdocuments/studentdocument.service';
import * as AWS from 'aws-sdk';
const s3 = new AWS.S3();

@Injectable()
export class StudentDetailService {
  constructor(
    private readonly studentDetailsService: StudentDetailsService,
    private readonly studentDocumentService: StudentDocumentService,
  ) {}
  async getStudentDetail(email): Promise<any> {
    const studentdetail = await this.studentDetailsService.getStudentDetail(
      email,
    );
    const fieldsDeleteFromStudentdetail = [
      'PK',
      'SK',
      'progressPercentage',
      'createdAt',
    ];
    const fieldsDeleteFromStudentDocument = [
      'PK',
      'SK',
      'pathName',
      'createdAt',
      'email',
      's3FilePath',
    ];
    fieldsDeleteFromStudentdetail.forEach((key) => {
      delete studentdetail[key];
    });
    const fileTypeMappingWithSalesforce = {
      birthcertificate: 'Birth Certificate',
      testreport: 'Test Report',
      cv: 'CV',
      degreecertificate: 'Degree certificate',
      othercertificate: 'Other certification',
      leavingcertificate: 'Leaving Certificate',
      passport: 'ID/Passport',
    };
    const studentDocuments =
      await this.studentDocumentService.getStudentDocuments(email);
    for (const studentDocument of studentDocuments) {
      if ('s3FilePath' in studentDocument) {
        const bucket = process.env.STUDENT_DOC_S3_BUCKET;
        const s3FilePath = studentDocument?.s3FilePath;
        const params = {
          Bucket: bucket,
          Key: s3FilePath,
          Expires: 3600,
          ResponseContentDisposition: `attachment; filename=${studentDocument.path}`,
        };
        const filePath = await new Promise((resolve, reject) => {
          s3.getSignedUrl('getObject', params, (err, data) => {
            if (err) reject(err);
            else resolve(data);
          });
        });
        studentDocument['documentUrl'] = filePath;
      }
      fieldsDeleteFromStudentDocument.forEach((key) => {
        delete studentDocument[key];
      });
      if ('path' in studentDocument) {
        studentDocument.documentName = studentDocument.path;
        delete studentDocument.path;
      }
      if ('fileType' in studentDocument) {
        studentDocument.documentType =
          fileTypeMappingWithSalesforce[studentDocument.fileType];
        delete studentDocument.fileType;
      }
    }
    return { ...studentdetail, documents: studentDocuments };
  }
}
