import {
  <PERSON>,
  Body,
  Post,
  BadRequestException,
  ForbiddenException,
  Req,
} from '@nestjs/common';
import { HZUSalesforceProcessService } from './hzu.salesforce.service';
import { Request } from 'express';

@Controller('/salesforce/hzu')
export class HZUProcessController {
  constructor(
    private readonly HZUSalesforceProcessService: HZUSalesforceProcessService,
  ) { }
  @Post('/createapplication')
  async createApplication(
    @Body() requestBody,
    @Req() request: Request,
  ): Promise<any> {
    return await this.HZUSalesforceProcessService.createApplication(
      requestBody,
      request,
    );
  }
  @Post('/uploadfile')
  async uploadDocument(
    @Body() requestBody,
    @Req() request: Request,
  ): Promise<any> {
    const response =
      await this.HZUSalesforceProcessService.checkDocumentAlreadyExists(
        requestBody,
      );
    if (response && response?.ContentDocumentId) {
      requestBody.contentDocumentId = response.ContentDocumentId;
      return await this.HZUSalesforceProcessService.updateDocument(
        requestBody,
        request,
      );
    } else {
      return await this.HZUSalesforceProcessService.uploadDocument(
        requestBody,
        request,
      );
    }
  }

  @Post('/fetchanduploadfile')
  async fetchAndUploadDocument(
    @Body() body: { uploadDetails: any; additionalDetails: any },
    @Req() request: Request,
  ): Promise<any> {
    try {
      const { uploadDetails, additionalDetails } = body;
      console.log('uploadDetails', uploadDetails);
      console.log('additionalDetails', additionalDetails);
      const base64Data = await this.HZUSalesforceProcessService.getBase64File(
        additionalDetails.BucketName__c
          ? additionalDetails.BucketName__c
          : process.env.REVIEW_CENTER_BUCKET_NAME,
        additionalDetails.S3FileName__c,
        process.env.S3_CROSS_ACCESS_ROLE_ARN,
      );
      console.log('base64Data', base64Data);
      if (!base64Data) {
        throw new BadRequestException(
          'Base64 data is empty or could not be fetched.',
        );
      }

      uploadDetails.base64File = base64Data;
      console.log('UD -->', uploadDetails);

      const response =
        await this.HZUSalesforceProcessService.checkDocumentAlreadyExists(
          uploadDetails,
        );
      console.log('response', response);
      if (response?.ContentDocumentId) {
        uploadDetails.contentDocumentId = response.ContentDocumentId;
        return await this.HZUSalesforceProcessService.updateDocument(
          uploadDetails,
          request,
          additionalDetails.scenario
        );
      } else {
        return await this.HZUSalesforceProcessService.uploadDocument(
          uploadDetails,
          request,
          additionalDetails.scenario
        );
      }
    } catch (error) {
      console.log('Error -->', error);
      throw new ForbiddenException(error);
    }
  }
}
