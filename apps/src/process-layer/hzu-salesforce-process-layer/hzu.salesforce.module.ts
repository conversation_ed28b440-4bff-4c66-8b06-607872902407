import { Modu<PERSON> } from '@nestjs/common';
import { HZUProcessController } from './hzu.salesforce.controller';
import { HZUSalesforceProcessService } from './hzu.salesforce.service';
import { HZUSalesforceModule } from 'apps/src/system-layer/hzu-salesforce/hzu.salesforce.module';
import { LoggerModule } from '@gus-eip/loggers';
import { ConfigModule } from '@nestjs/config';

@Module({
  providers: [HZUProcessController, HZUSalesforceProcessService],
  controllers: [HZUProcessController],
  exports: [HZUProcessController],
  imports: [
    HZUSalesforceModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    LoggerModule.forRoot({
      region: process.env.REGION,
      logGroupName: process.env.HZU_LOGGER_LOG_GROUP_NAME,
      teamWebhookUrl: process.env.TEAMS_WEBHOOK_URL,
      isAlertNeeded: false,
      options: 'HZUCloudWatchLogger',
    }),
  ],
})
export class HZUSalesforceProcessModule { }
