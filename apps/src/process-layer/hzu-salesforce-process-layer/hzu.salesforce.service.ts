import { Injectable, Inject } from '@nestjs/common';
import { HZUSalesforceCompositeService } from 'apps/src/system-layer/hzu-salesforce/composite/composite.service';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import { HZUSalesforceHedEducationHistoryService } from 'apps/src/system-layer/hzu-salesforce/hedEducationHistory/hededucationhistory.service';
import { HZUSalesforceContactService } from 'apps/src/system-layer/hzu-salesforce/contact/contact.service';
import * as salesForceHelper from '../../../libs/utils/utils';
import { CustomException } from 'apps/src/common/customException/customexception.service';
import { HZUSalesforceOpportunityService } from 'apps/src/system-layer/hzu-salesforce/opportunity/opportunity.salesforce.service';
import { HZUSalesforceApplicationService } from 'apps/src/system-layer/hzu-salesforce/application/application.service';
import { UtilitiesService } from 'apps/src/system-layer/hzu-salesforce/utilities.service';
import * as AWS from 'aws-sdk';
const s3 = new AWS.S3();
const sts = new AWS.STS();
interface CompositeRequestOptions {
  condition?: string;
}
@Injectable()
export class HZUSalesforceProcessService {
  constructor(
    @Inject('HZUCloudWatchLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly HZUSalesforceCompositeService: HZUSalesforceCompositeService,
    private readonly HZUSalesforceHedEducationHistoryService: HZUSalesforceHedEducationHistoryService,
    private readonly HZUSalesforceContactService: HZUSalesforceContactService,
    private readonly loggerEnum: LoggerEnum,
    private readonly HZUSalesforceOpportunityService: HZUSalesforceOpportunityService,
    private readonly HzuSalesforceApplicationService: HZUSalesforceApplicationService,
    private readonly utilitiesService: UtilitiesService,
  ) { }
  async createApplication(applicationDetails, request): Promise<any> {
    await this.recordHZUApplicationLogs(
      applicationDetails,
      request,
      this.loggerEnum.Event.CREATE_APPLICATION_INITIATED,
      applicationDetails.scenario
    );
    const objectsToCreate = [
      'Contact',
      'Acccount',
      'Opportunity',
      'hed__Application__c',
      'hed__Education_History__c',
    ];
    const compositeRequest = [];
    let contactId, contactExists, accountId, opportunityId, applicationId;
    for (const object of objectsToCreate) {
      if (object === 'Contact' && applicationDetails.Contact) {
        ({ contactId, accountId } = await this.getContact(
          applicationDetails,
          request,
        ));
        contactExists = !!contactId;
        accountId = await this.handleAccount(
          applicationDetails,
          contactExists,
          contactId,
          accountId,
          compositeRequest,
        );
        if (!contactId) {
          contactId = await this.createNewContact(
            compositeRequest,
            applicationDetails.Contact,
          );
        }
      }

      if (object === 'Opportunity' && applicationDetails.Opportunity) {
        opportunityId = await this.getOpportunity(applicationDetails, request);
        if (!opportunityId) {
          applicationDetails.Opportunity.ContactId = contactId;
          applicationDetails.Opportunity.AccountId = accountId;
          compositeRequest.push(
            await this.buildCompositeRequest(
              'Opportunity',
              'newOpportunity',
              applicationDetails.Opportunity,
              'POST',
            ),
          );
          opportunityId = '@{newOpportunity.id}';
        }
      }

      if (
        object === 'hed__Application__c' &&
        applicationDetails.hed__Application__c
      ) {
        applicationId = await this.getApplication(applicationDetails, request);
        if (!applicationId) {
          applicationDetails.hed__Application__c.hed__Applicant__c = contactId;
          applicationDetails.hed__Application__c.Opportunity__c = opportunityId;
          compositeRequest.push(
            await this.buildCompositeRequest(
              'hed__Application__c',
              'newApplication',
              applicationDetails.hed__Application__c,
              'POST',
            ),
          );
          applicationId = '@{newApplication.id}';
          // Update application status to 'Completed'
          const updateStatusDetails = {
            hed__Application_Status__c: 'Completed',
          };

          compositeRequest.push(
            await this.buildCompositeRequest(
              'hed__Application__c',
              'updateApplication',
              updateStatusDetails,
              'PATCH',
              applicationId,
            ),
          );
        }
      }

      if (
        object === 'hed__Education_History__c' &&
        applicationDetails.hed__Education_History__c
      ) {
        applicationDetails.hed__Education_History__c =
          this.removeDuplicateInstitutions(
            applicationDetails.hed__Education_History__c,
          );
        for (const subobj in applicationDetails[object]) {
          let eduHistoryExist = false;
          if (contactExists === true) {
            const Id = await this.gethedEducationHistory(
              applicationDetails[object][subobj]
                .hed__Educational_Institution_Name__c,
              contactId,
              request,
            );
            eduHistoryExist = Id ? true : false;
          }
          if (eduHistoryExist !== true) {
            applicationDetails[object][subobj]['hed__Contact__c'] = contactId;
            applicationDetails[object][subobj]['hed__Account__c'] = accountId;
            compositeRequest.push(
              await this.buildCompositeRequest(
                'hed__Education_History__c',
                `${object}${subobj}`,
                applicationDetails[object][subobj],
                'POST',
                null,
              ),
            );
          }
        }
      }
    }
    await this.recordHZUApplicationLogs(
      applicationDetails,
      request,
      this.loggerEnum.Event.COMPOSITE_REQUEST_INITIATED,
      applicationDetails.scenario,
      compositeRequest,
    );

    try {
      const { compositeResponse } =
        await this.HZUSalesforceCompositeService.compositeRequest(
          compositeRequest,
        );
      console.log('compositeResponse', compositeResponse);

      await this.recordHZUApplicationLogs(
        applicationDetails,
        request,
        this.loggerEnum.Event.COMPOSITE_REQUEST_COMPLETED,
        applicationDetails.scenario,
        compositeRequest,
        compositeResponse,
      );

      return compositeResponse;
    } catch (error) {
      console.log(error);
      const brand = request?.allowedEntities?.brand;
      if (error instanceof CustomException) {
        const { errorCode, errorDetails, request, eventName } =
          error?.['response'];
        this.cloudWatchLoggerService.error(
          applicationDetails.requestId,
          new Date().toISOString(),
          this.loggerEnum.Component.GUS_EIP_SERVICE,
          this.loggerEnum.Component
            .GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
          this.loggerEnum.Component.HZU_EDUCATIONCLOUD,
          eventName,
          applicationDetails.scenario ?? this.loggerEnum.UseCase.SYNC_GUS_TO_HZU,
          applicationDetails,
          applicationDetails,
          JSON.stringify(error?.['response']),
          brand,
          applicationDetails?.gusOpportunityId || '',
          `gus-middleware-service/${applicationDetails.applicationId}/${applicationDetails.EventUuid}`,
          'Application_Form_Id__c',
          applicationDetails?.applicationId,
          'Opportunity_Contact_hed__Application__c_hed__Education_History__c',
          applicationDetails?.applicationId,
        );
      }
      throw error;
    }
  }
  async createNewContact(compositeRequest, contactDetails) {
    compositeRequest.push(
      await this.buildCompositeRequest(
        'Contact',
        'newContact',
        contactDetails,
        'POST',
        null,
      ),
    );
    return '@{newContact.id}';
  }
  async handleAccount(
    applicationDetails,
    contactExists,
    contactId,
    accountId,
    compositeRequest,
  ) {
    if (!applicationDetails.Account) {
      return;
    }
    if (!accountId && contactExists) {
      await this.addNewAccountRequest(
        compositeRequest,
        applicationDetails,
        contactId,
      );
      return '@{newAccount.id}';
    } else if (accountId && !contactExists) {
      applicationDetails.Contact.AccountId = accountId;
      return accountId;
    } else if (!accountId && !contactExists) {
      await this.addNewAccountRequest(
        compositeRequest,
        applicationDetails,
        null,
      );
      return '@{newAccount.id}';
    }
    return accountId;
  }
  async addNewAccountRequest(compositeRequest, applicationDetails, contactId) {
    compositeRequest.push(
      await this.buildCompositeRequest(
        'Account',
        'newAccount',
        applicationDetails.Account,
        'POST',
        contactId,
      ),
    );
    applicationDetails.Contact.AccountId = '@{newAccount.id}';
  }
  async getOpportunity(applicationDetails, request): Promise<any> {
    const entityName = 'Opportunity';
    const object = 'opportunityByExternalId';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      object,
    );
    let opportunityDetailsByIdCondition = ` where External_ID__c = '${applicationDetails.Opportunity.External_ID__c}'`;
    if (whereClause) {
      opportunityDetailsByIdCondition = `${opportunityDetailsByIdCondition} and ${whereClause}`;
    }
    const Opportunity =
      await this.HZUSalesforceOpportunityService.getOpportunities(
        fieldsRequired,
        opportunityDetailsByIdCondition,
      );
    return Opportunity[0]?.Id;
  }
  async getContact(applicationDetails, request): Promise<any> {
    try {
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          'Contact',
          request?.allowedEntities,
          'hzuContactDetails',
        );

      let contactCondition = ` Where FirstName='${applicationDetails.Contact.FirstName}' AND LastName='${applicationDetails.Contact.LastName}' AND Email='${applicationDetails.Contact.Email}'`;

      if (whereClause) {
        contactCondition += ` AND ${whereClause}`;
      }
      const contact = await this.HZUSalesforceContactService.getContact(
        fieldsRequired,
        contactCondition,
      );
      return { contactId: contact[0]?.Id, accountId: contact[0]?.AccountId };
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GET_CONTACT_ERROR',
        error.message ? error.message : error,
        applicationDetails.applicationId,
        this.loggerEnum.Event.SYNC_APPLICATION_DETAILS,
      );
    }
  }
  async getApplication(applicationDetails, request): Promise<any> {
    const entityName = 'hed__Application__c';
    let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      'applicationDetails',
    );
    whereClause = ` where External_ID__c = '${applicationDetails.hed__Application__c.External_ID__c}' `;
    const application =
      await this.HzuSalesforceApplicationService.getApplicationIdByExternalId(
        fieldsRequired,
        whereClause,
      );
    console.log(application);
    return application?.Id;
  }
  async gethedEducationHistory(
    institutionName,
    contactId,
    request,
  ): Promise<any> {
    try {
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          'hedEducationHistory',
          request?.allowedEntities,
          'hzuEduHistoryDetails',
        );

      let eduHistoryCondition = ` WHERE hed__Educational_Institution_Name__c='${this.escapeSpecialCharacters(institutionName)}' and hed__Contact__c = '${contactId}'`;

      if (whereClause) {
        eduHistoryCondition += ` AND ${whereClause}`;
      }
      const eduHistory =
        await this.HZUSalesforceHedEducationHistoryService.getHedEducationHistory(
          fieldsRequired,
          eduHistoryCondition,
        );
      return eduHistory[0]?.Id;
    } catch (error) {
      throw new CustomException(
        this.getStatus(error),
        'GET_EDUCATION_HISTORY_ERROR',
        error.message ? error.message : error,
        contactId,
        this.loggerEnum.Event.SYNC_APPLICATION_DETAILS,
      );
    }
  }
  escapeSpecialCharacters(input) {
    return input.replace(/['"\\]/g, '\\$&');
  }
  async buildCompositeRequest(
    object: string,
    referenceId: string,
    request: object | string | null,
    method: string,
    id: string | null = null,
  ): Promise<any> {
    const url = id
      ? `/services/data/${process.env.SALESFORCE_API_VERSION}/sobjects/${object}/${id}`
      : method === 'GET'
        ? `/services/data/${process.env.SALESFORCE_API_VERSION}/query?q=${request}`
        : `/services/data/${process.env.SALESFORCE_API_VERSION}/sobjects/${object}`;

    const compositeRequest = {
      method,
      url,
      referenceId,
      ...(method !== 'GET' && { body: request }),
    };

    return compositeRequest;
  }
  async recordHZUApplicationLogs(
    applicationDetails,
    request,
    event,
    usecase?,
    destinationRequest?,
    response?,
  ): Promise<any> {
    const requestId = applicationDetails.eventUuid;
    const brand = request?.allowedEntities?.brand;
    let logDetails = {};
    switch (event) {
      case this.loggerEnum.Event.CREATE_APPLICATION_INITIATED:
        logDetails = {
          logMessage: 'Received create application request',
          secondaryKey: applicationDetails?.gusOpportunityId,
        };
        break;
      case this.loggerEnum.Event.COMPOSITE_REQUEST_INITIATED:
        logDetails = {
          logMessage: 'Composite request initiated',
          secondaryKey: applicationDetails?.gusOpportunityId,
        };
        break;
      case this.loggerEnum.Event.COMPOSITE_REQUEST_COMPLETED:
        logDetails = {
          logMessage: 'Composite request completed',
          secondaryKey: applicationDetails?.gusOpportunityId,
        };
        break;
    }
    await this.cloudWatchLoggerService.log(
      requestId,
      new Date().toISOString(),
      this.loggerEnum.Component.GUS_EIP_SERVICE,
      this.loggerEnum.Component
        .GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      this.loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.loggerEnum.UseCase[usecase] ?? usecase ?? this.loggerEnum.UseCase.GUS_SALESFORCE_OPERATION,
      applicationDetails,
      destinationRequest ? destinationRequest : applicationDetails,
      logDetails['logMessage'],
      brand,
      logDetails['secondaryKey'],
      `gus-middleware-service/${applicationDetails.applicationId}/${requestId}`,
      'Application_Form_Id__c',
      applicationDetails.applicationId,
      'Opportunity_Contact_hed__Application__c_hed__Education_History__c',
      'Opportunity_Contact_hed__Application__c_hed__Education_History__c',
      null,
      null,
      response,
    );
  }
  removeDuplicateInstitutions(educationHistory) {
    const uniqueInstitutions = [];
    const institutionNames = new Set();

    educationHistory.forEach((record) => {
      if (!institutionNames.has(record.hed__Educational_Institution_Name__c)) {
        institutionNames.add(record.hed__Educational_Institution_Name__c);
        uniqueInstitutions.push(record);
      }
    });

    return uniqueInstitutions;
  }
  getStatus(error): any {
    let status;
    if (error.statusCode) {
      status = error.statusCode;
    } else if (error.code) {
      status = error.code;
    } else {
      status = '500';
    }
    return status;
  }
  async updateDocument(documentDetails, request, scenario?): Promise<any> {
    const compositeRequest = [];
    compositeRequest.push(
      await this.buildCompositeRequest(
        'ContentVersion',
        'createUpdatedContentVersion',
        await this.formContentVersion(documentDetails),
        'POST',
      ),
    );
    compositeRequest.push(
      await this.buildCompositeRequest(
        'DocumentChecklistItem',
        'updateDocumentChecklistItem',
        {
          External_ID__c: documentDetails.externalId,
        },
        'PATCH',
        documentDetails.documentCheckListId,
      ),
    );
    const { compositeResponse } =
      await this.HZUSalesforceCompositeService.compositeRequest(
        compositeRequest,
      );
    const hasErrors = compositeResponse.some((response) => {
      const statusCode = response.httpStatusCode;
      return statusCode !== 200 && statusCode !== 201 && statusCode !== 204;
    });

    if (hasErrors) {
      await this.utilitiesService.recordHZUErrorLogs(
        documentDetails,
        request,
        'DOCUMENT_UPLOAD_ERROR',
        'upload document failed',
        null,
        'ContentVersion_ContentDocumentLink',
        scenario,
        compositeRequest,
        compositeResponse,
      );
    }
    await this.utilitiesService.recordHZULogs(
      documentDetails,
      request,
      this.loggerEnum.Event.DOCUMENT_UPLOAD_COMPLETED,
      'upload document completed successfully',
      null,
      'ContentVersion_ContentDocumentLink',
      scenario,
      compositeRequest,
      compositeResponse,
    );
    return compositeResponse;
  }
  async uploadDocument(documentDetails, request, scenario?): Promise<any> {
    const compositeRequest = [];
    compositeRequest.push(
      await this.buildCompositeRequest(
        'ContentVersion',
        'newContentVersion',
        await this.formContentVersion(documentDetails),
        'POST',
      ),
    );
    compositeRequest.push(
      await this.buildCompositeRequest(
        'ContentDocumentLink',
        'getContentDocumentLinkDetails',
        "SELECT+ContentDocumentId+FROM+ContentVersion+WHERE+Id='@{newContentVersion.id}'",
        'GET',
      ),
    );
    compositeRequest.push(
      await this.buildCompositeRequest(
        'ContentDocumentLink',
        'newContentDocumentLink',
        await this.linkDocument(documentDetails),
        'POST',
      ),
    );
    compositeRequest.push(
      await this.buildCompositeRequest(
        'DocumentChecklistItem',
        'updateDocumentChecklistItem',
        {
          External_ID__c: documentDetails.externalId,
        },
        'PATCH',
        documentDetails.documentCheckListId,
      ),
    );
    await this.utilitiesService.recordHZULogs(
      documentDetails,
      request,
      this.loggerEnum.Event.DOCUMENT_UPLOAD_INITIATED,
      'upload document initiated',
      null,
      'ContentVersion_ContentDocumentLink',
      scenario,
      compositeRequest,
      null,
    );
    const { compositeResponse } =
      await this.HZUSalesforceCompositeService.compositeRequest(
        compositeRequest,
      );
    const hasErrors = compositeResponse.some((response) => {
      const statusCode = response.httpStatusCode;
      return statusCode !== 200 && statusCode !== 201 && statusCode !== 204;
    });

    if (hasErrors) {
      await this.utilitiesService.recordHZUErrorLogs(
        documentDetails,
        request,
        'DOCUMENT_UPLOAD_ERROR',
        'upload document failed',
        null,
        'ContentVersion_ContentDocumentLink',
        scenario,
        compositeRequest,
        compositeResponse,
      );
    }
    await this.utilitiesService.recordHZULogs(
      documentDetails,
      request,
      this.loggerEnum.Event.DOCUMENT_UPLOAD_COMPLETED,
      'upload document completed successfully',
      null,
      'ContentVersion_ContentDocumentLink',
      scenario,
      compositeRequest,
      compositeResponse,
    );
    return compositeResponse;
  }
  async checkDocumentAlreadyExists(documentDetails: any): Promise<any> {
    const compositeRequest = [];
    compositeRequest.push(
      await this.buildCompositeRequest(
        'ContentDocumentLink',
        'getContentDocumentLink',
        `SELECT Id, ContentDocumentId, ContentDocument.LatestPublishedVersionId FROM ContentDocumentLink WHERE LinkedEntityId='${documentDetails.documentCheckListId}'`,
        'GET',
      ),
    );
    const { compositeResponse } =
      await this.HZUSalesforceCompositeService.compositeRequest(
        compositeRequest,
      );
    return compositeResponse[0].body?.records?.[0];
  }
  async formContentVersion(documentDetails): Promise<any> {
    return {
      PathOnClient: documentDetails.fileNameWithExtension,
      Title: documentDetails.fileNameWithExtension,
      ContentLocation: 'S',
      VersionData: documentDetails.base64File,
      ContentDocumentId: documentDetails?.contentDocumentId,
      // FileExtension: documentDetails.fileExtension,
      // FileType: documentDetails.fileType,
    };
  }
  async linkDocument(documentDetails): Promise<any> {
    return {
      LinkedEntityId: documentDetails.documentCheckListId,
      ContentDocumentId:
        '@{getContentDocumentLinkDetails.records[0].ContentDocumentId}',
      Visibility: 'AllUsers',
    };
  }

  async getBase64File(
    bucket: string,
    key: string,
    roleArn: string,
  ): Promise<string> {
    const s3 = await this.getS3CredentialsByRole(roleArn);

    console.log('S3 -->', s3);
    const params = {
      Bucket: bucket,
      Key: key,
    };
    console.log('Params -->', params);

    const data = await new Promise<AWS.S3.GetObjectOutput>(
      (resolve, reject) => {
        s3.getObject(params, (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      },
    );

    console.log('Data -->', params);
    if (!data.Body) {
      throw new Error('File not found or empty');
    }

    const base64File = data.Body.toString('base64');
    console.log('base64File -->', base64File);
    return base64File;
  }

  async getS3CredentialsByRole(roleArn): Promise<any> {
    const sessionName = `Session-${Date.now()}`;
    const param: AWS.STS.AssumeRoleRequest = {
      RoleArn: roleArn,
      RoleSessionName: sessionName,
    };
    const data = await new Promise((resolve, reject) => {
      sts.assumeRole(param, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    const credentials = data['Credentials'];
    const s3 = new AWS.S3({
      accessKeyId: credentials.AccessKeyId,
      secretAccessKey: credentials.SecretAccessKey,
      sessionToken: credentials.SessionToken,
    });
    return s3;
  }
}
