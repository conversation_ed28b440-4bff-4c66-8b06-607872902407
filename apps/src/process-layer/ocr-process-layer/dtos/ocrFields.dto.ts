import {
  <PERSON>Enum,
  <PERSON>NotEmpty,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { DocTypeEnum } from '../enums/docType.enum';
import { Type } from 'class-transformer';

class OCRFieldsDto {
  @IsOptional()
  @IsString()
  Identity_Document_Number__c: string;

  @IsOptional()
  @IsString()
  FirstName: string;

  @IsOptional()
  @IsString()
  LastName: string;

  @IsOptional()
  @IsString()
  Expiry_Date__c: string;

  @IsOptional()
  @IsString()
  Issuing_Country__c: string;

  @IsOptional()
  @IsString()
  Issue_Date__c: string;

  @IsOptional()
  Expiry_Date_Confidence_Score__c: string | number;

  @IsOptional()
  Identity_Document_Confidence_Score__c: string | number;

  @IsOptional()
  Issue_Date_Confidence_Score__c: string | number;

  @IsOptional()
  Issuing_Country_Confidence_Score__c: string | number;
}

class OCRReferenceDto {
  @IsString()
  @IsNotEmpty()
  opportunityId: string;

  @IsEnum(DocTypeEnum)
  @IsNotEmpty()
  docType: DocTypeEnum;

  @IsString()
  @IsNotEmpty()
  brand: string;

  @IsString()
  @IsNotEmpty()
  opportunityFileId: string;

  @IsString()
  @IsNotEmpty()
  relatedId: string;
}

export class OCRResponseFieldsDto {
  @IsObject()
  @ValidateNested()
  @Type(() => OCRFieldsDto)
  ocrFields: OCRFieldsDto;

  @IsObject()
  @ValidateNested()
  @Type(() => OCRReferenceDto)
  reference: OCRReferenceDto;

  @IsOptional()
  @IsString()
  errorMsg: string;
}
