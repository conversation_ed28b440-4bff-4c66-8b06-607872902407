import {
  Body,
  Controller,
  HttpException,
  HttpStatus,
  Patch,
  Post,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Request } from 'express';
import * as uuid from 'uuid';
import { OCRProcessService } from './ocr.process.service';
import { OCRResponseFieldsDto } from './dtos/ocrFields.dto';
import { OCRStatusFieldsDto } from './dtos/ocrStatusFields.dto';
import { GusSalesforceOpportunityFileService } from 'apps/src/system-layer/gus-salesforce/opportunityFiles/opportunityFiles.service';

@Controller()
@UsePipes(
  new ValidationPipe({
    whitelist: true,
  }),
)
export class OCRProcessController {
  constructor(
    private readonly ocrProcessService: OCRProcessService,
    private readonly gusSalesforceOpportunityFileService: GusSalesforceOpportunityFileService,
  ) {}

  @Post('salesforce/gus/ocr/results')
  async saveOCRResults(
    @Body() data: OCRResponseFieldsDto,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.ocrProcessService.saveOCRResults(
        request['requestId'] ?? uuid.v4(),
        data,
      );
    } catch (error) {
      throw new HttpException(
        {
          statusCode: error.status || HttpStatus.INTERNAL_SERVER_ERROR,
          message: error || 'Internal Server Error',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch('salesforce/gus/ocr/status')
  async updateOCRStatus(@Body() event: OCRStatusFieldsDto): Promise<any> {
    try {
      return await this.gusSalesforceOpportunityFileService.updateOpportunityFileRecord(
        event.opportunityFileId,
        { Status__c: event.status },
      );
    } catch (error) {
      throw new HttpException(
        {
          statusCode: error.status || HttpStatus.INTERNAL_SERVER_ERROR,
          message: error || 'Internal Server Error',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
