import { DynamicModule, Module } from '@nestjs/common';
import { OCRProcessService } from './ocr.process.service';
import { OCRProcessController } from './ocr.process.controller';
import { GusSalesforceSystemModule } from 'apps/src/system-layer/gus-salesforce/gus.salesforce.system.module';
import { DynamoDBModule } from 'apps/src/common/dynamoDB/dynamoDB.module';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { LoggerModule } from '@gus-eip/loggers';

@Module({})
export class OCRProcessModule {
  static async forRoot(
    parameterStoreCacheService: ParameterStoreCacheService,
  ): Promise<DynamicModule> {
    const logGroupname = (await parameterStoreCacheService.getParameter(
      'OCR_LOGGER_LOG_GROUP_NAME',
    )) as string;

    const teamsURL = (await parameterStoreCacheService.getParameter(
      'OCR_TEAMS_WEBHOOK_URL',
    )) as string;

    return {
      module: OCRProcessModule,
      imports: [
        LoggerModule.forRoot({
          region: process.env.REGION,
          logGroupName: logGroupname,
          options: 'OCRLogger',
          teamWebhookUrl: teamsURL,
          isAlertNeeded: true,
        }),
        GusSalesforceSystemModule,
      ],
      controllers: [OCRProcessController],
      providers: [OCRProcessService],
    };
  }
}
