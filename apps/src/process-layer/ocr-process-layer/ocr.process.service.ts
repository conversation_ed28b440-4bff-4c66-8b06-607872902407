import { Inject, Injectable } from '@nestjs/common';
import { GusSalesforceAccountService } from 'apps/src/system-layer/gus-salesforce/account/account.service';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
// import { OCRStatusEnum } from './enums/ocrStatus.enum';
import { GusSalesforceIdentityInfoRecordService } from 'apps/src/system-layer/gus-salesforce/identityInfoRecord/identityInfoRecord.service';
// import { GusSalesforceOpportunityFileService } from 'apps/src/system-layer/gus-salesforce/opportunityFiles/opportunityFiles.service';

@Injectable()
export class OCRProcessService {
  constructor(
    @Inject('OCRLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly loggerEnum: LoggerEnum,
    private readonly gusIdentityInfoRecordSalesforceService: GusSalesforceIdentityInfoRecordService,
    // private readonly gusSalesforceOpportunityFileService: GusSalesforceOpportunityFileService,
    private readonly gusSalesforceAccountService: GusSalesforceAccountService,
  ) {}

  async saveOCRResults(
    correlationId: string,
    extractedData: Record<string, any>,
  ): Promise<any> {
    try {
      await this.cloudWatchLoggerService.log(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OCR_RESPONSE_PROCESS_HANDLER,
        this.loggerEnum.Component.OCR_RESPONSE_PROCESS_HANDLER,
        this.loggerEnum.Event.SAVE_OCR_FIELDS,
        this.loggerEnum.UseCase.OCR_RESULT_PROCESSING,
        extractedData,
        null,
        'Initiate - Save OCR Extracted Fields',
        extractedData.reference.brand,
        extractedData.reference.opportunityFileId,
        `gus-ocr-handler/${extractedData.reference.opportunityId}/${correlationId}`,
        'opportunityId',
        extractedData.reference.opportunityId,
      );

      const queryCondition = `WHERE Id = '${extractedData.reference.relatedId}'`;

      const getIdentityInfoResponse =
        await this.gusIdentityInfoRecordSalesforceService.getIdentityInfoRecordData(
          queryCondition,
          ['Id', 'Account__c'],
        );

      const saveIdentityInfoFieldsresponse =
        await this.gusIdentityInfoRecordSalesforceService.updateIdentityInfoRecordData(
          getIdentityInfoResponse?.Id,
          {
            Identity_Document_Number__c:
              extractedData.ocrFields?.Identity_Document_Number__c,
            Expiry_Date__c: extractedData.ocrFields?.Expiry_Date__c
              ? extractedData.ocrFields?.Expiry_Date__c
              : null,
            Issuing_Country__c: extractedData.ocrFields?.Issuing_Country__c,
            Issue_Date__c: extractedData.ocrFields?.Issue_Date__c
              ? extractedData.ocrFields?.Issue_Date__c
              : null,
            Expiry_Date_Confidence_Score__c:
              extractedData.ocrFields.Expiry_Date_Confidence_Score__c,
            Identity_Document_Confidence_Score__c:
              extractedData.ocrFields.Identity_Document_Confidence_Score__c,
            Issue_Date_Confidence_Score__c:
              extractedData.ocrFields.Issue_Date_Confidence_Score__c,
            Issuing_Country_Confidence_Score__c:
              extractedData.ocrFields.Issuing_Country_Confidence_Score__c,
          },
        );

      let saveAccountIdentityInfoFieldsresponse;
      if (extractedData.ocrFields?.FirstName) {
        saveAccountIdentityInfoFieldsresponse =
          await this.gusSalesforceAccountService.updateAccount(
            {
              FirstName: extractedData.ocrFields?.FirstName,
              ...(extractedData?.ocrFields?.LastName && {
                LastName: extractedData.ocrFields.LastName,
              }),
            },
            getIdentityInfoResponse?.Account__c,
          );
      }

      // await this.gusSalesforceOpportunityFileService.updateOpportunityFileRecord(
      //   extractedData.reference.opportunityFileId,
      //   { Status__c: OCRStatusEnum.complete },
      // );

      return {
        saveIdentityInfoFieldsresponse,
        saveAccountIdentityInfoFieldsresponse,
      };
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OCR_RESPONSE_PROCESS_HANDLER,
        this.loggerEnum.Component.OCR_RESPONSE_PROCESS_HANDLER,
        this.loggerEnum.Event.SAVE_OCR_FIELDS,
        this.loggerEnum.UseCase.OCR_RESULT_PROCESSING,
        extractedData,
        null,
        error,
        extractedData.reference.brand,
        extractedData.reference.opportunityFileId,
        `gus-ocr-handler/${extractedData.reference.opportunityId}/${correlationId}`,
        'opportunityId',
        extractedData.reference.opportunityId,
      );
      throw error;
    }
  }
}
