import {
  <PERSON>,
  Body,
  Post,
  BadRequestException,
  Req,
} from '@nestjs/common';
import { R3ApplicationProcessService } from './application.process.service';
import { Request } from 'express';

@Controller('/salesforce/r3')
export class R3ApplicationProcessController {
  constructor(
    private readonly r3ApplicationProcessService: R3ApplicationProcessService,
  ) {}
  @Post('/createapplication')
  async createApplication(
    @Body() requestBody,
    @Req() request: Request,
  ): Promise<any> {
    return await this.r3ApplicationProcessService.createApplication(
      requestBody,
      request,
    );
  }
  @Post('/updateapplication')
  async updateApplication(
    @Body() requestBody,
    @Req() request: Request,
  ): Promise<any> {
    return await this.r3ApplicationProcessService.updateApplication(
      requestBody,
      request,
    );
  }
  @Post('/uploadfile')
  async uploadFile(
    @Body() data: any,
    @Req() request: Request,
  ): Promise<string> {
    try {
      if (
        data.applicationId &&
        data.gusApplicationId &&
        data.fileType &&
        data.path &&
        data.pathName &&
        data.file
      ) {
        return await this.r3ApplicationProcessService.uploadFile(data, request);
      } else {
        throw new BadRequestException('Missing required fields');
      }
    } catch (error) {
      console.error('Error create application:', error);
      throw error;
    }
  }
}
