import {
  Injectable,
  InternalServerErrorException,
  HttpStatus,
  NotFoundException,
  Inject,
} from '@nestjs/common';
import { R3SalesforceApplicationService } from 'apps/src/system-layer/r3-salesforce/application/application.service';
import { R3SalesforceContactService } from 'apps/src/system-layer/r3-salesforce/contact/contact.service';
import { R3SalesforceRecordTypeService } from 'apps/src/system-layer/r3-salesforce/recordType/recordType.service';
import { R3SalesforcePrerequesitesAndExperienceService } from 'apps/src/system-layer/r3-salesforce/prerequesitesAndExperience/prerequesitesAndExperience.service';
import { R3SalesforceContentDocumentLinkService } from 'apps/src/system-layer/r3-salesforce/contentDocumentLink/contentDocumentLink.service';
import { R3SalesforceRecommenderService } from 'apps/src/system-layer/r3-salesforce/recommender/recommender.service';
import { R3SalesforceContentVersionService } from 'apps/src/system-layer/r3-salesforce/contentVersion/contentVersion.service';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
@Injectable()
export class R3ApplicationProcessService {
  constructor(
    @Inject('CloudWatchLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly r3SalesforceApplicationService: R3SalesforceApplicationService,
    private readonly r3SalesforceContactService: R3SalesforceContactService,
    private readonly r3SalesforceRecordTypeService: R3SalesforceRecordTypeService,
    private readonly r3SalesforcePrerequesitesAndExperienceService: R3SalesforcePrerequesitesAndExperienceService,
    private readonly r3SalesforceRecommenderService: R3SalesforceRecommenderService,
    private readonly r3SalesforceContentDocumentLinkService: R3SalesforceContentDocumentLinkService,
    private readonly r3SalesforceContentVersionService: R3SalesforceContentVersionService,
    private readonly loggerEnum: LoggerEnum,
  ) { }
  async recordSalesforceRequest(
    object,
    action,
    request,
    initiated: boolean = true,
  ) {
    const event = initiated
      ? this.loggerEnum.Event.SALESFORCE_REQUEST_INITIATED
      : this.loggerEnum.Event.SALESFORCE_REQUEST_COMPLETED;
    // this.cloudWatchLoggerService.log(
    //   request?.body.applicationId,
    //   new Date().toISOString(),
    //   this.loggerEnum.Component.GUS_EIP_SERVICE,
    //   this.loggerEnum.Component.OAP_HANDLERS,
    //   this.loggerEnum.Component.SALESFORCE,
    //   event,
    //   action,
    //   request.body,
    //   request.body,
    //   'Salesforce Request',
    //   'R3',
    //   '',
    //   `gus-middleware-service/${request?.body.applicationId}`,
    //   object,
    //   request?.body.applicationId,
    // );
  }

  async createApplication(r3ApplicationDetails, request): Promise<any> {
    let contactId;
    let applicationId;

    const contactDetails = r3ApplicationDetails?.contactDetails;
    if (contactDetails?.Email) {
      await this.recordSalesforceRequest('Contact', 'GET', request, true);
      contactId = await this.r3SalesforceContactService.getContactId(
        contactDetails.Email,
        `School__c='${r3ApplicationDetails?.contactDetails?.School__c}'`,
      );
      await this.recordSalesforceRequest('Contact', 'GET', request, false);
      if (
        contactId &&
        r3ApplicationDetails?.applicationDetails?.gusApplicationId
      ) {
        await this.recordSalesforceRequest(
          'Application__c',
          'GET',
          request,
          true,
        );
        const applicationDetails =
          await this.r3SalesforceApplicationService.getApplication(
            'Id',
            `where Applicant__c = '${contactId} and GUSApplicationId__c = '${r3ApplicationDetails?.applicationDetails?.gusApplicationId}' `,
          );
        applicationId = applicationDetails?.Id;
        await this.recordSalesforceRequest(
          'Application__c',
          'GET',
          request,
          false,
        );
      }
    } else {
      throw new NotFoundException(`Email is required to create application`);
    }

    let r3CreatedContactDetails;

    if (!contactId) {
      if (contactDetails) {
        await this.recordSalesforceRequest('Contact', 'CREATE', request, true);
        r3CreatedContactDetails =
          await this.r3SalesforceContactService.createContact(contactDetails);
        r3ApplicationDetails.applicationDetails.Applicant__c =
          r3CreatedContactDetails?.data?.id;
        contactId = r3CreatedContactDetails?.data?.id;
        await this.recordSalesforceRequest('Contact', 'CREATE', request, false);
      }
    } else {
      r3ApplicationDetails.applicationDetails.Applicant__c = contactId;
      if (contactDetails?.hasOwnProperty('Email')) {
        delete contactDetails.Email;
        await this.recordSalesforceRequest('Contact', 'UPDATE', request, true);
        await this.r3SalesforceContactService.updateContact(
          contactId,
          contactDetails,
        );
        await this.recordSalesforceRequest('Contact', 'UPDATE', request, false);
      }
    }

    let r3CreatedApplicationDetails;
    if (!applicationId) {
      if (r3ApplicationDetails?.applicationDetails) {
        await this.recordSalesforceRequest(
          'Application__c',
          'CREATE',
          request,
          true,
        );
        r3CreatedApplicationDetails =
          await this.r3SalesforceApplicationService.createApplication(
            r3ApplicationDetails.applicationDetails,
          );
        await this.recordSalesforceRequest(
          'Application__c',
          'CREATE',
          request,
          false,
        );
        applicationId = r3CreatedApplicationDetails.data.id;
      }
    }

    return {
      applicationId: applicationId,
      contactId: contactId,
    };
  }
  async getR3ApplicationAndContactId(
    applicationDetails,
    request,
  ): Promise<any> {
    await this.recordSalesforceRequest('Application__c', 'GET', request, true);
    const r3ApplicationDetails =
      await this.r3SalesforceApplicationService.getApplication(
        'Id,Applicant__c',
        `where GUSApplicationId__c = '${applicationDetails.applicationDetails.GUSApplicationId__c}' and Email__c = '${applicationDetails.email}' and Applicant__r.School__c = '${applicationDetails.school}'`,
      );
    await this.recordSalesforceRequest('Application__c', 'GET', request, false);
    return r3ApplicationDetails;
  }
  async updateApplication(updateApplicationDetails, request): Promise<any> {
    const updateApplicationResponse = {
      Prerequesites_and_Experience__c: [],
      Recommender__c: [],
    };
    let applicationId = updateApplicationDetails.applicationId;
    let contactId = updateApplicationDetails.contactId;
    if (!(applicationId || contactId)) {
      const r3ApplicationDetails = await this.getR3ApplicationAndContactId(
        updateApplicationDetails,
        request,
      );
      applicationId = r3ApplicationDetails.Id;
      contactId = r3ApplicationDetails.Applicant__c;
    }
    if (Object.keys(updateApplicationDetails?.applicationDetails).length > 0) {
      await this.recordSalesforceRequest(
        'Application__c',
        'UPDATE',
        request,
        true,
      );
      await this.r3SalesforceApplicationService.updateApplication(
        applicationId,
        updateApplicationDetails.applicationDetails,
      );
      await this.recordSalesforceRequest(
        'Application__c',
        'UPDATE',
        request,
        false,
      );
    }
    if (Object.keys(updateApplicationDetails?.contactDetails).length > 0) {
      await this.recordSalesforceRequest('Contact', 'UPDATE', request, true);
      await this.r3SalesforceContactService.updateContact(
        contactId,
        updateApplicationDetails.contactDetails,
      );
      await this.recordSalesforceRequest('Contact', 'UPDATE', request, false);
    }
    if (updateApplicationDetails?.Prerequesites_and_Experience__c.length > 0) {
      for (const prerequesitiesAndExperience of updateApplicationDetails?.Prerequesites_and_Experience__c) {
        if ('id' in prerequesitiesAndExperience) {
          const updateRecordId = prerequesitiesAndExperience.id;
          if (prerequesitiesAndExperience.hasOwnProperty('id')) {
            delete prerequesitiesAndExperience['id'];
          }
          await this.recordSalesforceRequest(
            'Prerequesites_and_Experience__c',
            'UPDATE',
            request,
            true,
          );
          await this.r3SalesforcePrerequesitesAndExperienceService.updatePrerequesitesAndExperience(
            updateRecordId,
            prerequesitiesAndExperience,
          );
          await this.recordSalesforceRequest(
            'Prerequesites_and_Experience__c',
            'UPDATE',
            request,
            false,
          );
        } else {
          const updateRecordTypeResponse = {};
          updateRecordTypeResponse['duplicateId'] =
            prerequesitiesAndExperience.duplicateId;
          await this.recordSalesforceRequest(
            'RecordType',
            'GET',
            request,
            true,
          );
          const recordTypeId =
            await this.r3SalesforceRecordTypeService.getRecordTypeIdByType(
              prerequesitiesAndExperience?.type,
            );
          await this.recordSalesforceRequest(
            'RecordType',
            'GET',
            request,
            false,
          );
          if (prerequesitiesAndExperience.hasOwnProperty('type')) {
            delete prerequesitiesAndExperience['type'];
          }
          if (prerequesitiesAndExperience.hasOwnProperty('duplicateId')) {
            delete prerequesitiesAndExperience['duplicateId'];
          }
          prerequesitiesAndExperience['RecordTypeId'] = recordTypeId;
          prerequesitiesAndExperience['Applicant__c'] = contactId;
          prerequesitiesAndExperience['Application__c'] = applicationId;
          await this.recordSalesforceRequest(
            'Prerequesites_and_Experience__c',
            'CREATE',
            request,
            true,
          );
          const prerequesitiesAndExperienceResponse =
            await this.r3SalesforcePrerequesitesAndExperienceService.createPrerequesitesAndExperience(
              prerequesitiesAndExperience,
            );
          await this.recordSalesforceRequest(
            'Prerequesites_and_Experience__c',
            'CREATE',
            request,
            false,
          );
          updateRecordTypeResponse['id'] =
            prerequesitiesAndExperienceResponse?.['data']?.id;
          updateApplicationResponse.Prerequesites_and_Experience__c.push(
            updateRecordTypeResponse,
          );
        }
      }
    }
    if (updateApplicationDetails?.Recommender__c.length > 0) {
      for (const recommender of updateApplicationDetails?.Recommender__c) {
        if ('id' in recommender) {
          const updateRecordId = recommender.id;
          if (recommender.hasOwnProperty('id')) {
            delete recommender['id'];
          }
          await this.recordSalesforceRequest(
            'Recommender__c',
            'UPDATE',
            request,
            true,
          );
          await this.r3SalesforceRecommenderService.updateRecommender(
            updateRecordId,
            recommender,
          );
          await this.recordSalesforceRequest(
            'Recommender__c',
            'UPDATE',
            request,
            false,
          );
        } else {
          const updateRecommenderResponse = {};
          updateRecommenderResponse['duplicateId'] = recommender?.duplicateId;
          recommender['Application__c'] = applicationId;
          if (recommender.hasOwnProperty('duplicateId')) {
            delete recommender['duplicateId'];
          }
          await this.recordSalesforceRequest(
            'Recommender__c',
            'CREATE',
            request,
            true,
          );
          const recommenderResponse =
            await this.r3SalesforceRecommenderService.createRecommender(
              recommender,
            );
          updateRecommenderResponse['id'] = recommenderResponse?.['data']?.id;
          updateApplicationResponse.Recommender__c.push(
            updateRecommenderResponse,
          );
          await this.recordSalesforceRequest(
            'Recommender__c',
            'CREATE',
            request,
            false,
          );
        }
      }
    }
    return updateApplicationResponse;
  }
  async uploadFile(fileJson, request): Promise<any> {
    let uploadFileResponse = {};
    let documentId;
    try {
      await this.recordSalesforceRequest(
        'ContentVersion',
        'CREATE',
        request,
        true,
      );
      documentId =
        await this.r3SalesforceContentVersionService.createContentVersion(
          fileJson,
        );
      await this.recordSalesforceRequest(
        'ContentVersion',
        'CREATE',
        request,
        false,
      );
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Upload failed',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        errorCode: 'UPLOAD_FILE_FAILED',
      });
    }
    uploadFileResponse['ContentDocumentVersionId'] = documentId;
    await this.recordSalesforceRequest('ContentVersion', 'GET', request, true);
    const documentDetails =
      await this.r3SalesforceContentVersionService.getContentDocumentDetails(
        documentId,
      );
    await this.recordSalesforceRequest('ContentVersion', 'GET', request, false);
    uploadFileResponse = {
      ...uploadFileResponse,
      ...documentDetails,
    };
    const ContentDocumentId = documentDetails?.ContentDocumentId;
    let documentLinkedId;
    if (ContentDocumentId) {
      await this.recordSalesforceRequest(
        'ContentDocumentLink',
        'CREATE',
        request,
        true,
      );
      documentLinkedId =
        await this.r3SalesforceContentDocumentLinkService.linkDocumentToObject(
          fileJson?.applicationId,
          ContentDocumentId,
        );
      await this.recordSalesforceRequest(
        'ContentDocumentLink',
        'CREATE',
        request,
        false,
      );
    } else {
      throw new InternalServerErrorException('Upload failed');
    }
    uploadFileResponse['documentLinkedId'] = documentLinkedId;
    uploadFileResponse['file'] = fileJson.file;
    return uploadFileResponse;
  }
}
