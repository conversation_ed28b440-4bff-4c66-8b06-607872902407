import { Modu<PERSON> } from '@nestjs/common';
import { R3ApplicationProcessController } from './application.process.controller';
import { R3ApplicationProcessService } from './application.process.service';
import { R3SalesforceSystemModule } from 'apps/src/system-layer/r3-salesforce/r3.salesforce.system.module';
import { LoggerModule } from '@gus-eip/loggers';
import { ConfigModule } from '@nestjs/config';

@Module({
  providers: [R3ApplicationProcessController, R3ApplicationProcessService],
  controllers: [R3ApplicationProcessController],
  exports: [R3ApplicationProcessController],
  imports: [
    R3SalesforceSystemModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    LoggerModule.forRoot({
      region: process.env.REGION,
      logGroupName: process.env.LOGGER_LOG_GROUP_NAME,
      options: 'CloudWatchLogger',
    }),
  ],
})
export class R3SalesforceProcessModule {}
