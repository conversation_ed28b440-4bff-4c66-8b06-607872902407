import { Type } from 'class-transformer';
import { IsOptional, IsString, ValidateNested } from 'class-validator';

export class AgencyBusinessEssentialsInfodto {
    @IsString()
    Screening_Enquiry__c: string;

    @IsString()
    Business_Essentials_ID__c: string;

    @IsString()
    Service_Type__c: string;

    @IsString()
    RecordTypeId: string;

    @IsOptional()
    @IsString()
    Address_1__c: string;

    @IsOptional()
    @IsString()
    PostalCode__c: string;

    @IsOptional()
    @IsString()
    City__c: string;

    @IsOptional()
    @IsString()
    Unit_Number__c: string;

    @IsOptional()
    @IsString()
    Street_Name__c: string;

    @IsOptional()
    @IsString()
    Street_Type__c: string;

    @IsOptional()
    @IsString()
    Business_Name__c: string;

    @IsOptional()
    @IsString()
    DUNS_Number__c: string;

    @IsOptional()
    @IsString()
    Building_Name__c: string;

    @IsOptional()
    @IsString()
    Suburb__c: string;

    @IsOptional()
    @IsString()
    State_Province_Code__c: string;

    @IsOptional()
    @IsString()
    Building_Number__c: string;

    @IsOptional()
    @IsString()
    Business_Registration_Number__c: string;

    @IsOptional()
    @IsString()
    Jurisdiction_Of_Incorporation__c: string;

    @IsOptional()
    @IsString()
    Second_Address__c: string;

    @IsOptional()
    @IsString()
    Second_Building_Name__c: string;

    @IsOptional()
    @IsString()
    Second_Building_Number__c: string;

    @IsOptional()
    @IsString()
    Second_City__c: string;

    @IsOptional()
    @IsString()
    Second_Postal_Code__c: string;

    @IsOptional()
    @IsString()
    Second_State__c: string;

    @IsOptional()
    @IsString()
    Second_Street_Name__c: string;

    @IsOptional()
    @IsString()
    Second_Street_Type__c: string;

    @IsOptional()
    @IsString()
    Second_Suburb__c: string;

    @IsOptional()
    @IsString()
    Second_Unit_Number__c: string;

    @IsOptional()
    @IsString()
    Sources__c: string;
}

export class AgencyBusinessEssentialsDto {
    @ValidateNested()
    @Type(() => AgencyBusinessEssentialsInfodto)
    data: AgencyBusinessEssentialsInfodto;
}
