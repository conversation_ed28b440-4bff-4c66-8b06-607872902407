import { Type } from 'class-transformer';
import { IsOptional, IsString, ValidateNested } from 'class-validator';

export class AgencyScreeningFeedbackInfodto {
  @IsString()
  Screening_Enquiry__c: string;

  @IsString()
  Watchlist_Status__c: string;

  @IsString()
  Score__c: string;

  @IsString()
  Subject_Matched__c: string;

  @IsOptional()
  @IsString()
  Entity_Name__c: string;

  @IsOptional()
  @IsString()
  Source_List_Type__c: string;

  @IsString()
  Remarks__c: string;

  @IsOptional()
  @IsString()
  Caution__c: string;

  @IsString()
  URL__c: string;

  @IsOptional()
  @IsString()
  Source_Agency_Name__c: string;

  @IsString()
  Service_Type__c: string;

  @IsString()
  RecordTypeId: string;

  @IsOptional()
  @IsString()
  Feedback_Type__c: string;
}

export class AgencyScreeningFeedbackDto {
  @ValidateNested()
  @Type(() => AgencyScreeningFeedbackInfodto)
  data: AgencyScreeningFeedbackInfodto;
}
