import {
  Body,
  Controller,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { AgencyVerificationService } from 'apps/src/system-layer/trulioo-verification/agencyVerification/agencyVerification.service';
import { v4 as uuidv4 } from 'uuid';
import { Request, Response } from 'express';
import { TruliooVerificationKybVerificationService } from './truliookyb.verification.process.service';

@Controller()
export class TruliooKybVerificationController {
  constructor(
    private readonly agencyVerificationService: AgencyVerificationService,
    private readonly truliooKybVerificationService: TruliooVerificationKybVerificationService,
  ) {}

  @Post('gus/trulioo/agencykyb/initiateverification/:enquiryTypeId')
  async submitKYBVerificationData(
    @Param('enquiryTypeId') enquiryTypeId: string,
    @Body() event: any,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const response =
        await this.truliooKybVerificationService.constructAgencyKYBObject(
          event,
          request['requestId'],
          request['account-Id'],
        );
      return await this.agencyVerificationService.submitKYBVerificationData(
        response,
      );
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('gus/trulioo/agencykyb/notifyverification/:enquiryTypeId')
  async notifyKYBFeedbackListener(
    @Body() event: any,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<any> {
    try {
      const result: any =
        await this.truliooKybVerificationService.notifyKYBFeedbackListener(
          `${request.headers['x-trulioo-signature']}`,
          event,
          uuidv4(),
        );
      return response.status(result.status).json(result.body);
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('gus/trulioo/agencykybenrollment/notifyverification/:enquiryTypeId')
  async notifyKYBEnrollmentFeedbackListener(
    @Body() event: any,
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<any> {
    try {
      const result =
        await this.truliooKybVerificationService.notifyKYBEnrollmentFeedbackListener(
          `${request.headers['authorization']}`,
          event,
          uuidv4(),
        );
      return response.status(result.status).json(result.body);
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('salesforce/gus/agentKyb/ongoingscreeningenquiry/:enquiryTypeId')
  async updateKYBEnquiryData(
    @Body() updateEventData: any,
    @Req() request: Request,
    @Query('isInsert') isInsert: string,
  ): Promise<any> {
    try {
      if (isInsert === 'true') {
        return this.truliooKybVerificationService.insertOngoingKYBScreeningData(
          request['requestId'],
          updateEventData,
        );
      }
      return await this.truliooKybVerificationService.updateKYBEnquiryData(
        request['requestId'],
        updateEventData,
      );
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('salesforce/gus/agentKyb/screeningenquiryfeedback/:enquiryTypeId')
  async createAgencyScreeningEnquiryFeedback(
    @Body() requestBody: any,
    @Param('enquiryTypeId') enquiryTypeId: string,
    @Req() request: Request,
    @Query() query: any,
  ): Promise<any> {
    try {
      return await this.truliooKybVerificationService.createScreeningEnquiryFeedback(
        request['requestId'],
        requestBody,
        query.serviceType,
      );
    } catch (error) {
      throw new HttpException(
        `Unable to create screening enquiry feedback record in salesforce - ${error}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
