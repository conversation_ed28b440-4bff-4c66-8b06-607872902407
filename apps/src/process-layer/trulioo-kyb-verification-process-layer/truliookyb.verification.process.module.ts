import { DynamicModule, Module } from '@nestjs/common';
import { TruliooVerificationModule } from 'apps/src/system-layer/trulioo-verification/truliooVerification.module';
import { LoggerModule } from '@gus-eip/loggers';
import { SqsModule } from 'apps/src/common/sqs/sqs.module';
import { TruliooVerificationKybVerificationService } from './truliookyb.verification.process.service';
import { TruliooKybVerificationController } from './truliookyb.verification.process.controller';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { DynamoDBModule } from 'apps/src/common/dynamoDB/dynamoDB.module';
import { GusSalesforceSystemModule } from 'apps/src/system-layer/gus-salesforce/gus.salesforce.system.module';

@Module({})
export class TruliooVerificationKybVerificationModule {
  static async forRoot(
    parameterStoreCacheService: ParameterStoreCacheService,
  ): Promise<DynamicModule> {
    const logGroupname = (await parameterStoreCacheService.getParameter(
      'KYB_KYC_LOGGER_LOG_GROUP_NAME',
    )) as string;

    return {
      module: TruliooVerificationKybVerificationModule,
      imports: [
        TruliooVerificationModule.forRoot(parameterStoreCacheService),
        LoggerModule.forRoot({
          region: process.env.REGION,
          logGroupName: logGroupname,
          options: 'KYBKYCLogger',
          teamWebhookUrl: '',
          isAlertNeeded: false,
        }),
        SqsModule,
        DynamoDBModule,
        GusSalesforceSystemModule,
      ],
      providers: [
        TruliooVerificationKybVerificationService,
        {
          provide: 'GUS_SALESFORCE_SOURCE',
          useFactory: async () => {
            return new SalesforceService(
              process.env.GUS_CONSUMER_KEY,
              process.env.GUS_CONSUMER_SECRET,
              process.env.GUS_AUTH_URL,
              process.env.ACCESS_TOKEN_SECRET,
              process.env.GUS_GRANT_TYPE,
              process.env.GUS_USER_NAME,
              process.env.GUS_PASSWORD,
            );
          },
          inject: [],
        },
      ],
      controllers: [TruliooKybVerificationController],
    };
  }
}
