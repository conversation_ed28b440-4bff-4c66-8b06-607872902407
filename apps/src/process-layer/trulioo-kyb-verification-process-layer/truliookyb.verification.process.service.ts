import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { SqsService } from 'apps/src/common/sqs/sqs.service';
import { HmacAuthenticationService } from 'apps/src/system-layer/trulioo-verification/hmacAuthentication/hmacAuthentication.service';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { DynamoDBService } from 'apps/src/common/dynamoDB/dynamoDB.service';
import { AccountKYBDto } from './dtos/accountKYB.dto';
import { OngoingScreeningEnquiryDto } from './dtos/newOngoingScreeningEnquiry.dto';
import { AgencyScreeningFeedbackDto } from './dtos/agencyScreeningFeedback.dto';
import * as kybFieldsConfig from './kybFieldsConfig.json';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import {
  GetSecretValueCommand,
  SecretsManagerClient,
} from '@aws-sdk/client-secrets-manager';
import { ApiBasicAuth } from '@nestjs/swagger';
import { AgencyBusinessEssentialsDto } from './dtos/agencyBusinessEssentials.dto';
import { GusSalesforceVerificationScreeningService } from 'apps/src/system-layer/gus-salesforce/verificationScreening/verificationScreening.service';

@Injectable()
export class TruliooVerificationKybVerificationService {
  constructor(
    @Inject('KYBKYCLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly loggerEnum: LoggerEnum,
    private readonly sqsService: SqsService,
    private readonly hmacAuthenticationService: HmacAuthenticationService,
    private readonly parameterStoreCacheService: ParameterStoreCacheService,
    @Inject('GUS_SALESFORCE_SOURCE')
    private readonly gusSalesforceService: SalesforceService,
    private readonly gusSalesforceVerificationScreeningService: GusSalesforceVerificationScreeningService,
  ) {}

  private async LogEvent(
    correlationId: string,
    source: string,
    destination: string,
    event: string,
    sourcePayload: any,
    destinationPayload: any,
    msg: string,
    usecase: string,
    accountId?: string,
  ): Promise<any> {
    try {
      await this.cloudWatchLoggerService.log(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        source,
        destination,
        event,
        usecase,
        sourcePayload,
        destinationPayload,
        msg,
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );
    } catch (error) {
      throw error;
    }
  }

  private async LogError(
    correlationId: string,
    source: string,
    destination: string,
    event: string,
    sourcePayload: any,
    destinationPayload: any,
    error: any,
    usecase: string,
    accountId?: string,
  ): Promise<any> {
    try {
      await this.cloudWatchLoggerService.error(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        source,
        destination,
        event,
        usecase,
        sourcePayload,
        destinationPayload,
        error.message,
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );
    } catch (error) {
      throw error;
    }
  }

  async constructAgencyKYBObject(
    agencyData: any,
    correlationId: string,
    accountId: string,
  ): Promise<any> {
    try {
      await this.LogEvent(
        correlationId,
        this.loggerEnum.Component
          .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.CREATE_SCREENING_ENQUIRY,
        agencyData,
        null,
        'Received KYB Data.',
        this.loggerEnum.UseCase.KYB_SCREENING,
        accountId,
      );
      const fields = kybFieldsConfig;
      const response = Object.entries(agencyData)
        .map(([fieldName, fieldValue]) => {
          const element = fields.elements.find(
            (element) => element.name === fieldName,
          );
          return element ? { [element.id]: fieldValue } : null;
        })
        .reduce((acc, curr) => {
          return curr ? { ...acc, ...curr } : acc;
        }, {});
      return response;
    } catch (error) {
      await this.LogError(
        correlationId,
        this.loggerEnum.Component
          .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.CREATE_SCREENING_ENQUIRY,
        agencyData,
        error,
        error,
        this.loggerEnum.UseCase.KYB_SCREENING,
        accountId,
      );
      throw error;
    }
  }

  private async verifyRequest(
    payload: any,
    truliooSignature: string,
    correlationId: string,
  ): Promise<any> {
    if (
      await this.hmacAuthenticationService.verifyHmacSignature(
        JSON.stringify(payload),
        truliooSignature,
      )
    ) {
      await this.LogEvent(
        correlationId,
        this.loggerEnum.Component
          .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
        this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
        this.loggerEnum.Event.VALIDATE_REQUESTER,
        { payload, truliooSignature },
        {
          status: 200,
          body: {
            challenge: payload?.challenge || truliooSignature,
          },
        },
        'HMAC VERIFICATION SUCCESSFUL',
        this.loggerEnum.UseCase.KYB_SCREENING,
      );
      return {
        status: 200,
        body: {
          challenge: payload?.challenge || truliooSignature,
        },
      };
    } else {
      throw new HttpException('Unauthorized', 401);
    }
  }

  async notifyKYBFeedbackListener(
    truliooSignature: any,
    event: any,
    correlationId: string,
  ): Promise<any> {
    try {
      if (event.type === 'URL_VERIFICATION') {
        await this.LogEvent(
          correlationId,
          this.loggerEnum.Component
            .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
          this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
          this.loggerEnum.Event.VALIDATE_REQUESTER,
          { event, truliooSignature },
          null,
          'URL VERIFICATION INITIATED',
          this.loggerEnum.UseCase.KYB_SCREENING,
        );
        return await this.verifyRequest(event, truliooSignature, correlationId);
      } else if (event.type === 'EVENT_CALLBACK') {
        await this.LogEvent(
          correlationId,
          this.loggerEnum.Component
            .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
          this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
          this.loggerEnum.Event.PUBLISH_MESSAGE_TO_SQS,
          { event, truliooSignature },
          null,
          'NOTIFY FEEDBACK LISTENER',
          this.loggerEnum.UseCase.KYB_SCREENING,
        );
        if (await this.verifyRequest(event, truliooSignature, correlationId)) {
          if (
            (event?.event?.type === 'SERVICE_SUBMIT' &&
              event.serviceType === 'TRULIOO_BUSINESS_WL' &&
              event.serviceData?.transactionInfo &&
              event.serviceData?.watchlistResults &&
              event?.serviceData?.serviceStatus === 'COMPLETED') ||
            (event?.event?.type === 'SERVICE_SUBMIT' &&
              event.serviceType === 'TRULIOO_KYB_VERIFY' &&
              event.serviceData?.transactionInfo &&
              event?.serviceData?.serviceStatus === 'COMPLETED')
          ) {
            let publishMessage = event.retry === 0 ? true : false;
            if (!publishMessage) {
              const screeningData =
                await this.gusSalesforceVerificationScreeningService.getScreeningDataById(
                  (await this.parameterStoreCacheService.getParameter(
                    'KYB_ENQUIRY_TYPE_NEW',
                  )) as string,
                  'Id',
                  `Screening_Identifiers__c = '{"ClientID":"${event.userId}"}'`,
                );
              publishMessage = screeningData ? false : true;
            }
            if (publishMessage) {
              const sqsMsgGrpID =
                event.serviceType === 'TRULIOO_KYB_VERIFY'
                  ? 'KYB_KYC_BEs_FEEDBACK_SQS_MESSAGE_ID'
                  : 'KYB_KYC_WL_FEEDBACK_SQS_MESSAGE_ID';

              const publishMessageResponse: any =
                await this.sqsService.sendMessage(
                  JSON.stringify(event),
                  (await this.parameterStoreCacheService.getParameter(
                    'KYB_KYC_FEEDBACK_SQS_URL',
                  )) as string,
                  process.env.REGION,
                  (await this.parameterStoreCacheService.getParameter(
                    sqsMsgGrpID,
                  )) as string,
                );
              await this.LogEvent(
                correlationId,
                this.loggerEnum.Component
                  .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
                this.loggerEnum.Component
                  .GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
                this.loggerEnum.Event.PUBLISH_MESSAGE_TO_SQS,
                { event, truliooSignature },
                { 'Generated MessageID': publishMessageResponse.MessageId },
                'NOTIFY FEEDBACK LISTENER',
                this.loggerEnum.UseCase.KYB_SCREENING,
              );
            }
          }
          return {
            status: 200,
            body: {
              challenge: event?.challenge || truliooSignature,
            },
          };
        }
      } else {
        throw new BadRequestException('Invalid event');
      }
    } catch (error) {
      await this.LogError(
        correlationId,
        this.loggerEnum.Component
          .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
        this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
        this.loggerEnum.Event.PUBLISH_MESSAGE_TO_SQS,
        { event, truliooSignature },
        error,
        error,
        this.loggerEnum.UseCase.KYB_SCREENING,
      );
      throw error;
    }
  }

  async getTruliooClientCreds() {
    try {
      const client = new SecretsManagerClient({
        region: process.env.REGION,
      });
      const response: any = await client.send(
        new GetSecretValueCommand({
          SecretId: (await this.parameterStoreCacheService.getParameter(
            'TRULIOO_OAUTH_CREDS_SECRET_NAME',
          )) as string,
        }),
      );
      const { client_id, client_secret } = JSON.parse(response.SecretString);
      return {
        client_id,
        client_secret,
      };
    } catch (error) {
      throw new HttpException(
        `Failed to retrieve Trulioo client creds: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async notifyKYBEnrollmentFeedbackListener(
    headers: any,
    event: any,
    correlationId: string,
  ): Promise<any> {
    try {
      await this.LogEvent(
        correlationId,
        this.loggerEnum.Component
          .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
        this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
        this.loggerEnum.Event.PUBLISH_MESSAGE_TO_SQS,
        event,
        null,
        'NOTIFY FEEDBACK LISTENER',
        this.loggerEnum.UseCase.KYB_SCREENING,
      );

      const [authType, authToken] = headers.split(' ');
      const [clientId, clientSecret] = Buffer.from(authToken, 'base64')
        .toString('utf8')
        .split(':');

      const trulioo_client_creds = await this.getTruliooClientCreds();
      if (
        !(authType == 'Basic') ||
        !(clientId == trulioo_client_creds.client_id) ||
        !(clientSecret == trulioo_client_creds.client_secret)
      ) {
        throw new UnauthorizedException('Invalid credentials');
      }

      const publishMessageResponse: any = await this.sqsService.sendMessage(
        JSON.stringify(event),
        (await this.parameterStoreCacheService.getParameter(
          'KYB_KYC_FEEDBACK_SQS_URL',
        )) as string,
        process.env.REGION,
        (await this.parameterStoreCacheService.getParameter(
          'KYB_KYC_ENROLLMENT_FEEDBACK_SQS_MESSAGE_ID',
        )) as string,
      );
      await this.LogEvent(
        correlationId,
        this.loggerEnum.Component
          .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
        this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
        this.loggerEnum.Event.PUBLISH_MESSAGE_TO_SQS,
        event,
        { 'Generated MessageID': publishMessageResponse.MessageId },
        'NOTIFY FEEDBACK LISTENER',
        this.loggerEnum.UseCase.KYB_SCREENING,
      );
      return {
        status: 200,
        body: {
          response: 'Initiated Enrollment Enquiry Feedback Workflow',
        },
      };
    } catch (error) {
      await this.LogError(
        correlationId,
        this.loggerEnum.Component
          .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
        this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
        this.loggerEnum.Event.PUBLISH_MESSAGE_TO_SQS,
        event,
        error,
        error,
        this.loggerEnum.UseCase.KYB_SCREENING,
      );
      throw error;
    }
  }

  private async buildCompositeRequest(
    object: string,
    referenceId: string,
    body: any,
  ): Promise<any> {
    const attributes = {
      type: object,
      referenceId,
    };
    const compositeRequest = {
      attributes,
      ...body,
    };
    return compositeRequest;
  }

  async insertOngoingKYBScreeningData(
    correlationId: string,
    screeningEnquiry: Array<any>,
  ): Promise<any> {
    const compositeRequestScreening: any = [];
    let cnt = 0;
    try {
      await this.LogEvent(
        correlationId,
        this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.CREATE_SCREENING_ENQUIRY,
        screeningEnquiry,
        null,
        'Insert KYB Enrollment Data to SF',
        this.loggerEnum.UseCase.KYB_ENROLLMENT,
      );
      for (const screeningEnquiryRec of screeningEnquiry) {
        const screeningEnquiryDto = plainToInstance(
          OngoingScreeningEnquiryDto,
          screeningEnquiryRec,
        );
        const validationErrors = await validate(screeningEnquiryDto);

        if (validationErrors.length > 0) {
          throw new BadRequestException(
            'Input validation failed for screeningEnquiry',
            JSON.stringify(validationErrors),
          );
        }
        compositeRequestScreening.push(
          await this.buildCompositeRequest(
            'Screening_Enquiry__c',
            `Screening_Enquiry__c_${cnt}`,
            screeningEnquiryRec,
          ),
        );
        ++cnt;
      }
      const screeningInsertResponse =
        await this.gusSalesforceService.compositeRequestSobjects(
          compositeRequestScreening,
        );
      if (screeningInsertResponse) {
        await this.LogEvent(
          correlationId,
          this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
          this.loggerEnum.Component.SALESFORCE,
          this.loggerEnum.Event.UPDATE_KYB_ENROLLMENT_DATA,
          screeningEnquiry,
          { compositeRequestScreening, screeningInsertResponse },
          this.loggerEnum.Event.OPERATION_COMPLETED,
          this.loggerEnum.UseCase.KYB_ENROLLMENT,
        );
      }
    } catch (error) {
      await this.LogError(
        correlationId,
        this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.CREATE_SCREENING_ENQUIRY,
        screeningEnquiry,
        compositeRequestScreening,
        error,
        this.loggerEnum.UseCase.KYB_ENROLLMENT,
      );
      throw error;
    }
  }

  async updateKYBEnquiryData(
    correlationId: string,
    updateEventData: any,
  ): Promise<any> {
    let cnt = 0;
    const compositeRequestAccount = [];
    const compositeRequestScreening = [];
    try {
      const account: Array<any> = updateEventData.account;
      let screeningEnquiry: Array<{
        Id: string;
        Screening_Identifiers__c: any;
      }> = updateEventData.screeningEnquiry;
      await this.LogEvent(
        correlationId,
        this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.UPDATE_KYB_ENROLLMENT_DATA,
        { account, screeningEnquiry },
        null,
        'Update KYB Enrollment Data to SF',
        this.loggerEnum.UseCase.KYB_ENROLLMENT,
      );

      for (const accountRec of account) {
        const accountDto = plainToInstance(AccountKYBDto, accountRec);
        const validationErrors = await validate(accountDto);

        if (validationErrors.length > 0) {
          throw new BadRequestException(
            'Input validation failed for account',
            JSON.stringify(validationErrors),
          );
        }
        compositeRequestAccount.push(
          await this.buildCompositeRequest('Account', `Account_${cnt}`, {
            Id: accountRec.Id,
            Ongoing_KYB_Status__c: 'Enrolled',
            Ongoing_KYB_Timestamp__c: accountRec.Ongoing_KYB_Timestamp__c,
          }),
        );
        ++cnt;
      }

      cnt = 0;
      for (const screeningEnquiryRec of screeningEnquiry) {
        compositeRequestScreening.push(
          await this.buildCompositeRequest(
            'Screening_Enquiry__c',
            `Screening_Enquiry__c_${cnt}`,
            screeningEnquiryRec,
          ),
        );
        ++cnt;
      }

      const accountUpdateResponse =
        await this.gusSalesforceService.compositeRequestSobjects(
          compositeRequestAccount,
          'PATCH',
        );
      if (accountUpdateResponse) {
        await this.LogEvent(
          correlationId,
          this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
          this.loggerEnum.Component.SALESFORCE,
          this.loggerEnum.Event.UPDATE_KYB_ENROLLMENT_DATA,
          { account, screeningEnquiry },
          { compositeRequestAccount, accountUpdateResponse },
          this.loggerEnum.Event.OPERATION_COMPLETED,
          this.loggerEnum.UseCase.KYB_ENROLLMENT,
        );
      }

      const screeningUpdateResponse =
        await this.gusSalesforceService.compositeRequestSobjects(
          compositeRequestScreening,
          'PATCH',
        );
      if (screeningUpdateResponse) {
        await this.LogEvent(
          correlationId,
          this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
          this.loggerEnum.Component.SALESFORCE,
          this.loggerEnum.Event.UPDATE_KYB_ENROLLMENT_DATA,
          { account, screeningEnquiry },
          { compositeRequestScreening, screeningUpdateResponse },
          this.loggerEnum.Event.OPERATION_COMPLETED,
          this.loggerEnum.UseCase.KYB_ENROLLMENT,
        );
      }

      return { accountUpdateResponse, screeningUpdateResponse };
    } catch (error) {
      await this.LogError(
        correlationId,
        this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.UPDATE_KYB_ENROLLMENT_DATA,
        updateEventData,
        { compositeRequestAccount, compositeRequestScreening },
        error,
        this.loggerEnum.UseCase.KYB_ENROLLMENT,
      );
      throw error;
    }
  }

  async createScreeningEnquiryFeedback(
    correlationId: string,
    feedbackData: Array<any>,
    serviceType: string,
  ): Promise<any> {
    let compositeRequestScreening: any = [];
    let cnt = 0;
    try {
      let screeningEnquiryFeedbackDto;
      for (const screeningEnquiryFeedbackRec of feedbackData) {
        if (
          serviceType ===
          (await this.parameterStoreCacheService.getParameter(
            'SERVICETYPE_BUSINESWATCHLISTSCREENING',
          ))
        ) {
          screeningEnquiryFeedbackDto = plainToInstance(
            AgencyScreeningFeedbackDto,
            screeningEnquiryFeedbackRec,
          );
        } else if (
          serviceType ==
          (await this.parameterStoreCacheService.getParameter(
            'SERVICETYPE_BUSINESSESSENTIALS',
          ))
        ) {
          screeningEnquiryFeedbackDto = plainToInstance(
            AgencyBusinessEssentialsDto,
            screeningEnquiryFeedbackRec,
          );
        } else {
          throw new BadRequestException('Invalid serviceType');
        }
        const validationErrors = await validate(screeningEnquiryFeedbackDto);

        if (validationErrors.length > 0) {
          throw new BadRequestException(
            'Input validation failed for screeningEnquiryFeedback',
            JSON.stringify(validationErrors),
          );
        }
        compositeRequestScreening.push(
          await this.buildCompositeRequest(
            'Screening_Enquiry_Feedback__c',
            `Screening_Enquiry_Feedback__c${cnt}`,
            screeningEnquiryFeedbackRec?.data,
          ),
        );
        ++cnt;
      }

      const responses = [];

      for (let i = 0; i < compositeRequestScreening.length; i += 200) {
        const compositeRequestScreeningChunk = compositeRequestScreening.slice(
          i,
          i + 200,
        );
        const screeningInsertResponse =
          await this.gusSalesforceService.compositeRequestSobjects(
            compositeRequestScreeningChunk,
          );
        responses.push(screeningInsertResponse);
      }

      if (responses.length > 0) {
        await this.LogEvent(
          correlationId,
          this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_HANDLER,
          this.loggerEnum.Component.SALESFORCE,
          this.loggerEnum.Event.SAVE_KYB_SCREENING_FEEDBACK,
          feedbackData,
          { compositeRequestScreening, responses },
          this.loggerEnum.Event.OPERATION_COMPLETED,
          this.loggerEnum.UseCase.KYB_SCREENING,
        );
      }
      return responses;
    } catch (error) {
      await this.LogError(
        correlationId,
        this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_HANDLER,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.SAVE_KYB_SCREENING_FEEDBACK,
        feedbackData,
        compositeRequestScreening,
        error,
        this.loggerEnum.UseCase.KYB_SCREENING,
      );
      throw error;
    }
  }
}
