import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { makeHTTPRequest } from 'apps/libs/utils/utils';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { DynamoDBService } from 'apps/src/common/dynamoDB/dynamoDB.service';
import { AxiosRequestConfig } from 'axios';

@Injectable()
export class IELTSValidationService {
  constructor(
    private readonly httpService: HttpService,
    private readonly parameterStoreCacheService: ParameterStoreCacheService,
    private readonly dynamodbService: DynamoDBService,
  ) {}

  async getDocValidationsStatus(validationData: any): Promise<any> {
    try {
      const validationKeyRec: any = await this.dynamodbService.getObject(
        `${await this.parameterStoreCacheService.getParameter(
          'CERT_VALIDATION_KEYS_TABLE_NAME',
        )}`,
        {
          PK: validationData.brand,
          SK: validationData.proficiencyQualification,
        },
      );

      const config: AxiosRequestConfig = {
        headers: {
          Authorization: `Basic ${Buffer.from(
            `${validationKeyRec?.Item?.userName}:${validationKeyRec?.Item?.password}`,
          ).toString('base64')}`,
        },
      };

      const url = `${validationKeyRec.Item.proficiencyQualificationApiUrl}?trfNumber=${validationData.TrfNo}`;
      const response: Record<string, any> = (
        await makeHTTPRequest<any>(this.httpService, 'get', url, null, config)
      ).data;

      if (!response?.results?.length) {
        throw new BadRequestException('No Exam Found!');
      }
      return {
        test_date: response.results[0]?.testDate?.replace(
          /(\d{4})(\d{2})(\d{2})/,
          '$1-$2-$3',
        ),
        overall_score: response.results[0]?.overallBandScore,
        swrl_speaking_subscore: response.results[0]?.speakingScore,
        swrl_writing_subscore: response.results[0]?.writingScore,
        swrl_reading_subscore: response.results[0]?.readingScore,
        swrl_listening_subscore: response.results[0]?.listeningScore,
      };
    } catch (error) {
      throw error;
    }
  }
}
