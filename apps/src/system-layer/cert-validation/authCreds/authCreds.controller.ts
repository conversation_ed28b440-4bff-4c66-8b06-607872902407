import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Request } from 'express';
import * as uuid from 'uuid';
import { AuthCredsService } from './authCreds.service';
import { AuthCredsRequestDto } from './dtos/authCredsRequest.dto';
import { AuthCredsUpdateDto } from './dtos/authCredsUpdate.dto';

@Controller('gus')
@UsePipes(new ValidationPipe({ whitelist: true }))
export class AuthCredsController {
  constructor(private readonly authCredsService: AuthCredsService) {}

  @Get('proficiencyQualification/testProviders/list/authCreds')
  async getAuthCredentials(
    @Req() request: Request,
    @Query() query: AuthCredsRequestDto,
  ): Promise<any> {
    try {
      const response = await this.authCredsService.getAuthCredentials(
        request['requestId'] ?? uuid.v4(),
        query.brand,
        query.proficiencyQualification,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Post('proficiencyQualification/testProvider/authCreds')
  async updateAuthCredentials(
    @Req() request: Request,
    @Body() body: AuthCredsUpdateDto,
  ): Promise<any> {
    try {
      const response = await this.authCredsService.updateAuthCredentials(
        request['requestId'] ?? uuid.v4(),
        body,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
}
