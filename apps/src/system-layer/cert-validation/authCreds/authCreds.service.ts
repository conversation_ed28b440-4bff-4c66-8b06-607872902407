import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import { Inject, Injectable } from '@nestjs/common';
import { DynamoDBService } from 'apps/src/common/dynamoDB/dynamoDB.service';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';

@Injectable()
export class AuthCredsService {
  constructor(
    @Inject('GusMiddlewareLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly loggerEnum: LoggerEnum,
    private readonly parameterStoreCacheService: ParameterStoreCacheService,
    private readonly dynamodbService: DynamoDBService,
  ) {}

  async getAuthCredentials(
    correlationId: string,
    brand?: string,
    proficiencyQualification?: string,
  ): Promise<any> {
    try {
      await this.cloudWatchLoggerService.log(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OAP_ADMIN_PORTAL,
        this.loggerEnum.Component.OAP_ADMIN_PORTAL,
        this.loggerEnum.Event
          .FETCH_PROFICIENCY_QUALIFICATION_TEST_PROVIDER_AUTH_CREDS,
        this.loggerEnum.UseCase.LIST_AUTH_CREDS,
        { brand, proficiencyQualification },
        null,
        'Initiate Fetch Auth Creds',
        brand,
        null,
        `gus-middleware-service/ProficiencyQualificationTestProviderCreds/${correlationId}`,
        'correlationId',
        correlationId,
      );

      const tableName = (await this.parameterStoreCacheService.getParameter(
        'CERT_VALIDATION_KEYS_TABLE_NAME',
      )) as string;

      if (proficiencyQualification) {
        return this.dynamodbService.getObject(tableName, {
          PK: brand,
          SK: proficiencyQualification,
        });
      }

      return this.dynamodbService.queryObjects({
        TableName: tableName,
        KeyConditionExpression: '#pk = :pkValue',
        ExpressionAttributeNames: { '#pk': 'PK' },
        ExpressionAttributeValues: { ':pkValue': brand },
      });
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OAP_ADMIN_PORTAL,
        this.loggerEnum.Component.OAP_ADMIN_PORTAL,
        this.loggerEnum.Event
          .FETCH_PROFICIENCY_QUALIFICATION_TEST_PROVIDER_AUTH_CREDS,
        this.loggerEnum.UseCase.LIST_AUTH_CREDS,
        { brand, proficiencyQualification },
        null,
        error,
        brand,
        null,
        `gus-middleware-service/ProficiencyQualificationTestProviderCreds/${correlationId}`,
        'correlationId',
        correlationId,
      );
      throw error;
    }
  }

  async updateAuthCredentials(
    correlationId: string,
    authData: Record<string, any>,
  ): Promise<any> {
    try {
      await this.cloudWatchLoggerService.log(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OAP_ADMIN_PORTAL,
        this.loggerEnum.Component.OAP_ADMIN_PORTAL,
        this.loggerEnum.Event
          .UPDATE_PROFICIENCY_QUALIFICATION_TEST_PROVIDER_AUTH_CREDS,
        this.loggerEnum.UseCase.SAVE_AUTH_CREDS,
        authData,
        null,
        'Initiate Update Auth Creds',
        authData.brand,
        null,
        `gus-middleware-service/ProficiencyQualificationTestProviderCreds/${correlationId}`,
        'correlationId',
        correlationId,
      );
      const tableName = await this.parameterStoreCacheService.getParameter(
        'CERT_VALIDATION_KEYS_TABLE_NAME',
      );

      return await this.dynamodbService.updateObject(
        (await this.parameterStoreCacheService.getParameter(
          'CERT_VALIDATION_KEYS_TABLE_NAME',
        )) as string,
        {
          PK: authData.brand,
          SK: authData.proficiencyQualification,
        },
        {
          updatedDate: new Date().toISOString(),
          proficiencyQualificationApiUrl:
            authData.proficiencyQualificationApiUrl,
          updatedBy: authData.updatedBy,
          apiKey: authData?.apiKey,
          userName: authData?.userName,
          password: authData?.password,
          clientId: authData?.clientId,
          clientSecret: authData?.clientSecret,
          grantType: authData?.grantType,
          scope: authData?.scope,
          tokenRequestUrl: authData?.tokenRequestUrl,
        },
      );
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.OAP_ADMIN_PORTAL,
        this.loggerEnum.Component.OAP_ADMIN_PORTAL,
        this.loggerEnum.Event
          .UPDATE_PROFICIENCY_QUALIFICATION_TEST_PROVIDER_AUTH_CREDS,
        this.loggerEnum.UseCase.SAVE_AUTH_CREDS,
        authData,
        null,
        error,
        authData.brand,
        null,
        `gus-middleware-service/ProficiencyQualificationTestProviderCreds/${correlationId}`,
        'correlationId',
        correlationId,
      );
      throw error;
    }
  }
}
