import { IsE<PERSON>, <PERSON>NotEmpty, IsOptional, IsString } from 'class-validator';

export class AuthCredsUpdateDto {
  @IsNotEmpty()
  @IsString()
  brand: string;

  @IsNotEmpty()
  @IsString()
  proficiencyQualification: string;

  @IsNotEmpty()
  @IsString()
  proficiencyQualificationApiUrl: string;

  @IsNotEmpty()
  @IsEmail()
  updatedBy: string;

  @IsOptional()
  apiKey?: any;

  @IsOptional()
  userName?: any;

  @IsOptional()
  password?: any;

  @IsOptional()
  clientId?: any;

  @IsOptional()
  clientSecret?: any;

  @IsOptional()
  grantType?: any;

  @IsOptional()
  scope?: any;

  @IsOptional()
  tokenRequestUrl?: any;
}
