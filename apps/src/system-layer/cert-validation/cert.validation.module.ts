import { LoggerModule } from '@gus-eip/loggers';
import { HttpModule } from '@nestjs/axios';
import { DynamicModule, Module } from '@nestjs/common';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { DuolingoValidationService } from './duolingoValidation/duolingoValidation.service';
import { DynamoDBModule } from 'apps/src/common/dynamoDB/dynamoDB.module';
import { IELTSValidationService } from './IELTSValidation/IELTSValidation.service';
import { AuthCredsController } from './authCreds/authCreds.controller';
import { AuthCredsService } from './authCreds/authCreds.service';
@Module({})
export class CertValidationModule {
  static async forRoot(
    parameterStoreCacheService: ParameterStoreCacheService,
  ): Promise<DynamicModule> {
    const logGroupname = (await parameterStoreCacheService.getParameter(
      'GUS_MIDDLEWARE_LOGGER_LOG_GROUP_NAME',
    )) as string;

    return {
      module: CertValidationModule,
      imports: [
        LoggerModule.forRoot({
          region: process.env.REGION,
          logGroupName: logGroupname,
          options: 'GusMiddlewareLogger',
          teamWebhookUrl: '',
          isAlertNeeded: false,
        }),
        HttpModule,
        DynamoDBModule,
      ],
      controllers: [AuthCredsController],
      providers: [
        DuolingoValidationService,
        IELTSValidationService,
        AuthCredsService,
      ],
      exports: [DuolingoValidationService, IELTSValidationService],
    };
  }
}
