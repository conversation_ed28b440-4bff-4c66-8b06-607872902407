import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { makeHTTPRequest } from 'apps/libs/utils/utils';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { DynamoDBService } from 'apps/src/common/dynamoDB/dynamoDB.service';

@Injectable()
export class DuolingoValidationService {
  constructor(
    private readonly httpService: HttpService,
    private readonly parameterStoreCacheService: ParameterStoreCacheService,
    private readonly dynamodbService: DynamoDBService,
  ) {}

  async getDocValidationsStatus(validationData: any): Promise<any> {
    try {
      const validationKeyRec: any = await this.dynamodbService.getObject(
        `${await this.parameterStoreCacheService.getParameter(
          'CERT_VALIDATION_KEYS_TABLE_NAME',
        )}`,
        {
          PK: validationData.brand,
          SK: validationData.proficiencyQualification,
        },
      );

      const url = `https://${validationKeyRec.Item.apiKey}@${validationKeyRec.Item.proficiencyQualificationApiUrl}?birthdate=${validationData.DOB}&certificate_id=${validationData.TrfNo}`;
      const response: Record<string, any> = (
        await makeHTTPRequest<any>(this.httpService, 'get', url, null)
      ).data;

      if (response && !response.error) {
        return {
          test_date: response.test_date,
          overall_score: response.overall_score,
          swrl_speaking_subscore: response.swrl_speaking_subscore,
          swrl_writing_subscore: response.swrl_writing_subscore,
          swrl_reading_subscore: response.swrl_reading_subscore,
          swrl_listening_subscore: response.swrl_listening_subscore,
        };
      } else {
        const exception = !response
          ? new InternalServerErrorException('Response is undefined')
          : new BadRequestException(response.error ?? response);
        throw exception;
      }
    } catch (error) {
      throw error;
    }
  }
}
