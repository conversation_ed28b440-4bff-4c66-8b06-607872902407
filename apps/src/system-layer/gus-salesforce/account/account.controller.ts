import {
  Controller,
  Body,
  HttpStatus,
  HttpException,
  Get,
  Patch,
  Param,
  Req,
  Query,
} from '@nestjs/common';
import { GusSalesforceAccountService } from './account.service';

import { Request } from 'express';
import * as salesForceHelper from '../../../../libs/utils/utils';

@Controller('/salesforce/gus')
export class GusSalesforceAccountController {
  constructor(
    private readonly gusSalesforceAccountService: GusSalesforceAccountService,
  ) {}
  @Get('/getAccount')
  async getAccountDetails(@Req() request: Request): Promise<any> {
    try {
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          'Account',
          request['allowedEntities'],
        );
      return await this.gusSalesforceAccountService.getAccounts(
        fieldsRequired,
        `where ${whereClause}`,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/personaccount/:email')
  async getPersonAccount(
    @Param('email') email: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const { whereClause, fieldsRequired } =
        await salesForceHelper.queryBuilder(
          'Account',
          request['allowedEntities'],
        );

      return await this.gusSalesforceAccountService.getPersonAccount(
        email,
        fieldsRequired,
        whereClause,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/personaccountbystudentexternalid/:id')
  async getPersonAccountByStudentExternalId(
    @Param('id') id: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const { whereClause, fieldsRequired } =
        await salesForceHelper.queryBuilder(
          'Account',
          request['allowedEntities'],
        );

      return await this.gusSalesforceAccountService.getPersonAccountByStudentExternalId(
        id,
        fieldsRequired,
        whereClause,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Patch('/updateAccount/:id')
  async updateAccount(
    @Body() accountDetails: any,
    @Param('id') id: string,
  ): Promise<any> {
    try {
      return await this.gusSalesforceAccountService.updateAccount(
        accountDetails,
        id,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/accounts/nokybenrolled/:enquiryTypeId')
  async getNonKybenrolledAccounts(): Promise<any> {
    try {
      return await this.gusSalesforceAccountService.getNonKybenrolledAccounts(
        'Id, Name',
      );
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/personaccountbybrand')
  async getPersonAccountByEmailAndBrand(
    @Query('email') email: string,
    @Query('brand') brand: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const { whereClause, fieldsRequired } = await salesForceHelper.queryBuilder(
        'Account',
        request['allowedEntities'],
      );

      let condition = `BusinessUnitFilter__c = '${brand}'`;
      if (whereClause) {
        condition = `${condition} AND ${whereClause}`;
      }

      return await this.gusSalesforceAccountService.getPersonAccountByBrand(
        email,
        fieldsRequired,
        condition,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
