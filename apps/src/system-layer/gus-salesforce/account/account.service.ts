import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';

@Injectable()
export class GusSalesforceAccountService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getAccounts(
    queryFields,
    queryCondition = null,
    isTotalRequired = false,
  ): Promise<any> {
    const accounts = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Account',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return accounts;
  }

  async  getPersonAccount(
    email,
    requiredFields,
    queryCondition = null,
  ): Promise<any> {
    let query = `Select ${requiredFields} from Account where PersonEmail = '${email}' and RecordTypeId = '${process.env.PERSON_ACCOUNT_RECORDTYPE_ID}'`;
    if (queryCondition != null) {
      query = `${query} and ${queryCondition}`;
    }
    console.log('person account query', query);
    const personAccountDetails = await this.gusSalesforceService.executeAPI(
      query,
      'GET',
    );
    return personAccountDetails;
  }

  async  getPersonAccountByStudentExternalId(
    email,
    requiredFields,
    queryCondition = null,
  ): Promise<any> {
    let query = `Select ${requiredFields} from Account where Student_External_ID__c = '${email}' and RecordTypeId = '${process.env.PERSON_ACCOUNT_RECORDTYPE_ID}'`;
    if (queryCondition != null) {
      query = `${query} and ${queryCondition}`;
    }
    console.log('person account query', query);
    const personAccountDetails = await this.gusSalesforceService.executeAPI(
      query,
      'GET',
    );
    return personAccountDetails;
  }
  
  async getPersonAccountByBrand(
    email: string,
    requiredFields: string,
    queryCondition: string = null,
  ): Promise<any> {
    let query = `Select ${requiredFields} from Account where PersonEmail = '${email}' and RecordTypeId = '${process.env.PERSON_ACCOUNT_RECORDTYPE_ID}'`;
    
    if (queryCondition) {
      query = `${query} and ${queryCondition}`;
    }

    const personAccountDetails = await this.gusSalesforceService.executeAPI(
      encodeURIComponent(query),
      'GET',
    );
    return personAccountDetails[0];
  }

  async updateAccount(accountUpdateDetails, accountId): Promise<any> {
    if (accountId) {
      const response = await this.gusSalesforceService.executeAPI(
        `sobjects/Account/${accountId}`,
        'PATCH',
        accountUpdateDetails,
      );
      return response.data;
    } else {
      throw new NotFoundException(`Account Id Required`);
    }
  }
  async createAccount(data): Promise<any> {
    try {
      if (!this.utilitiesService.checkRequiredField(data, 'Account')) {
        return;
      }
      const response = await this.gusSalesforceService.executeAPI(
        `sobjects/Account`,
        'POST',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      throw error;
    }
  }

  async getNonKybenrolledAccounts(requiredFields: any): Promise<any> {
    try {
      let query = `SELECT ${requiredFields} FROM Account
                    WHERE (Ongoing_KYB_Status__c = 'Not Enrolled' OR Ongoing_KYB_Status__c = null) 
                    AND AgentStatus__c = 'Active' 
                    AND Ongoing_KYB_Timestamp__c = null
                    LIMIT 60`;
      const AccountDetails = await this.gusSalesforceService.executeAPI(
        query,
        'GET',
      );
      return AccountDetails;
    } catch (error) {
      throw error;
    }
  }
}
