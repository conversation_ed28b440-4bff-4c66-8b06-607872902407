import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';

@Injectable()
export class GusSalesforceAccountAgeContractService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getAccountAgeContract(
    queryFields,
    queryCondition = null,
    isTotalRequired = false,
  ): Promise<any> {
    const accounts = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Account_Age_Contract__c',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return accounts;
  }
}
