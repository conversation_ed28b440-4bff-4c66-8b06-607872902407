import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceApplicationService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async createApplication(data): Promise<any> {
    try {
      if (!this.utilitiesService.checkRequiredField(data, 'Application__c')) {
        return;
      }
      const response = await this.gusSalesforceService.executeAPI(
        `sobjects/Application__c`,
        'POST',
        data,
      );
      return response.data.id;
    } catch (error) {
      throw error;
    }
  }
  async updateApplication(id, data): Promise<any> {
    const response = await this.gusSalesforceService.executeAPI(
      `sobjects/Application__c/${id}`,
      'PATCH',
      data,
    );
    return {
      statusCode: response.status,
    };
  }
  async getApplication(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const opportunity = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Application__c',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return opportunity;
  }
}
