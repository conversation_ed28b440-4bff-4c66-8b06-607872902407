import {
  Controller,
  Body,
  HttpStatus,
  HttpException,
  Post,
} from '@nestjs/common';
import { GusSalesforceApplicationFile } from './applicationFile.service';

@Controller('/salesforce/gus')
export class GusSalesforceApplicationFileController {
  constructor(
    private readonly gusSalesforceApplicationFile: GusSalesforceApplicationFile,
  ) {}
  @Post('/applicationFile')
  async updateApplicationFile(@Body() details: any): Promise<any> {
    try {
      return await this.gusSalesforceApplicationFile.createApplicationFile(
        details,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
