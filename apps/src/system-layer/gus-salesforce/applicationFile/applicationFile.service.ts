import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class GusSalesforceApplicationFile {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
  ) {}
  async createApplicationFile(data): Promise<any> {
    try {
      const response = await this.gusSalesforceService.executeAPI(
        `sobjects/Application_File__c`,
        'POST',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      throw error;
    }
  }
  async updateApplicationFile(id, data): Promise<any> {
    const response = await this.gusSalesforceService.executeAPI(
      `sobjects/Application_File__c/${id}`,
      'PATCH',
      data,
    );
    return {
      statusCode: response.status,
    };
  }
}
