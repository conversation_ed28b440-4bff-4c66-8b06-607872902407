import { Controller, HttpStatus, HttpException, Get } from '@nestjs/common';
import { GusSalesforceBusinessunitService } from './businessunit.service';
import { BusinessUnit__c } from 'apps/libs/salesforce/default.fields';
@Controller('/salesforce/gus')
export class GusSalesforceBusinessunitController {
  constructor(
    private readonly gusSalesforceBusinessunitService: GusSalesforceBusinessunitService,
  ) {}
  @Get('/businessunit')
  async getBusinessunits(): Promise<any> {
    try {
      return await this.gusSalesforceBusinessunitService.getBusinessUnits(
        BusinessUnit__c,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
