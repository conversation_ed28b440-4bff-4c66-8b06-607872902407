import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceBusinessunitService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getBusinessUnits(
    queryFields,
    queryCondition = null,
    isTotalRequired = false,
  ) {
    let BusinessunitDetails;
    try {
      BusinessunitDetails = await this.gusSalesforceService.executeAPI(
        this.utilitiesService.queryBuilder(
          queryFields,
          queryCondition,
          'BusinessUnit__c',
        ),
        'GET',
        null,
        isTotalRequired,
      );
    } catch (error) {
      console.log(error);
    }
    return BusinessunitDetails;
  }
}
