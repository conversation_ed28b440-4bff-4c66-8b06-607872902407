import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post,
  Req,
} from '@nestjs/common';
import { UtilitiesService } from '../utilities.service';
import * as salesForceHelper from 'apps/libs/utils/utils';
import { Request } from 'express';
import { GusSalesforceCaseService } from './case.salesforce.service';

@Controller('/salesforce/gus')
export class GusSalesforceCaseController {
  constructor(
    private readonly utilitiesService: UtilitiesService,
    private readonly gusSalesforceCaseService: GusSalesforceCaseService,
  ) {}

  @Post('/case')
  async createCaseRecord(
    @Body() data: any,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.gusSalesforceCaseService.createCaseRecord(data);
    } catch (error) {
      throw new HttpException(
        {
          statusCode: error.status || HttpStatus.INTERNAL_SERVER_ERROR,
          message: error || 'Internal Server Error',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch('/case/:caseId')
  async updateCaseRecord(
    @Param('caseId') caseId: string,
    @Body() data: any,
  ): Promise<any> {
    try {
      return await this.gusSalesforceCaseService.updateCaseRecord(caseId, data);
    } catch (error) {
      throw new HttpException(
        {
          statusCode: error.status || HttpStatus.INTERNAL_SERVER_ERROR,
          message: error || 'Internal Server Error',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/case/:caseId')
  async getCaseRecord(@Req() request: Request): Promise<any> {
    try {
      const caseId = request?.params?.caseId;

      if (!caseId || caseId === ':caseId') {
        throw new BadRequestException('Case Id is required to fetch a record.');
      }

      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          'Case',
          request['allowedEntities'],
          'caseDetailsCustom',
        );

      return await this.gusSalesforceCaseService.getCaseRecord(
        fieldsRequired,
        `WHERE Id='${caseId}'`,
      );
    } catch (error) {
      throw new HttpException(
        {
          statusCode: error.status || HttpStatus.INTERNAL_SERVER_ERROR,
          message: error || 'Internal Server Error',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
