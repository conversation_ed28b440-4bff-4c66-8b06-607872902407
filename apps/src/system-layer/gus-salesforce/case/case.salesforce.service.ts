import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
import { Inject, Injectable } from '@nestjs/common';

@Injectable()
export class GusSalesforceCaseService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private salesforceGUSService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}

  async createCaseRecord(data): Promise<any> {
    try {
      const response = await this.salesforceGUSService.executeAPI(
        `sobjects/case`,
        'POST',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      throw error;
    }
  }

  async updateCaseRecord(
    caseId: string,
    updateData: Record<string, any>,
  ): Promise<any> {
    try {
      if (!caseId) {
        throw new Error('Case Id is required to update a record.');
      }

      const response = await this.salesforceGUSService.executeAPI(
        `sobjects/case/${caseId}`,
        'PATCH',
        updateData,
      );

      return { data: response?.data };
    } catch (error) {
      throw error;
    }
  }

  async getCaseRecord(
    queryFields: string,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    try {
      const response = await this.salesforceGUSService.executeAPI(
        this.utilitiesService.queryBuilder(queryFields, queryCondition, 'Case'),
        'GET',
        null,
        isTotalRequired,
      );

      return response;
    } catch (error) {
      throw error;
    }
  }
}
