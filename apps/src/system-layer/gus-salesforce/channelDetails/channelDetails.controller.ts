import { Controller, Get, HttpException, HttpStatus, Param } from "@nestjs/common";
import { GusSalesforceChannelService } from "./channelDetails.service";

@Controller('/salesforce/gus')
export class GusSalesforceChannelController {
    constructor(
        private readonly gusSalesforceChannelService: GusSalesforceChannelService,
    ) { }
    @Get('/channel/getdetailsbyid/:channelid')
    async getChannelDetails(
        @Param('channelid') channelid: string
    ): Promise<any> {
        try {
            return this.gusSalesforceChannelService.getChannelDetails(channelid)
        } catch (error) {
            throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
