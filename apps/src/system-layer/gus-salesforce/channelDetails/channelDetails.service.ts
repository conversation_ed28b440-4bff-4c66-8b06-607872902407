import { Inject, Injectable } from "@nestjs/common";
import { SalesforceService } from "apps/libs/salesforce/salesforce.service";


@Injectable()
export class GusSalesforceChannelService {
    constructor(
        @Inject('GUS_SALESFORCE_SOURCE')
        private gusSalesforceService: SalesforceService
    ) { }
    async getChannelDetails(channelid, isTotalRequired = true): Promise<any> {
        const channelDetails = await this.gusSalesforceService.executeAPI(
            `connect/cms/delivery/channels/${channelid}/contents/query`,
            'GET',
            null,
            isTotalRequired,
            false
        );

        return channelDetails;
    }
}