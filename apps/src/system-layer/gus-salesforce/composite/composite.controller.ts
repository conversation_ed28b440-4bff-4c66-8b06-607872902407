import {
  Controller,
  HttpStatus,
  HttpException,
  Post,
  Body,
} from '@nestjs/common';
import { GusSalesforceCompositeService } from './composite.service';
@Controller('/salesforce/gus')
export class GusSalesforceCompositeController {
  constructor(
    private readonly gusSalesforceCompositeService: GusSalesforceCompositeService,
  ) {}
  @Post('/composite')
  async compositeRequest(@Body() compositeRequest: any): Promise<any> {
    try {
      return await this.gusSalesforceCompositeService.compositeRequest(
        compositeRequest,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
