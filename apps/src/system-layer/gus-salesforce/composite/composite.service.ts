import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class GusSalesforceCompositeService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
  ) {}
  async compositeRequest(request): Promise<any> {
    return await this.gusSalesforceService.compositeRequest(
      request?.compositeRequest ? request?.compositeRequest : request,
      request?.dependsOnPriorCall ? request?.dependsOnPriorCall : false,
    );
  }
}
