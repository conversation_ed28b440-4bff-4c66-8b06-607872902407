import { Controller, Get, Param, Query, Req } from '@nestjs/common';
import { Request } from 'express';
import { GusSalesforceContactService } from './contact.service';
import * as salesForceHelper from '../../../../libs/utils/utils';

@Controller('salesforce/gus')
export class GusSalesforceContactController {
  constructor(
    private readonly gusSalesforceContactService: GusSalesforceContactService,
  ) {}

  @Get('/contactbyemail/:emailId')
  async getContactByEmail(
    @Param('emailId') emailId: string,
    @Req() request: Request,
    @Query() query: any,
  ) {
    const entityName = 'Case';
    const entityOperation = 'contactByEmail';
    let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      entityOperation,
    );
    let fileCondition = `where Email = '${emailId}'`;
    if (query.AccountId) {
      whereClause = whereClause ?? '';
      whereClause += `AccountId = '${query.AccountId}'`;
    }
    if (whereClause) {
      fileCondition += `AND ${whereClause}`;
    }
    const response = await this.gusSalesforceContactService.getContactByEmailId(
      fieldsRequired,
      fileCondition,
    );

    return response;
  }
}
