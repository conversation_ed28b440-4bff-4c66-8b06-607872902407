import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceContactService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private utilitiesService: UtilitiesService
  ) { }
  async createContact(data): Promise<any> {
    try {
      const response = await this.gusSalesforceService.executeAPI(
        `sobjects/Contact`,
        'POST',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      throw error;
    }
  }
  async updateContact(id, data): Promise<any> {
    const response = await this.gusSalesforceService.executeAPI(
      `sobjects/Contact/${id}`,
      'PATCH',
      data,
    );
    return {
      statusCode: response.status,
    };
  }
  async getContactByEmailId(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const contactDetails = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Contact',
      ),
      'GET',
      null,
      isTotalRequired,
    );

    return contactDetails[0];
  }
}
