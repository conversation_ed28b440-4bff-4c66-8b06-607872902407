import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';

@Injectable()
export class GusSalesforceEducationHistoryService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getEducationHistory(
    queryFields,
    queryCondition = null,
    isTotalRequired = false,
  ): Promise<any> {
    const educationHistory = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'EducationHistoryRecord__c',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return educationHistory;
  }
}
