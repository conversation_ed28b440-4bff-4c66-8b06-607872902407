import { Body, Controller, Get, HttpException, HttpStatus, Param, Patch, Post, Req } from "@nestjs/common";
import { UtilitiesService } from "../utilities.service";
import { Request } from "express";
import { GusSalesforceEmailMessageService } from "./emailMessage.salesforce.service";
import * as salesForceHelper from '../../../../libs/utils/utils';

@Controller('/salesforce/gus')
export class GusSalesforceEmailMessageController {
    constructor(
        private readonly utilitiesService: UtilitiesService,
        private readonly gusSalesforceEmailMessageService: GusSalesforceEmailMessageService
    ) { }

    @Post('/emailmessage')
    async createEmailMessage(
        @Body() data: any,
        @Req() request: Request,
    ): Promise<any> {
        return await this.gusSalesforceEmailMessageService.createEmailMessage(
            data
        );
    }

    @Patch('/emailmessage/:id')
    async updateEmailMessage(
        @Body() details: any,
        @Param('id') id: string,
    ): Promise<any> {
        try {
            return await this.gusSalesforceEmailMessageService.updateEmailMessage(
                details,
                id,
            );
        } catch (error) {
            throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Get('/emailmessage/:messageidentifier')
    async getEmailMessage(
        @Param('messageidentifier') messageidentifier: string,
        @Req() request: Request,
    ): Promise<any> {
        try {
            const { whereClause, fieldsRequired } =
                await salesForceHelper.queryBuilder(
                    'EmailMessage',
                    request['allowedEntities'],
                    'EmailMessageByMessageIdentifier'
                );

            return await this.gusSalesforceEmailMessageService.getEmailMessage(
                messageidentifier,
                fieldsRequired,
                whereClause,
            );
        } catch (error) {
            throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}