import { SalesforceService } from "apps/libs/salesforce/salesforce.service";
import { UtilitiesService } from "../utilities.service";
import { Inject, Injectable } from "@nestjs/common";

@Injectable()
export class GusSalesforceEmailMessageService {
    constructor(
        @Inject('GUS_SALESFORCE_SOURCE')
        private salesforceGUSService: SalesforceService,
        private readonly utilitiesService: UtilitiesService,
    ) { }

    async createEmailMessage(data): Promise<any> {
        try {
            const response = await this.salesforceGUSService.executeAPI(
                `sobjects/EmailMessage`,
                'POST',
                data,
            );
            return { data: response?.data };
        } catch (error) {
            console.log(error)
            throw error;
        }
    }

    async updateEmailMessage(data, id): Promise<any> {
        const response = await this.salesforceGUSService.executeAPI(
            `sobjects/EmailMessage/${id}`,
            'PATCH',
            data,
        );
        return {
            statusCode: response.status,
        };
    }

    async getEmailMessage(
        messageidentifier,
        requiredFields,
        queryCondition = null,
    ): Promise<any> {
        let query = `Select ${requiredFields} from EmailMessage where MessageIdentifier = '${messageidentifier}'`;
        if (queryCondition != null) {
            query = `${query} and ${queryCondition}`;
        }
        const emailMessageDetails = await this.salesforceGUSService.executeAPI(
            query,
            'GET',
        );
        return emailMessageDetails[0];
    }
}