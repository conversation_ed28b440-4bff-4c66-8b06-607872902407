import { Module } from '@nestjs/common';
import { GusSalesforcePicklistValueInfoController } from './picklistValueInfo/picklistValueInfo.controller';
import { GusSalesforcePicklistValueInfoService } from './picklistValueInfo/picklistValueInfo.service';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from './utilities.service';
import { GusSalesforceAccountController } from './account/account.controller';
import { GusSalesforceAccountService } from './account/account.service';
import { GusSalesforceBusinessunitController } from './businessunit/businessunit.controller';
import { GusSalesforceBusinessunitService } from './businessunit/businessunit.service';
import { GusSalesforceLevelController } from './level/level.controller';
import { GusSalesforceLevelService } from './level/level.service';
import { GusSalesforceOpportunityDocumentTypeController } from './opportunityDocumentType/opportunityDocumentType.controller';
import { GusSalesforceOpportunityDocumentTypeService } from './opportunityDocumentType/opportunityDocumentType.service';
import { GusSalesforceOpportunityFileController } from './opportunityFiles/opportunityFiles.controller';
import { GusSalesforceOpportunityFileService } from './opportunityFiles/opportunityFiles.service';
import { GusSalesforcePricebookEntryController } from './pricebookEntry/pricebookEntry.controller';
import { GusSalesforcePricebookEntryService } from './pricebookEntry/pricebookEntry.service';
import { GusSalesforceProduct2Controller } from './product2/product2.controller';
import { GusSalesforceProductService } from './product2/product2.service';
import { GusSalesforceProgrammeService } from './programme/programme.service';
import { SalesforceModule } from 'apps/libs/salesforce/salesforce.module';
import { GusSalesForceOpportunityController } from './opportunity/opportunity.salesforce.controller';
import { GusSalesforceOpportunityService } from './opportunity/opportunity.salesforce.service';
import { GusSalesForceTaskController } from './task/task.salesforce.controller';
import { GusSalesforceTaskService } from './task/task.salesforce.service';
import { GusSalesforceProgrammeController } from './programme/programme.controller';
import { GusSalesforceVerticalController } from './vertical/vertical.controller';
import { GusSalesforceVerticalService } from './vertical/vertical.service';
import { GusSalesforceSchoolController } from './school/school.controller';
import { GusSalesforceSchoolService } from './school/school.service';
import { GusSalesforceCompositeService } from './composite/composite.service';
import { GusSalesforceCompositeController } from './composite/composite.controller';
import { GusSalesforceLeadController } from './lead/lead.controller';
import { GusSalesforceLeadService } from './lead/lead.service';
import { GusSalesforceApplicationService } from './application/application.service';
import { GusSalesforceContactService } from './contact/contact.service';
import { GusSalesforceOpportunityLineItemService } from './opportunityLineItem/opportunityLineItem.salesforce.service';
import { GusSalesforceApplicationFileController } from './applicationFile/applicationFile.controller';
import { GusSalesforceApplicationFile } from './applicationFile/applicationFile.service';
import { GusSalesforceIndividual } from './individual/individual.service';
import { ConfigModule } from '@nestjs/config';
import { LoggerModule } from '@gus-eip/loggers';
import { GusSalesforceEducationHistoryService } from './educationHistoryRecord/educationHistoryRecord.service';
import { GusSalesforceContactController } from './contact/contact.controller';
import { GusSalesforceCaseController } from './case/case.salesforce.controller';
import { GusSalesforceCaseService } from './case/case.salesforce.service';
import { GusSalesforceUserController } from './user/user.controller';
import { GusSalesforceUserService } from './user/user.service';
import { GusSalesforcePathwayProviderController } from './pathwayprovider/pathwayprovider.controller';
import { GusSalesforcePathwayProviderService } from './pathwayprovider/pathwayprovider.service';
import { GusSalesforceVerificationScreeningService } from './verificationScreening/verificationScreening.service';
import { GusSalesforceVerificationScreeningController } from './verificationScreening/verificationScreening.controller';
import { GusSalesforceChannelService } from './channelDetails/channelDetails.service';
import { GusSalesforceChannelController } from './channelDetails/channelDetails.controller';
import { GusSalesforceSurveyService } from './survey/survey.service';
import { GusSalesforceInstitutionService } from './institution/institution.services';
import { GusSalesforceInstitutionController } from './institution/institution.controller';
import { DynamoDBModule } from 'apps/src/common/dynamoDB/dynamoDB.module';
import { GusSalesforceEmailMessageController } from './emailMessage/emailMessage.salesforce.controller';
import { GusSalesforceEmailMessageService } from './emailMessage/emailMessage.salesforce.service';
import { GusSalesforceAccountAgeContractService } from './accountAgeContract/accountagecontract.service';
import { GusSalesforceIdentityInfoRecordService } from './identityInfoRecord/identityInfoRecord.service';
import { GusSalesforceVisaApplicationService } from './visaApplication/visaApplication.salesforce.service';
import { GusSalesforceVisaApplicationController } from './visaApplication/visaApplication.salesforce.controller';
import { GusSalesforceSobjectService } from './sobject/sobject.service';
import { GusSalesforcePlatformEventController } from './platformEvent/platformevent.controller';
import { GusSalesforcePlatformEventService } from './platformEvent/platformevent.service';
@Module({
  providers: [
    GusSalesforceIndividual,
    GusSalesforceApplicationFile,
    GusSalesforceApplicationFileController,
    GusSalesforceProgrammeController,
    GusSalesforceVerticalService,
    GusSalesForceOpportunityController,
    GusSalesforceOpportunityService,
    GusSalesForceTaskController,
    GusSalesforceTaskService,
    GusSalesforcePicklistValueInfoController,
    GusSalesforceAccountController,
    GusSalesforcePicklistValueInfoService,
    UtilitiesService,
    GusSalesforceBusinessunitController,
    GusSalesforceLevelController,
    GusSalesforceOpportunityDocumentTypeController,
    GusSalesforceOpportunityFileController,
    GusSalesforcePricebookEntryController,
    GusSalesforceProduct2Controller,
    GusSalesforceAccountService,
    GusSalesforceBusinessunitService,
    GusSalesforceLevelService,
    GusSalesforceOpportunityDocumentTypeService,
    GusSalesforceOpportunityFileService,
    GusSalesforcePricebookEntryService,
    GusSalesforceProductService,
    GusSalesforceProgrammeService,
    GusSalesforceVerticalController,
    GusSalesforceSchoolController,
    GusSalesforceSchoolService,
    GusSalesforceCompositeService,
    GusSalesforceCompositeController,
    GusSalesforceLeadController,
    GusSalesforceLeadService,
    GusSalesforceApplicationService,
    GusSalesforceContactController,
    GusSalesforceContactService,
    GusSalesforceOpportunityLineItemService,
    GusSalesforceEducationHistoryService,
    GusSalesforceIdentityInfoRecordService,
    GusSalesforceCaseController,
    GusSalesforceCaseService,
    GusSalesforceUserController,
    GusSalesforceUserService,
    GusSalesforcePathwayProviderController,
    GusSalesforcePathwayProviderService,
    GusSalesforceVerificationScreeningService,
    GusSalesforceChannelController,
    GusSalesforceChannelService,
    GusSalesforceSurveyService,
    GusSalesforceInstitutionService,
    GusSalesforceEmailMessageController,
    GusSalesforceEmailMessageService,
    GusSalesforceAccountAgeContractService,
    GusSalesforceVisaApplicationService,
    GusSalesforceSobjectService,
    GusSalesforceVisaApplicationController,
    GusSalesforcePlatformEventController,
    GusSalesforcePlatformEventService,
    {
      provide: 'GUS_SALESFORCE_SOURCE',
      useFactory: async () => {
        return new SalesforceService(
          process.env.GUS_CONSUMER_KEY,
          process.env.GUS_CONSUMER_SECRET,
          process.env.GUS_AUTH_URL,
          process.env.ACCESS_TOKEN_SECRET,
          process.env.GUS_GRANT_TYPE,
          process.env.GUS_USER_NAME,
          process.env.GUS_PASSWORD,
        );
      },
      inject: [],
    },
  ],
  controllers: [
    GusSalesforceApplicationFileController,
    GusSalesforceVerticalController,
    GusSalesforceProgrammeController,
    GusSalesForceOpportunityController,
    GusSalesForceTaskController,
    GusSalesforcePicklistValueInfoController,
    GusSalesforceAccountController,
    GusSalesforceBusinessunitController,
    GusSalesforceLevelController,
    GusSalesforceLeadController,
    GusSalesforceOpportunityDocumentTypeController,
    GusSalesforceOpportunityFileController,
    GusSalesforcePricebookEntryController,
    GusSalesforceProduct2Controller,
    GusSalesforceSchoolController,
    GusSalesforceCompositeController,
    GusSalesforceContactController,
    GusSalesforceCaseController,
    GusSalesforceUserController,
    GusSalesforcePathwayProviderController,
    GusSalesforceVerificationScreeningController,
    GusSalesforceChannelController,
    GusSalesforceInstitutionController,
    GusSalesforceEmailMessageController,
    GusSalesforceVisaApplicationController,
    GusSalesforcePlatformEventController,
  ],
  exports: [
    GusSalesforceIndividual,
    GusSalesforceApplicationFile,
    GusSalesforceApplicationFileController,
    GusSalesforceAccountService,
    GusSalesforceLevelService,
    GusSalesforceVerticalController,
    GusSalesforceVerticalService,
    GusSalesforceProgrammeController,
    GusSalesforceProgrammeService,
    GusSalesForceOpportunityController,
    GusSalesforceOpportunityService,
    GusSalesForceTaskController,
    GusSalesforceTaskService,
    GusSalesforcePicklistValueInfoController,
    GusSalesforceAccountController,
    GusSalesforceBusinessunitController,
    GusSalesforceLevelController,
    GusSalesforceOpportunityDocumentTypeController,
    GusSalesforceOpportunityFileController,
    GusSalesforcePricebookEntryController,
    GusSalesforceProduct2Controller,
    GusSalesforceOpportunityDocumentTypeService,
    GusSalesforceProgrammeService,
    GusSalesforceOpportunityFileService,
    GusSalesforcePricebookEntryService,
    GusSalesforceProductService,
    GusSalesforceBusinessunitService,
    GusSalesforceSchoolController,
    GusSalesforceSchoolService,
    GusSalesforceCompositeService,
    GusSalesforceCompositeController,
    GusSalesforceLeadService,
    GusSalesforceLeadController,
    GusSalesforceApplicationService,
    GusSalesforceContactService,
    UtilitiesService,
    GusSalesforceOpportunityLineItemService,
    GusSalesforceIdentityInfoRecordService,
    GusSalesforceEducationHistoryService,
    GusSalesforceContactController,
    GusSalesforceCaseController,
    GusSalesforceUserController,
    GusSalesforcePathwayProviderController,
    GusSalesforcePathwayProviderService,
    GusSalesforceChannelController,
    GusSalesforceChannelService,
    GusSalesforceVerificationScreeningService,
    GusSalesforceSurveyService,
    GusSalesforceEmailMessageController,
    GusSalesforceEmailMessageService,
    GusSalesforceAccountAgeContractService,
    GusSalesforceIdentityInfoRecordService,
    GusSalesforceOpportunityFileService,
    GusSalesforceSobjectService,
    GusSalesforceUserService,
    GusSalesforcePlatformEventController,
    GusSalesforcePlatformEventService,
  ],
  imports: [
    SalesforceModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    LoggerModule.forRoot({
      region: process.env.REGION,
      logGroupName: process.env.LOGGER_LOG_GROUP_NAME,
      teamWebhookUrl: process.env.TEAMS_WEBHOOK_URL,
      isAlertNeeded: false,
      options: 'CloudWatchLogger',
    }),
    DynamoDBModule,
  ],
})
export class GusSalesforceSystemModule {}
