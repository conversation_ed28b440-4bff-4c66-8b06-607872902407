import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';

@Injectable()
export class GusSalesforceIdentityInfoRecordService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private utilitiesService: UtilitiesService,
  ) {}
  async createIdentityInfoRecord(data) {
    let updatedData = {};
    if (data['IdentityInfoRecord__c']) {
      updatedData = {
        ...data['IdentityInfoRecord__c'],
        Opportunity__c: data['opportunityId'],
        Account__c: data['accountId'],
      };
    }
    try {
      if (
        !this.utilitiesService.checkRequiredField(
          updatedData,
          'IdentityInfoRecord__c',
        )
      ) {
        return;
      }
      const response = await this.gusSalesforceService.executeAPI(
        `sobjects/IdentityInfoRecord__c`,
        'POST',
        updatedData,
      );
      return { data: response?.data };
    } catch (error) {
      throw error;
    }
  }

  async updateIdentityInfoRecord(id, data) {
    let updatedData = {};
    if (data['IdentityInfoRecord__c']) {
      updatedData = {
        ...data['IdentityInfoRecord__c'],
        Opportunity__c: data['opportunityId'],
        Account__c: data['accountId'],
      };
    }

    try {
      if (
        !this.utilitiesService.checkRequiredField(
          updatedData,
          'IdentityInfoRecord__c',
        )
      ) {
      }

      const response = await this.gusSalesforceService.executeAPI(
        `sobjects/IdentityInfoRecord__c/${id}`,
        'PATCH',
        updatedData,
      );

      return { data: response?.data };
    } catch (error) {
      throw error;
    }
  }

  async getIdentityInfoRecord(applicationDetails, requiredFields) {
    let query = `Select ${requiredFields} from IdentityInfoRecord__c where Opportunity__c = '${applicationDetails.opportunityId}' and Account__c = '${applicationDetails.accountId}'`;

    const identityInfoRecords = await this.gusSalesforceService.executeAPI(
      query,
      'GET',
    );
    return identityInfoRecords;
  }

  async updateIdentityInfoRecordData(
    identityInfoRecordId: string | Number,
    identityInfoRecordDetails: Record<string, any>,
  ): Promise<any> {
    try {
      if (identityInfoRecordId) {
        const response = await this.gusSalesforceService.executeAPI(
          `sobjects/IdentityInfoRecord__c/${identityInfoRecordId}`,
          'PATCH',
          identityInfoRecordDetails,
        );
        return response.data;
      } else {
        throw new NotFoundException(`identityInfoRecordId Required`);
      }
    } catch (error) {
      throw error;
    }
  }

  async getIdentityInfoRecordData(
    queryCondition: string,
    fields: string[],
  ): Promise<any> {
    try {
      let query = `SELECT ${fields.join(', ')} FROM IdentityInfoRecord__c`;
      query = query + ' ' + (queryCondition ?? '');
      const result = await this.gusSalesforceService.executeAPI(query, 'GET');
      return result[0];
    } catch (error) {
      throw error;
    }
  }
}
