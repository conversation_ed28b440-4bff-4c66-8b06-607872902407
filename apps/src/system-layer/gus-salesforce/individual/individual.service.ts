import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from 'apps/src/system-layer/gus-salesforce/utilities.service';
@Injectable()
export class GusSalesforceIndividual {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private utilitiesService: UtilitiesService,
  ) {}
  async get(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    if (queryCondition === '') {
      queryCondition = 'order by name asc';
    }
    const individual = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Individual',
      ),
      'GET',
      isTotalRequired,
    );
    return individual;
  }
  async createIndividual(data): Promise<any> {
    try {
      const response = await this.gusSalesforceService.executeAPI(
        `sobjects/Individual`,
        'POST',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      throw error;
    }
  }
  async updateIndividual(id, data): Promise<any> {
    const response = await this.gusSalesforceService.executeAPI(
      `sobjects/Individual/${id}`,
      'PATCH',
      data,
    );
    return {
      statusCode: response.status,
    };
  }
}
