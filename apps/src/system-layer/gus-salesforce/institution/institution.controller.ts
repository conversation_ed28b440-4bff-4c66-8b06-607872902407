import { Controller, HttpStatus, Get, Req } from '@nestjs/common';
import { GusSalesforceInstitutionService } from './institution.services';
import { Request } from 'express';
import { CustomException } from 'apps/src/common/customException/customexception.service';
@Controller('/salesforce/gus')
export class GusSalesforceInstitutionController {
  constructor(
    private readonly gusSalesforceInstitutionService: GusSalesforceInstitutionService,
  ) {}
  @Get('/institutions')
  async getInstitutions(@Req() request: Request): Promise<any> {
    try {
      const country = request.query.country as string;
      const searchText = (request.query?.searchText as string) ?? '';
      const start = request.query.start
        ? JSON.parse(request.query.start as string)
        : null;
      const limit = parseInt(request.query.limit as string, 10) || 100; // Default limit to 100 if not provided

      if (!country) {
        throw new CustomException(
          HttpStatus.BAD_REQUEST,
          'MISSING_COUNTRY',
          'The "country" query parameter is required.',
          request.url,
          'GET_INSTITUTIONS',
        );
      }

      const response =
        await this.gusSalesforceInstitutionService.getInstitutions(
          country,
          searchText,
          start,
          limit,
        );

      return response;
    } catch (error) {
      if (error instanceof CustomException) {
        // Re-throw the custom exception if it's an instance of CustomException
        throw error;
      } else {
        // If the error format is different, create a generic internal server error
        throw new CustomException(
          HttpStatus.INTERNAL_SERVER_ERROR,
          'INTERNAL_SERVER_ERROR',
          error.message ||
            'An unexpected error occurred while fetching institutions.',
          request.url,
          'GET_INSTITUTIONS',
        );
      }
    }
  }
}
