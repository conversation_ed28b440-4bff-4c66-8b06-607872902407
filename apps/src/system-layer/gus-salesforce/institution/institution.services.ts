import { HttpStatus, Injectable } from '@nestjs/common';
import { CustomException } from 'apps/src/common/customException/customexception.service';
import { DynamoDBService } from 'apps/src/common/dynamoDB/dynamoDB.service';

@Injectable()
export class GusSalesforceInstitutionService {
  constructor(private dynamodbService: DynamoDBService) {}

  /**
   * Retrieves a list of institutions for a given country from the DynamoDB table specified by GUS_MIDDLEWARE_SF_INSTITUTION_CACHE_TABLE.
   * @param country The two-letter ISO country code to filter by.
   * @param start The starting key for pagination, if any.
   * @param limit The maximum number of results to return, defaulting to 100.
   * @returns An object with two properties: institutionNames, an array of institution names, and lastEvaluatedKey, the starting key for the next page of results.
   * @throws Error if the table name is not found in environment variables, or if there is an error communicating with DynamoDB.
   */
  async getInstitutions(
    country: string,
    searchText?: string,
    startKey?: AWS.DynamoDB.DocumentClient.Key,
    limit = 100,
  ): Promise<{
    institutionNames: string[];
    lastEvaluatedKey: AWS.DynamoDB.DocumentClient.Key | undefined;
  }> {
    const tableName = process.env.GUS_MIDDLEWARE_SF_INSTITUTION_CACHE_TABLE;
    const secondaryIndexName = 'InstitutionNameLowerIndex';

    if (!tableName) {
      throw new CustomException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        'TABLE_NOT_FOUND',
        'The GUS_MIDDLEWARE_SF_INSTITUTION_CACHE_TABLE environment variable is not set.',
        'getInstitutions',
        'GET_INSTITUTIONS',
      );
    }

    // Base query parameters
    const params: AWS.DynamoDB.DocumentClient.QueryInput = {
      TableName: tableName,
      KeyConditionExpression: '#pk = :pk',
      ExpressionAttributeNames: {
        '#pk': 'PK',
      },
      ExpressionAttributeValues: {
        ':pk': `COUNTRY#${country}`,
      },
      ProjectionExpression: 'Institution_Name__c',
      Limit: limit,
      ExclusiveStartKey: startKey,
    };

    // Use the secondary index if searchText is provided and not empty
    if (searchText) {
      params.IndexName = secondaryIndexName;
      params.KeyConditionExpression +=
        ' AND begins_with(#institutionLower, :searchText)';
      params.ExpressionAttributeNames['#institutionLower'] =
        'Institution_Name_Lower__c';
      params.ExpressionAttributeValues[':searchText'] =
        searchText.toLowerCase();
    }

    try {
      const result = await this.dynamodbService.queryObjects(params);
      const institutionNames =
        result.Items?.map((item) => item.Institution_Name__c) || [];
      return {
        institutionNames,
        lastEvaluatedKey: result.LastEvaluatedKey,
      };
    } catch (error) {
      console.error('Error fetching institutions:', error);
      throw new CustomException(
        HttpStatus.INTERNAL_SERVER_ERROR,
        'QUERY_FAILED',
        'Failed to fetch institutions from DynamoDB.',
        'getInstitutions',
        'GET_INSTITUTIONS',
      );
    }
  }
}
