// lead.controller.ts
import { Controller, Get, Post, Param, Body, Req } from '@nestjs/common';
import { GusSalesforceLeadService } from './lead.service';
import * as salesForceHelper from 'apps/libs/utils/utils';
import { Request } from 'express';
import { UtilitiesService } from 'apps/src/system-layer/gus-salesforce/utilities.service';
@Controller('salesforce/gus/lead')
export class GusSalesforceLeadController {
  constructor(
    private readonly leadService: GusSalesforceLeadService,
    private readonly utilitiesService: UtilitiesService,
  ) { }

  @Post()
  async createLead(@Body() data): Promise<any> {
    if (this.utilitiesService.checkRequiredField(data, 'Lead')) {
      const leadId = await this.leadService.createLead(data);
      return { Id: leadId, success: true };
    }
  }

  @Get(':email')
  async getLead(
    @Param('email') email: string,
    @Req() request: Request,
  ): Promise<any> {
    const entityName = 'Lead';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request?.['allowedEntities'],
    );

    let leadByEmailIdCondition = `WHERE Email ='${email}'`;

    if (whereClause) {
      leadByEmailIdCondition += ` AND ${whereClause}`;
    }
    try {
      const lead = await this.leadService.get(
        fieldsRequired,
        leadByEmailIdCondition,
      );

      return lead;
    } catch (error) {
      throw error;
    }
  }

  @Post(':id')
  async updateLead(@Param('id') id: string, @Body() data): Promise<any> {
    const response = await this.leadService.updateLead(id, data);
    return { response: response, success: true };
  }
}
