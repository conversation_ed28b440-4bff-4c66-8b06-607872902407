import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceLeadService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) { }
  async get(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    if (queryCondition === '') {
      queryCondition = 'order by name asc';
    }
    const leads = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(queryFields, queryCondition, 'Lead'),
      'GET',
      isTotalRequired,
    );
    return leads;
  }
  async createLead(data): Promise<any> {
    try {
      if (!this.utilitiesService.checkRequiredField(data, 'Lead')) {
        return;
      }
      const response = await this.gusSalesforceService.executeAPI(
        `sobjects/Lead`,
        'POST',
        data,
      );
      return response.data.id;
    } catch (error) {
      throw error;
    }
  }
  async updateLead(id, data): Promise<any> {
    const response = await this.gusSalesforceService.executeAPI(
      `sobjects/Lead/${id}`,
      'PATCH',
      data,
    );
    return {
      statusCode: response.status,
    };
  }
}
