import {
  Controller,
  Body,
  HttpStatus,
  HttpException,
  Get,
  Query,
  Req,
} from '@nestjs/common';
import { GusSalesforceLevelService } from './level.service';
import * as salesForceHelper from '../../../../libs/utils/utils';
import { Request } from 'express';
@Controller('/salesforce/gus')
export class GusSalesforceLevelController {
  constructor(
    private readonly gusSalesforceLevelService: GusSalesforceLevelService,
  ) {}
  @Get('/level')
  async getLevels(
    @Query('condition') condition: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const entityName = 'Level__c';
      let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
        entityName,
        request['allowedEntities'],
      );
      if (whereClause) {
        whereClause = `where ${whereClause}`;
      }
      return await this.gusSalesforceLevelService.getLevels(
        fieldsRequired,
        whereClause,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
