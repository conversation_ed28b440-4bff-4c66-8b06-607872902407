import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceLevelService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getLevels(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    if (queryCondition === '') {
      queryCondition = 'order by name asc';
    }
    const levels = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Level__c',
      ),
      'GET',
      isTotalRequired,
    );
    return levels;
  }
}
