export const ObjectConfig = {
  Opportunity: {
    requiredFields: [
      'CloseDate',
      'StageName',
      'ApplicationFormId__c',
      'BusinessUnit__c',
      'Name',
    ],
    SalesAttributeMappings: {
      OwnerId: 'BusinessDeveloper__c',
      BusinessDeveloper__c: 'BusinessDeveloper__c',
      AgentAccount__c: 'AgentAccount__c',
    },
  },
  Account: {
    requiredFields: ['PersonEmail', 'RecordTypeId', 'LastName'],
    SalesAttributeMappings: {
      Lead_Agent__c: 'AgentAccount__c',
      Lead_Creation_Date__c: 'Created_Date_and_Time__c',
    },
  },
  Individual: {
    requiredFields: ['Email__c', 'Brand__c'],
  },
  Lead: {
    requiredFields: [
      'Email',
      'BusinessUnit__c',
      'Brand__c',
      'LastName',
      'Country',
      'Programme__c',
    ],
    utmParamsRequiredFields: [
      'pi__utm_source__c',
      'pi__utm_medium__c',
      'pi__utm_campaign__c',
      'pi__utm_content__c',
      'pi__utm_term__c',
    ],
    termsAndCondition: [
      'HasOptedOutOfMarketingEmailSync__c',
      'HasOptedOutOfGUSMarketingEmail__c',
    ],
  },
  Application__c: {
    requiredFields: ['First_Name__c'],
    SalesAttributeMappings: {
      OwnerId: 'BusinessDeveloper__c',
      Business_Developer__c: 'BusinessDeveloper__c',
      Agent_Account__c: 'AgentAccount__c',
    },
  },
  OpportunityFile__c: {
    requiredFields: [
      'DocumentType__c',
      'Name',
      'ApplicationId__c',
      'S3FileName__c',
      'OriginalValue__c',
    ],
  },
  OpportunityLineItem: {
    requiredFields: ['OpportunityId', 'Product2Id'],
  },
  OpportunityTeamMember: {
    requiredFields: [
      'OpportunityId',
      'OpportunityAccessLevel',
      'TeamMemberRole',
      'UserId',
    ],
  },
  LanguageProficiencyRecord__c: {
    requiredFields: [
      'ProficiencyQualification__c',
      'Opportunity__c',
      'Account__c',
    ],
  },
  EducationHistoryRecord__c: {
    requiredFields: ['Opportunity__c', 'Account__c'],
  },
  Visa_Application__c: {
    requiredFields: ['Opportunity__c', 'Account__c'],
  },
  Connection__c: {
    requiredFields: ['Opportunity_c__c', 'Account__c'],
  },
  WorkHistoryRecord__c: {
    requiredFields: ['Employer__c', 'Opportunity__c', 'Account__c'],
  },

  IdentityInfoRecord__c: {
    requiredFields: [
      'Opportunity__c',
      'Account__c',
      'Identity_Document_Number__c',
    ],
  },

  Survey_AP__c: {
    requiredFields: ['Email_Address__c', 'Institution__c'],
  },

  Screening_Enquiry__c: {
    requiredFields: [
      'Screening_Identifiers__c',
      'Account__c',
      'Business_registration_number__c',
      'City__c',
      'Name_of_business__c',
      'Country_of_incorporation__c',
      'State_Province_of_Address__c',
      'State_Province_of_Incorporation__c',
      'ZIP_code__c',
      'VAT_Number__c',
      'RecordTypeId',
      'KYB_Enquiry_Type__c',
    ],
  },

  Screening_Enquiry_Feedback__c: {
    requiredFields: [
      'Screening_Enquiry__c',
      'Watchlist_Status__c',
      'Score__c',
      'Subject_Matched__c',
      'Entity_Name__c',
      'Source_List_Type__c',
      'Remarks__c',
      'Caution__c',
      'URL__c',
      'Source_Agency_Name__c',
    ],
  },
};
