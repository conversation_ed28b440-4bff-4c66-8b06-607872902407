import {
  Body,
  Controller,
  Get,
  Param,
  Req,
  Post,
  HttpException,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { GusSalesforceOpportunityService } from './opportunity.salesforce.service';
import * as salesForceHelper from 'apps/libs/utils/utils';
import { Request } from 'express';
import { UtilitiesService } from '../utilities.service';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
@Controller('/salesforce/gus')
export class GusSalesForceOpportunityController {
  constructor(
    private readonly GUSSalesforceApplicationService: GusSalesforceOpportunityService,
    private readonly utilitiesService: UtilitiesService,
    private readonly loggerEnum: LoggerEnum,
  ) { }
  @Get('/opportunities/:entityOperation')
  async getOpportunities(
    @Param('entityOperation') entityOperation: string,
    @Req() request: Request,
  ): Promise<any> {
    const entityName = 'Opportunity';
    let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      entityOperation,
    );
    if (whereClause) {
      whereClause = ` where ${whereClause}`;
    }
    return await this.GUSSalesforceApplicationService.getOpportunities(
      fieldsRequired,
      whereClause,
    );
  }

  @Get('/opportunityId/:applicationId')
  async getOpportunityId(
    @Param('applicationId') applicationId: string,
    @Req() request: Request,
    @Query('scenario') scenario?: string,
  ): Promise<any> {
    try {
      const fieldsRequired = 'Id';
      const whereClause = `where ApplicationFormId__c = '${applicationId}'`;
      console.log("scenario ->", scenario)
      await this.utilitiesService.recordGUSLogs(
        applicationId,
        request,
        this.loggerEnum.Event.GET_OPPORTUNITY_INITIATED,
        'get opportunity by applicationId',
        applicationId,
        'OPPORTUNITY',
        scenario,
        { fieldsRequired, whereClause },
        null,
      );
      const response =
        await this.GUSSalesforceApplicationService.getOpportunityId(
          fieldsRequired,
          whereClause,
        );
      await this.utilitiesService.recordGUSLogs(
        applicationId,
        request,
        this.loggerEnum.Event.GET_OPPORTUNITY_COMPLETED,
        'get opportunity by applicationId',
        applicationId,
        'OPPORTUNITY',
        scenario,
        { fieldsRequired, whereClause },
        response,
      );
      return response;
    } catch (error) {
      await this.utilitiesService.recordGUSErrorLogs(
        applicationId,
        request,
        this.loggerEnum.Event.GET_OPPORTUNITY_FAILED,
        'get opportunity by applicationId failed',
        error,
        'OPPORTUNITY',
        scenario,
        null,
        null,
      );
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('/updateOpportunity/:id')
  async updateOpportunityDetails(
    @Param('id') id: string,
    @Body() details: any,
    @Req() request: Request,
    @Query('scenario') scenario?: string,
  ): Promise<any> {
    try {
      await this.utilitiesService.recordGUSLogs(
        details,
        request,
        this.loggerEnum.Event.UPDATE_OPPORTUNITY_INITIATED,
        'update opportunity by id',
        id,
        'OPPORTUNITY',
        scenario,
        details,
        null,
      );
      const response =
        await this.GUSSalesforceApplicationService.updateOpportunity(
          id,
          details,
        );
      await this.utilitiesService.recordGUSLogs(
        details,
        request,
        this.loggerEnum.Event.UPDATE_OPPORTUNITY_COMPLETED,
        'update opportunity by id',
        id,
        'OPPORTUNITY',
        scenario,
        details,
        response,
      );
      return response;
    } catch (error) {
      await this.utilitiesService.recordGUSErrorLogs(
        details,
        request,
        this.loggerEnum.Event.UPDATE_OPPORTUNITY_FAILED,
        error,
        id,
        'OPPORTUNITY',
        scenario,
        details,
        null,
      );
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
