import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceOpportunityService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private salesforceGUSService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) { }
  async getOpportunities(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const opportunity = await this.salesforceGUSService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Opportunity',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return opportunity;
  }
  async createOpportuntiy(data): Promise<any> {
    try {
      if (!this.utilitiesService.checkRequiredField(data, 'Opportunity')) {
        return;
      }
      const response = await this.salesforceGUSService.executeAPI(
        `sobjects/opportunity`,
        'POST',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      throw error;
    }
  }
  async getOpportunityId(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const opportunity = await this.salesforceGUSService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Opportunity',
      ),
      'GET',
      null,
      isTotalRequired,
    );

    return opportunity[0];
  }
  async updateOpportunity(id, data): Promise<any> {
    console.log(id, data)
    const response = await this.salesforceGUSService.executeAPI(
      `sobjects/Opportunity/${id}`,
      'PATCH',
      data,
    );
    return {
      statusCode: response.status,
    };
  }
}
