import {
  Controller,
  Body,
  HttpStatus,
  HttpException,
  Get,
  Param,
} from '@nestjs/common';
import { GusSalesforceOpportunityDocumentTypeService } from './opportunityDocumentType.service';

@Controller('/salesforce/gus')
export class GusSalesforceOpportunityDocumentTypeController {
  constructor(
    private readonly gusSalesforceOpportunityDocumentTypeService: GusSalesforceOpportunityDocumentTypeService,
  ) {}
  @Get('/opportunitydocumenttype')
  async documentTypes(): Promise<any> {
    try {
      return await this.gusSalesforceOpportunityDocumentTypeService.getOpportunityDocumentTypes(
        'DocumentType__c',
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
