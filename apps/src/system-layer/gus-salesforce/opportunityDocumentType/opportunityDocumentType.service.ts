import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceOpportunityDocumentTypeService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getOpportunityDocumentTypes(
    queryFields,
    queryCondition = null,
    isTotalRequired = false,
  ): Promise<any> {
    const documentTypes = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'OpportunityDocumentType__c',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return documentTypes;
  }
}
