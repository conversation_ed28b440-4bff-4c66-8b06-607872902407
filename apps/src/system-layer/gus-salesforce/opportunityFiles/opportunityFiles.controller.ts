import {
  Controller,
  Post,
  HttpStatus,
  HttpException,
  Get,
  Body,
  Req,
  Param,
  Query,
} from '@nestjs/common';
import { GusSalesforceOpportunityFileService } from './opportunityFiles.service';
import { UtilitiesService } from '../utilities.service';
import { Request } from 'express';
import * as salesForceHelper from '../../../../libs/utils/utils';
@Controller('/salesforce/gus')
export class GusSalesforceOpportunityFileController {
  constructor(
    private readonly gusSalesforceOpportunityFileService: GusSalesforceOpportunityFileService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  @Get('/opportunityfile/:id')
  async getOpportunityFiles(
    @Param('id') id: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const entityName = 'OpportunityFile__c';
      const entityOperation = 'opportunityFilesById';
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          entityName,
          request['allowedEntities'],
          entityOperation,
        );
      let fileCondition = `WHERE Id = '${id}'`;
      if (whereClause) {
        fileCondition += `AND ${whereClause}`;
      }
      return await this.gusSalesforceOpportunityFileService.getOpportunityFiles(
        fieldsRequired,
        fileCondition,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/opportunityfilesByS3Filename')
  async getOpportunityFilesByS3Filename(
    @Req() request: Request,
    @Query('s3FileName') s3FileName: string,
    @Query('opportunityId') opportunityId?: string,
    @Query('documentType') documentType?: string,
  ): Promise<any> {
    try {
      if (!s3FileName) {
        throw new HttpException(
          's3FileName is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      const entityName = 'OpportunityFile__c';
      const entityOperation = 'opportunityFilesByS3FileName';
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          entityName,
          request['allowedEntities'],
          entityOperation,
        );
      const cleanedPath = s3FileName.replace(/\\/g, '');
      const pathSegments = cleanedPath.split('/');

      const encodedPath = pathSegments
        .map((segment) => encodeURIComponent(segment))
        .join('/');
      const rawPath = cleanedPath;

      let fileCondition = `WHERE S3FileName__c In ('${encodedPath}', '${rawPath}')`;

      if (opportunityId) {
        fileCondition += ` AND Opportunity__c = '${opportunityId}'`;
      }
      if (documentType) {
        fileCondition += ` AND DocumentType__c = '${documentType}'`;
      }
      if (whereClause) {
        fileCondition += ` AND ${whereClause}`;
      }

      return await this.gusSalesforceOpportunityFileService.getOpportunityFiles(
        fieldsRequired,
        fileCondition,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/opportunityfilebyapplicationid/:applicationId')
  async getOpportunityFilesByApplicationId(
    @Param('applicationId') applicationId: string,
  ): Promise<any> {
    try {
      const fieldsRequired =
        'Id, Additional_Info__c,DocumentType__c,Opportunity__c, Opportunity__r.LevelCode__c ';
      const whereClause = `where ApplicationId__c = '${applicationId}'`;
      return await this.gusSalesforceOpportunityFileService.getOpportunityFilesByApplicationId(
        fieldsRequired,
        whereClause,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('/updateopportunityfile/:id')
  async updateOpportunityFile(
    @Param('id') id: string,
    @Body() details: any,
  ): Promise<any> {
    try {
      return await this.gusSalesforceOpportunityFileService.updateOpportunityFileRecord(
        id,
        details,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('/opportunityfile')
  async uploadOpportunityFiles(
    @Body() fileData: any,
    @Req() request: Request,
  ): Promise<any> {
    try {
      // await this.utilitiesService.recordGUSLogs(
      //   fileData,
      //   request,
      //   'UPDATE_OPPORTUNITYFILE',
      //   'update opportunity files',
      //   null,
      //   'OPPORTUNITY',
      //   fileData,
      //   null,
      // );
      const response =
        await this.gusSalesforceOpportunityFileService.uploadOpportunityFiles(
          fileData,
        );
      // await this.utilitiesService.recordGUSLogs(
      //   fileData,
      //   request,
      //   'UPDATE_OPPORTUNITYFILE',
      //   'update opportunity files',
      //   null,
      //   'OPPORTUNITY',
      //   fileData,
      //   response,
      // );
      return response;
    } catch (error) {
      // await this.utilitiesService.recordGUSErrorLogs(
      //   fileData,
      //   request,
      //   'UPDATE_OPPORTUNITYFILE',
      //   error,
      //   null,
      //   'OPPORTUNITY',
      //   fileData,
      //   null,
      // );
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
