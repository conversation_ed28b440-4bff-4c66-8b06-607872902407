import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';

@Injectable()
export class GusSalesforceOpportunityFileService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getOpportunityFiles(
    queryFields,
    queryCondition = null,
    isTotalRequired = false,
  ): Promise<any> {
    const OpportunityFiles = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'OpportunityFile__c',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return OpportunityFiles;
  }
  async getOpportunityFilesByApplicationId(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const opportunityFiles = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'OpportunityFile__c',
      ),
      'GET',
      null,
      isTotalRequired,
    );

    return opportunityFiles;
  }
  uploadOpportunityFiles: any = async (fileData) => {
    const response = await this.gusSalesforceService.executeAPI(
      `sobjects/OpportunityFile__c`,
      'POST',
      fileData,
    );
    return response?.data;
  };

  async updateOpportunityFileRecord(
    opportunityFileId: string | Number,
    opportunityFileRecordDetails: any,
  ): Promise<any> {
    try {
      if (opportunityFileId) {
        const response = await this.gusSalesforceService.executeAPI(
          `sobjects/OpportunityFile__c/${opportunityFileId}`,
          'PATCH',
          opportunityFileRecordDetails,
        );
        return response.data;
      } else {
        throw new NotFoundException(`opportunityFileId Required`);
      }
    } catch (error) {
      throw error;
    }
  }
}
