import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceOpportunityLineItemService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private salesforceGUSService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async createOpportuntiyLineItem(data): Promise<any> {
    try {
      if (
        !this.utilitiesService.checkRequiredField(data, 'OpportunityLineItem')
      ) {
        return;
      }
      const response = await this.salesforceGUSService.executeAPI(
        `sobjects/OpportunityLineItem`,
        'POST',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      throw error;
    }
  }

  async getOpportunityLineItems(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const opportunityLineItems = await this.salesforceGUSService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'OpportunityLineItem',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return opportunityLineItems;
  }
}
