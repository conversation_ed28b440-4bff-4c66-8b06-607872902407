import {
  Controller,
  Body,
  HttpStatus,
  HttpException,
  Get,
  Query,
  Req,
} from '@nestjs/common';
import { GusSalesforcePathwayProviderService } from './pathwayprovider.service';
import * as salesForceHelper from '../../../../libs/utils/utils';
import { Request } from 'express';
@Controller('/salesforce/gus')
export class GusSalesforcePathwayProviderController {
  constructor(
    private readonly gusSalesforcePathwayProviderService: GusSalesforcePathwayProviderService,
  ) {}
  @Get('/pathwayprovider')
  async getPathwayproviders(
    @Query('condition') condition: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const entityName = 'PathwayProvider__c';
      let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
        entityName,
        request['allowedEntities'],
      );
      if (whereClause) {
        whereClause = `where ${whereClause}`;
      }
      return await this.gusSalesforcePathwayProviderService.getPathwayProviders(
        fieldsRequired,
        whereClause,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
