import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforcePathwayProviderService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getPathwayProviders(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    if (queryCondition === '') {
      queryCondition = 'order by name asc';
    }
    const pathwayProviders = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'PathwayProvider__c',
      ),
      'GET',
      isTotalRequired,
    );
    return pathwayProviders;
  }
}
