import { Controller, Body, Post, Patch, Get, Param } from '@nestjs/common';
import { GusSalesforcePicklistValueInfoService } from './picklistValueInfo.service';
import { PicklistValueInfo } from 'apps/libs/salesforce/default.fields';

@Controller('/salesforce/gus')
export class GusSalesforcePicklistValueInfoController {
  constructor(
    private readonly gusSalesforcePicklistValueInfoService: GusSalesforcePicklistValueInfoService,
  ) {}
  @Get('/country')
  async getCountryCode(): Promise<any> {
    return await this.gusSalesforcePicklistValueInfoService.getCountries(
      PicklistValueInfo,
    );
  }
  @Get('/languages')
  async getLanguages(): Promise<any> {
    return await this.gusSalesforcePicklistValueInfoService.getLanguages(
      PicklistValueInfo,
    );
  }
  @Get('/states/:countryCode')
  async getStatesByCountryCode(
    @Param('countryCode') countryCode: string,
  ): Promise<any> {
    return await this.gusSalesforcePicklistValueInfoService.getStatesByCountryCode(
      countryCode,
    );
  }
}
