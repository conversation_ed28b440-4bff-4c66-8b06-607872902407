import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforcePicklistValueInfoService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getCountries(
    queryFields,
    queryCondition = null,
    isTotalRequired = false,
  ) {
    let countryDetails;
    try {
      if (!queryCondition) {
        queryCondition = `where EntityParticle.DurableId = 'Contact.MailingAddress.MailingCountryCode' order by Label asc`;
      }
      countryDetails = await this.gusSalesforceService.executeAPI(
        this.utilitiesService.queryBuilder(
          queryFields,
          queryCondition,
          'PicklistValueInfo',
        ),
        'GET',
        null,
        isTotalRequired,
      );
    } catch (error) {
      console.log(error);
      throw error;
    }
    return countryDetails;
  }
  async getStatesByCountryCode(countryCode: string) {
    const states = await this.gusSalesforceService.executeAPI(
      'ui-api/object-info/Lead/picklist-values/012Tf0000012h9xIAA/StateCode',
      'GET',
      null,
      true,
      false,
    );

    if (countryCode in states.controllerValues) {
      return states.values.filter((item) =>
        item.validFor.includes(states.controllerValues[countryCode]),
      );
    } else {
      return [];
    }
  }

  async getLanguages(
    queryFields,
    queryCondition = null,
    isTotalRequired = false,
  ) {
    let languageDetails;
    try {
      if (!queryCondition) {
        queryCondition = `where EntityParticle.DurableId = 'Lead.00N0X00000DO6CM'`;
      }
      languageDetails = await this.gusSalesforceService.executeAPI(
        this.utilitiesService.queryBuilder(
          queryFields,
          queryCondition,
          'PicklistValueInfo',
        ),
        'GET',
        null,
        isTotalRequired,
      );
    } catch (error) {
      console.log(error);
      throw error;
    }
    return languageDetails;
  }
}
