import {
  Controller,
  HttpStatus,
  HttpException,
  Post,
  Body,
  Param,
} from '@nestjs/common';
import { GusSalesforcePlatformEventService } from './platformevent.service';
@Controller('/salesforce/gus')
export class GusSalesforcePlatformEventController {
  constructor(
    private readonly gusSalesforcePlatformEventService: GusSalesforcePlatformEventService,
  ) {}
  @Post('/event/:eventName')
  async platformEventRequest(
    @Body() platformEventRequest: any,
    @Param('eventName') eventName: string,
  ): Promise<any> {
    try {
      return await this.gusSalesforcePlatformEventService.publishEvent(
        eventName,
        platformEventRequest,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
