import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class GusSalesforcePlatformEventService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
  ) {}
  async publishEvent(eventName, request): Promise<any> {
    const res = await this.gusSalesforceService.executeAPI(
      `sobjects/${eventName}`,
      'POST',
      request,
    );
    return res.data;
  }
}
