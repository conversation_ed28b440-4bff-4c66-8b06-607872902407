import {
  Controller,
  Body,
  HttpStatus,
  HttpException,
  Get,
  Param,
} from '@nestjs/common';
import { GusSalesforcePricebookEntryService } from './pricebookEntry.service';
import { PricebookEntry } from 'apps/libs/salesforce/default.fields';
@Controller('/salesforce/gus')
export class GusSalesforcePricebookEntryController {
  constructor(
    private readonly gusSalesforcePricebookEntryervice: GusSalesforcePricebookEntryService,
  ) {}
  @Get('/pricebookentry')
  async getPricebookEntry(): Promise<any> {
    try {
      return await this.gusSalesforcePricebookEntryervice.getPricebookEntry(
        PricebookEntry.programmeDetails,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
