import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforcePricebookEntryService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getPricebookEntry(
    queryFields,
    queryCondition = null,
    isTotalRequired = false,
  ): Promise<any> {
    const pricebookEntry = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'PricebookEntry',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return pricebookEntry;
  }
}
