import {
  Controller,
  Body,
  HttpStatus,
  HttpException,
  Get,
  Param,
} from '@nestjs/common';
import { GusSalesforceProductService } from './product2.service';
import { Product2 } from 'apps/libs/salesforce/default.fields';
@Controller('/salesforce/gus')
export class GusSalesforceProduct2Controller {
  constructor(
    private readonly gusSalesforceProduct2Service: GusSalesforceProductService,
  ) {}
  @Get('/Product')
  async getProducts(): Promise<any> {
    try {
      return await this.gusSalesforceProduct2Service.getProducts(
        Product2.Product2Details,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
