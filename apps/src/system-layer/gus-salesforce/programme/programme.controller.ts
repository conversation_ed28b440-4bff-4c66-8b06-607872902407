import {
  Controller,
  Body,
  HttpStatus,
  HttpException,
  Get,
  Param,
  Req,
} from '@nestjs/common';
import { GusSalesforceProgrammeService } from './programme.service';
import { Request } from 'express';
import * as salesForceHelper from '../../../../libs/utils/utils';
@Controller('/salesforce/gus')
export class GusSalesforceProgrammeController {
  constructor(
    private readonly gusSalesforceProgrammeService: GusSalesforceProgrammeService,
  ) {}
  @Get('/programme')
  async getProgrammes(@Req() request: Request): Promise<any> {
    try {
      const entityName = 'Programme__c';
      let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
        entityName,
        request['allowedEntities'],
      );
      if (whereClause) {
        whereClause = `where ${whereClause}`;
      }
      return await this.gusSalesforceProgrammeService.getProgrammes(
        fieldsRequired,
        whereClause,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
