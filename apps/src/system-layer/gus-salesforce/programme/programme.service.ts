import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceProgrammeService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getProgrammes(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const Programmes = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Programme__c',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return Programmes;
  }
}
