import {
  Controller,
  HttpStatus,
  HttpException,
  Get,
  Req,
} from '@nestjs/common';
import { GusSalesforceSchoolService } from './school.service';
import * as salesForceHelper from 'apps/libs/utils/utils';
import { Request } from 'express';
@Controller('/salesforce/gus')
export class GusSalesforceSchoolController {
  constructor(
    private readonly gusSalesforceSchoolService: GusSalesforceSchoolService,
  ) {}
  @Get('/school')
  async getSchool(@Req() request: Request): Promise<any> {
    try {
      const entityName = 'School__c';
      const entityOperation = 'lookup';
      let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
        entityName,
        request['allowedEntities'],
        entityOperation,
      );
      if (whereClause) {
        whereClause =
          `where ` + whereClause + ` group by Brand__c order by Brand__c asc`;
      }
      return await this.gusSalesforceSchoolService.getSchool(
        fieldsRequired,
        whereClause,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
