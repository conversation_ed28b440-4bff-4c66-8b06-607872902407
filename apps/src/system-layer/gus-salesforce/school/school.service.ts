import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceSchoolService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getSchool(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    if (queryCondition === '') {
      queryCondition = ' by order by name asc';
    }
    const levels = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'School__c',
      ),
      'GET',
      isTotalRequired,
    );
    return levels;
  }
}
