import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceSurveyService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private salesforceGUSService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}

  async createSurvey(data): Promise<any> {
    try {
      if (!this.utilitiesService.checkRequiredField(data, 'Survey_AP__c')) {
        return;
      }
      const response = await this.salesforceGUSService.executeAPI(
        `sobjects/Survey_AP__c`,
        'POST',
        data,
      );
      return { data: response.data };
    } catch (error) {
      console.log('err', error);

      throw error;
    }
  }
}
