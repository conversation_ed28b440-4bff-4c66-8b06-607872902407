import {
  <PERSON>,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Req,
} from '@nestjs/common';
import { GusSalesforceTaskService } from './task.salesforce.service';
import { Request } from 'express';
import * as salesForceHelper from '../../../../libs/utils/utils';

@Controller('/salesforce/gus')
export class GusSalesForceTaskController {
  constructor(
    private readonly GUSSalesforceApplicationService: GusSalesforceTaskService,
  ) {}
  @Get('/task/:id')
  async getTaskFromOpportunityId(
    @Param('id') id: string,
    @Req() request: Request,
  ): Promise<any> {
    const entityName = 'Task';
    const entityOperation = 'opportunityAdmissionComments';
    let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      entityOperation,
    );
    if (whereClause) {
      console.log(whereClause);
      whereClause = `where WhatId = '${id}' and ` + whereClause;
    }
    try {
      return await this.GUSSalesforceApplicationService.getTask(
        fieldsRequired,
        whereClause,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
