import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class GusSalesforceTaskService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private salesforceGUSService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getTask(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const task = await this.salesforceGUSService.executeAPI(
      this.utilitiesService.queryBuilder(queryFields, queryCondition, 'Task'),
      'GET',
      null,
      isTotalRequired,
    );
    return task;
  }
}
