import { Controller, Get, Param, Req } from '@nestjs/common';
import { Request } from 'express';
import { GusSalesforceUserService } from './user.service';
import * as salesForceHelper from '../../../../libs/utils/utils';

@Controller('salesforce/gus')
export class GusSalesforceUserController {
  constructor(
    private readonly gusSalesforceUserService: GusSalesforceUserService,
  ) {}

  @Get('userbyemail/:emailId')
  async getUserByEmail(
    @Param('emailId') emailId: string,
    @Req() request: Request,
  ) {
    const entityName = 'User';
    const entityOperation = 'userByEmail';
    const { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      entityOperation,
    );

    let fileCondition = `where Email = '${emailId}'`;
    if (whereClause) {
      fileCondition += `AND ${whereClause}`;
    }
    const response = await this.gusSalesforceUserService.getUsers(
      fieldsRequired,
      fileCondition,
    );
    return response;
  }
}
