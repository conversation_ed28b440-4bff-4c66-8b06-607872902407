import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
import { Inject, Injectable } from '@nestjs/common';

@Injectable()
export class GusSalesforceUserService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
    private utilitiesService: UtilitiesService,
  ) {}

  async getUsers(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const userDetails = await this.gusSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(queryFields, queryCondition, 'User'),
      'GET',
      null,
      isTotalRequired,
    );

    return userDetails[0];
  }
}
