import { Inject, Injectable } from '@nestjs/common';
import { ObjectConfig } from 'apps/src/system-layer/gus-salesforce/objectConfig';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
@Injectable()
export class UtilitiesService {
  constructor(
    @Inject('CloudWatchLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly loggerEnum: LoggerEnum,
  ) {}
  queryBuilder(queryFields, queryCondition, sobject) {
    let query = `Select ${queryFields} from ${sobject} `;
    if (queryCondition) {
      query = query + queryCondition;
    }
    return encodeURIComponent(query);
  }
  checkRequiredField(request, object, otherRequiredFields?) {
    if (request) {
      const requiredFields = otherRequiredFields
        ? ObjectConfig[object]?.[otherRequiredFields]
        : ObjectConfig[object]?.requiredFields;
      if (requiredFields) {
        const missingFields = requiredFields.filter(
          (field) => !(field in request),
        );
        if (missingFields.length > 0) {
          return false;
        }
      }
      return true;
    }
    return false;
  }
  async recordGUSLogs(
    applicationDetails,
    request,
    event,
    logMessage,
    secondaryKey,
    object,
    usecase?,
    destinationRequest?,
    response?,
  ): Promise<any> {
    const requestId = request.headers['correlation-id']
      ? request.headers['correlation-id']
      : request['requestId'];
    const brand = request?.allowedEntities?.brand;
    await this.cloudWatchLoggerService.log(
      requestId,
      new Date().toISOString(),
      this.loggerEnum.Component.GUS_EIP_SERVICE,
      this.loggerEnum.Component
        .GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      this.loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.loggerEnum.UseCase[usecase] ??
        usecase ??
        this.loggerEnum.UseCase.GUS_SALESFORCE_OPERATION,
      applicationDetails,
      destinationRequest ? destinationRequest : applicationDetails,
      logMessage,
      brand,
      secondaryKey,
      `gus-middleware-service/${requestId}`,
      'Application_Form_Id__c',
      applicationDetails?.applicationId,
      object,
      null,
      object,
      null,
      response,
    );
  }
  async recordGUSErrorLogs(
    applicationDetails,
    request,
    event,
    errorMessage,
    secondaryKey,
    object,
    usecase?,
    destinationRequest?,
    response?,
  ): Promise<any> {
    const requestId = request.headers['Correlation-Id'];
    const brand = request?.allowedEntities?.brand;
    await this.cloudWatchLoggerService.error(
      requestId,
      new Date().toISOString(),
      this.loggerEnum.Component.GUS_EIP_SERVICE,
      this.loggerEnum.Component
        .GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      this.loggerEnum.Component.GUS_SALESFORCE,
      event,
      this.loggerEnum.UseCase[usecase] ??
        usecase ??
        this.loggerEnum.UseCase.GUS_SALESFORCE_OPERATION,
      applicationDetails,
      destinationRequest ? destinationRequest : applicationDetails,
      errorMessage,
      brand,
      secondaryKey,
      `gus-middleware-service/${requestId}`,
      'Application_Form_Id__c',
      applicationDetails?.applicationId,
      object,
      null,
      object,
      null,
      response,
    );
  }
}
