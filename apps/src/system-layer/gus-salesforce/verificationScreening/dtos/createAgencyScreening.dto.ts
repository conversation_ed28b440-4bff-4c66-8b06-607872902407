import { IsOptional, IsString, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';

class ScreeningIdentifiersDto {
  @IsString()
  ClientID: string;
}

export class ScreeningEnquiryInfoDto {
  @ValidateNested()
  @Type(() => ScreeningIdentifiersDto)
  @Transform(({ value }) => {
    return JSON.stringify(value) ;
  }, {toPlainOnly: true })
  Screening_Identifiers__c: string;

  @IsString()
  Account__c: string;

  @IsString()
  Business_registration_number__c: string;

  @IsString()
  City__c: string;

  @IsString()
  Name_of_business__c: string;

  @IsString()
  Country_of_incorporation__c: string;

  @IsString()
  State_Province_of_Address__c: string;

  @IsString()
  State_Province_of_Incorporation__c: string;

  @IsString()
  ZIP_code__c: string;

  @IsString()
  VAT_Number__c: string;

  @IsString()
  KYB_Enquiry_Type__c: string;

  @IsOptional()
  @IsString()
  RecordTypeId?: string = '';
}

export class CreateAgencyScreeningDto {
  @ValidateNested()
  @Type(() => ScreeningEnquiryInfoDto)
  data: ScreeningEnquiryInfoDto;
}
