import { Body, Controller, Get, HttpException, HttpStatus, Param, Post, Query, UsePipes, ValidationPipe } from '@nestjs/common';
import { GusSalesforceVerificationScreeningService } from './verificationScreening.service';
import { CreateAgencyScreeningDto } from './dtos/createAgencyScreening.dto';

@Controller('/salesforce/gus')
@UsePipes(new ValidationPipe({ whitelist: true }))
export class GusSalesforceVerificationScreeningController {
    constructor(private readonly gusSalesforceVerificationScreeningService: GusSalesforceVerificationScreeningService){}
    
    @Post('agentKyb/screeningenquiry/:enquiryTypeId')
    async createScreeningEnquiry(
        @Body() requestBody: CreateAgencyScreeningDto,
        @Param('enquiryTypeId') enquiryTypeId: string
    ): Promise<any> {
        try{
            requestBody.data.RecordTypeId = enquiryTypeId;
            return await this.gusSalesforceVerificationScreeningService.createScreeningEnquiry(requestBody?.data);
        }catch(error){
            throw new HttpException(`Unable to create screening enquiry record in salesforce - ${error}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Post('/agentKyb/screeningData/:enquiryTypeId')
    async getScreeningDataByAccount(
        @Param('enquiryTypeId') enquiryTypeId: string,
        @Query() query: Record<string, any>,
        @Body() accountIds: Array<string>
    ): Promise<any> {
        try {
        const queryCondition = accountIds.length>0
            ?`Account__c in (${accountIds.map(id => `'${id}'`).join(',')})`
            :`Account__c = '${query.accountId}'`
        return await this.gusSalesforceVerificationScreeningService.getScreeningDataById(
            query.enquiryType,
            'Screening_Identifiers__c, ID, Account__c',
            queryCondition
        );
        }catch(error){
            throw new HttpException(`Unable to fetch screening enquiry record from salesforce - ${error}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Get('/agentkyb/screeningData/:enquiryTypeId')
    async getScreeningDataByIdentifier(
        @Query() query: Record<string, any>
    ): Promise<any> {
        try {	
        const queryCondition = `Screening_Identifiers__c = '{"ClientID":"${query.clientId}"}'`
        return await this.gusSalesforceVerificationScreeningService.getScreeningDataById(
            query.enquiryType,
            'Screening_Identifiers__c, ID, Account__c',
            queryCondition
        );
        }catch(error){
            throw new HttpException(`Unable to fetch screening enquiry record from salesforce - ${error}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
