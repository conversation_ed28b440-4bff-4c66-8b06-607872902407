import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';

@Injectable()
export class GusSalesforceVerificationScreeningService {
    constructor(
        @Inject('GUS_SALESFORCE_SOURCE')
        private readonly gusSalesforceService: SalesforceService,
        private readonly utilitiesService: UtilitiesService,
      ) {}

    async createScreeningEnquiry(data): Promise<any> {
        try {
          if (!this.utilitiesService.checkRequiredField(data, 'Screening_Enquiry__c')) {
            throw new BadRequestException('Malformed fields');
          }
          const response = await this.gusSalesforceService.executeAPI(
            `sobjects/Screening_Enquiry__c`,
            'POST',
            data,
          );
          return { data: response?.data };
        } catch (error) {
          throw error;
        }
    }

    async getScreeningDataById(
      enquiryType,
      requiredFields,
      queryCondition = null,
    ): Promise<any> {
      try{
        let query = `Select ${requiredFields} from Screening_Enquiry__c 
                     where KYB_Enquiry_Type__c= '${enquiryType}'`;
        if (queryCondition != null) {
          query = `${query} and ${queryCondition}`;
        }
        console.log('sf query', query);
        const ScreeningIdentifiers = await this.gusSalesforceService.executeAPI(
          query,
          'GET',
        );
        return ScreeningIdentifiers;
      }catch(error){
        throw error;
      }
    }
}
