import {
  Controller,
  Body,
  HttpStatus,
  HttpException,
  Get,
  Param,
} from '@nestjs/common';
import { GusSalesforceVerticalService } from './vertical.service';
import { Vertical__c } from 'apps/libs/salesforce/default.fields';
@Controller('/salesforce/gus')
export class GusSalesforceVerticalController {
  constructor(
    private readonly gusSalesforceVerticalService: GusSalesforceVerticalService,
  ) {}
  @Get('/vertical')
  async getVerticals(): Promise<any> {
    try {
      return await this.gusSalesforceVerticalService.getVerticals(Vertical__c);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
