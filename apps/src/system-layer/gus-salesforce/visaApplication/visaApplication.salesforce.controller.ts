import {
  Body,
  Controller,
  Get,
  Param,
  Req,
  Post,
  HttpException,
  HttpStatus,
  Patch,
} from '@nestjs/common';
import { GusSalesforceVisaApplicationService } from './visaApplication.salesforce.service';
import * as salesForceHelper from 'apps/libs/utils/utils';
import { Request } from 'express';
import { UtilitiesService } from '../utilities.service';
import { LoggerEnum } from '@gus-eip/loggers';

@Controller('/salesforce/gus/')
export class GusSalesforceVisaApplicationController {
  constructor(
    private readonly gusSalesforceVisaApplicationService: GusSalesforceVisaApplicationService,
    private readonly utilitiesService: UtilitiesService,
    private readonly loggerEnum: LoggerEnum,
  ) {}

  @Post('visaapplication')
  async createVisaApplication(@Body() payload: any): Promise<any> {
    try {
      return await this.gusSalesforceVisaApplicationService.createVisaApplication(
        payload,
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create visa application',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch('visaapplication/:id')
  async updateVisaApplication(
    @Param('id') id: string,
    @Body() payload: any,
  ): Promise<any> {
    if (!id) {
      throw new HttpException('ID is required', HttpStatus.BAD_REQUEST);
    }

    try {
      return await this.gusSalesforceVisaApplicationService.updateVisaApplication(
        id,
        payload,
      );
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update visa application',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
