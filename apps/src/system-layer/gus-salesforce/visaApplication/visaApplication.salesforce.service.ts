import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
import { error } from 'console';

@Injectable()
export class GusSalesforceVisaApplicationService {
  constructor(
    @Inject('GUS_SALESFORCE_SOURCE')
    private salesforceGUSService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}

  async createVisaApplication(data): Promise<any> {
    try {
      if (
        !this.utilitiesService.checkRequiredField(data, 'Visa_Application__c')
      ) {
        throw new Error('Missing required fields to create Visa Application');
      }
      const response = await this.salesforceGUSService.executeAPI(
        `sobjects/Visa_Application__c`,
        'POST',
        data,
      );
      console.log('Response ->', response);

      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async updateVisaApplication(id, data): Promise<any> {
    try {
      const response = await this.salesforceGUSService.executeAPI(
        `sobjects/Visa_Application__c/${id}`,
        'PATCH',
        data,
      );
      console.log('Response ->', response);

      return {
        success: response.status === 204,
      };
    } catch (error) {
      throw error;
    }
  }
}
