import { Module } from '@nestjs/common';
import { StudentDetailsController } from './studentdetails/studentdetails.controller';
import { StudentDetailsService } from './studentdetails/studentdetails.service';
import { StudentDocumentService } from './studentdocuments/studentdocument.service';
@Module({
  providers: [
    StudentDetailsController,
    StudentDetailsService,
    StudentDocumentService,
  ],
  controllers: [StudentDetailsController],
  exports: [
    StudentDetailsController,
    StudentDetailsService,
    StudentDocumentService,
  ],
})
export class StudentDetailsModule {}
