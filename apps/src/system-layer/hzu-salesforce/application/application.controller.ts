import {
  Body,
  Controller,
  Get,
  Param,
  Req,
  Post,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { HZUSalesforceApplicationService } from './application.service';
import * as salesForceHelper from 'apps/libs/utils/utils';
import { Request } from 'express';
@Controller('/salesforce/hzu')
export class HZUSalesForceApplicationController {
  constructor(
    private readonly hzuSalesforceApplicationService: HZUSalesforceApplicationService,
  ) { }
  @Get('/applicationIdByExternalId/:externalId')
  async getApplicationIdByExternalId(
    @Param('externalId') externalId: string,
    @Req() request: Request,
  ): Promise<any> {
    const entityName = 'hed__Application__c';
    let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      'applicationDetails',
    );
    whereClause = ` where External_ID__c = '${externalId}' `;
    return await this.hzuSalesforceApplicationService.getApplicationIdByExternalId(
      fieldsRequired,
      whereClause,
    );
  }

  @Post('/updateApplication/:id')
  async updateApplicationIdByExternalId(
    @Param('id') id: string,
    @Body() details: any
  ): Promise<any> {
    try {
      return await this.hzuSalesforceApplicationService.updateApplication(
        id,
        details,
      );
    } catch (error) {
      throw error
    }
  }
}
