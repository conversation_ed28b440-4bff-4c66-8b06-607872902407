import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class HZUSalesforceApplicationService {
  constructor(
    @Inject('HZU_SALESFORCE_SOURCE')
    private hzuSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) { }
  async getApplicationIdByExternalId(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const applicationResponse = await this.hzuSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'hed__Application__c',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return applicationResponse[0];
  }

  async updateApplication(id, data): Promise<any> {
    const response = await this.hzuSalesforceService.executeAPI(
      `sobjects/hed__Application__c/${id}`,
      'PATCH',
      data,
    );
    return {
      statusCode: response.status,
    };
  }
}
