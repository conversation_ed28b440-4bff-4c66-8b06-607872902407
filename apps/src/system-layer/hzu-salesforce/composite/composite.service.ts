import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class HZUSalesforceCompositeService {
  constructor(
    @Inject('HZU_SALESFORCE_SOURCE')
    private HZUSalesforceService: SalesforceService,
  ) {}
  async compositeRequest(request): Promise<any> {
    return await this.HZUSalesforceService.compositeRequest(request);
  }
}
