import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class HZUSalesforceContactService {
  constructor(
    @Inject('HZU_SALESFORCE_SOURCE')
    private hzuSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getContact(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const contact = await this.hzuSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Contact',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return contact;
  }
}
