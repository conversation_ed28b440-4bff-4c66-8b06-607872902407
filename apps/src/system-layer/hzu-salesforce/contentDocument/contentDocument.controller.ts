import { Controller, Get, Param, Query, Req } from '@nestjs/common';
import { Request } from 'express';
import * as salesForceHelper from '../../../../libs/utils/utils';
import { UtilitiesService } from '../utilities.service';
import { HZUSalesforceContentDocumentService } from './contentDocument.service';
import { LoggerEnum } from '@gus-eip/loggers';
@Controller('/salesforce/hzu')
export class HZUSalesForceContentDocumentController {
    constructor(
        private readonly hzuSalesforceContentDocumentService: HZUSalesforceContentDocumentService,
        private readonly utilitiesService: UtilitiesService,
        private readonly loggerEnum: LoggerEnum
    ) { }
    @Get('/contentDocumentById/:id')
    async getContentDocumentById(
        @Param('id') contentDocumentId: string,
        @Req() request: Request,
        @Query('scenario') scenario?: string,
    ): Promise<any> {
        const entityName = 'ContentDocument';
        let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
            entityName,
            request['allowedEntities'],
            'contentDocumentById',
        );
        whereClause = ` where Id = '${contentDocumentId}' `;
        await this.utilitiesService.recordHZULogs(
            contentDocumentId,
            request,
            this.loggerEnum.Event.CONTENT_CONTENT_DOCUMENT_BY_ID,
            'get content document by id',
            null,
            scenario,
            { fieldsRequired, whereClause },
            null,
        );
        const response =
            await this.hzuSalesforceContentDocumentService.getContentDocumentById(
                fieldsRequired,
                whereClause,
            );
        await this.utilitiesService.recordHZULogs(
            contentDocumentId,
            request,
            this.loggerEnum.Event.CONTENT_CONTENT_DOCUMENT_BY_ID,
            'get content document by id',
            null,
            scenario,
            { fieldsRequired, whereClause },
            response,
        );
        return response;
    }
}
