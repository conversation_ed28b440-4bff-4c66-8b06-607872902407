import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';

@Injectable()
export class HZUSalesforceContentDocumentService {
    constructor(
        @Inject('HZU_SALESFORCE_SOURCE')
        private hzuSalesforceService: SalesforceService,
        private readonly utilitiesService: UtilitiesService,
    ) { }
    async getContentDocumentById(
        queryFields,
        queryCondition = '',
        isTotalRequired = false,
    ): Promise<any> {
        const contentVersion = await this.hzuSalesforceService.executeAPI(
            this.utilitiesService.queryBuilder(
                queryFields,
                queryCondition,
                'ContentDocument',
            ),
            'GET',
            null,
            isTotalRequired,
        );

        return contentVersion[0]
    }
}
