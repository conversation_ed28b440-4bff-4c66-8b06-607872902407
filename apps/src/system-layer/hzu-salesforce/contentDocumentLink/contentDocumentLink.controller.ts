import {
  Body,
  Controller,
  Get,
  Param,
  Req,
  Post,
  HttpException,
  HttpStatus,
  Query,
} from '@nestjs/common';
import * as salesForceHelper from 'apps/libs/utils/utils';
import { Request } from 'express';
import { HZUSalesforceContentDocumentLinkService } from './contentDocumentLink.service';
import { UtilitiesService } from '../utilities.service';
import { LoggerEnum } from '@gus-eip/loggers';
@Controller('/salesforce/hzu')
export class HZUSalesForceContentDocumentLinkController {
  constructor(
    private readonly hzuSalesforceContentDocumentLinkService: HZUSalesforceContentDocumentLinkService,
    private readonly utilitiesService: UtilitiesService,
    private readonly loggerEnum: LoggerEnum,
  ) { }
  @Get('/contentDocumentLinkByLinkedEntityId/:linkedEntityId')
  async getContentDocumentLinkByLinkedEntityId(
    @Param('linkedEntityId') linkedEntityId: string,
    @Req() request: Request,
    @Query('scenario') scenario?: string,
  ): Promise<any> {
    try {
      const entityName = 'ContentDocumentLink';
      let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
        entityName,
        request['allowedEntities'],
        'contentDocumentIdByLinkedEntityId',
      );
      whereClause = ` where LinkedEntityId = '${linkedEntityId}' `;
      await this.utilitiesService.recordHZULogs(
        linkedEntityId,
        request,
        this.loggerEnum.Event.CONTENT_DOCUMENT_LINK_BY_LINKED_ENTITY_ID,
        'get content document link by linked entity id',
        linkedEntityId,
        'ContentDocumentLink',
        scenario,
        { fieldsRequired, whereClause },
        null,
      );
      const response =
        await this.hzuSalesforceContentDocumentLinkService.getContentDocumentLinkByLinkedEntityId(
          fieldsRequired,
          whereClause,
        );
      await this.utilitiesService.recordHZULogs(
        linkedEntityId,
        request,
        this.loggerEnum.Event.CONTENT_DOCUMENT_LINK_BY_LINKED_ENTITY_ID_COMPLETED,
        'get content document link by linked entity id',
        linkedEntityId,
        scenario,
        { fieldsRequired, whereClause },
        response,
      );
      return response;
    } catch (error) {
      await this.utilitiesService.recordHZUErrorLogs(
        linkedEntityId,
        request,
        this.loggerEnum.Event.CONTENT_DOCUMENT_LINK_BY_LINKED_ENTITY_ID_FAILED,
        error,
        linkedEntityId,
        'ContentDocumentLink',
        scenario
      );
      throw error;
    }
  }
}
