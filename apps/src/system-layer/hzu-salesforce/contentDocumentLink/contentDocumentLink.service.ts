import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class HZUSalesforceContentDocumentLinkService {
    constructor(
        @Inject('HZU_SALESFORCE_SOURCE')
        private hzuSalesforceService: SalesforceService,
        private readonly utilitiesService: UtilitiesService,
    ) { }
    async getContentDocumentLinkByLinkedEntityId(
        queryFields,
        queryCondition = '',
        isTotalRequired = false,
    ): Promise<any> {
        console.log(queryFields, queryCondition)
        const contentDocumentLink = await this.hzuSalesforceService.executeAPI(
            this.utilitiesService.queryBuilder(
                queryFields,
                queryCondition,
                'ContentDocumentLink',
            ),
            'GET',
            null,
            isTotalRequired,
        );
        return contentDocumentLink[0];
    }
}
