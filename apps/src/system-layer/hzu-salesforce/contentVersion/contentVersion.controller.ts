import { Controller, Get, Param, Query, Req } from '@nestjs/common';
import { HZUSalesforceContentVersionService } from './contentVersion.service';
import { Request } from 'express';
import * as salesForceHelper from '../../../../libs/utils/utils';
import { UtilitiesService } from '../utilities.service';
import { LoggerEnum } from '@gus-eip/loggers';
@Controller('/salesforce/hzu')
export class HZUSalesForceContentVersionController {
  constructor(
    private readonly hzuSalesforceContentVersionService: HZUSalesforceContentVersionService,
    private readonly utilitiesService: UtilitiesService,
    private readonly loggerEnum: LoggerEnum,
  ) { }
  @Get('/contentVersionByContentDocumentId/:contentDocumentId')
  async getContentVersionByContentDocumentId(
    @Param('contentDocumentId') contentDocumentId: string,
    @Req() request: Request,
    @Query('scenario') scenario?: string,
  ): Promise<any> {
    const entityName = 'ContentVersion';
    let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      'contentVersionBYContentDocumentId',
    );
    whereClause = ` where ContentDocumentId = '${contentDocumentId}' `;
    await this.utilitiesService.recordHZULogs(
      contentDocumentId,
      request,
      this.loggerEnum.Event.CONTENT_VERSION_BY_CONTENT_DOCUMENT_ID,
      'get content version by content document id',
      null,
      scenario,
      { fieldsRequired, whereClause },
      null,
    );
    const response =
      await this.hzuSalesforceContentVersionService.getContentVersionByContentDocumentId(
        fieldsRequired,
        whereClause,
      );
    await this.utilitiesService.recordHZULogs(
      contentDocumentId,
      request,
      this.loggerEnum.Event.CONTENT_VERSION_BY_CONTENT_DOCUMENT_ID,
      'get content version by content document id',
      null,
      scenario,
      { fieldsRequired, whereClause },
      response,
    );
    return response;
  }

  @Get('/contentVersionById/:contentVersionId')
  async getContentVersionById(
    @Param('contentVersionId') contentVersionId: string,
    @Req() request: Request,
    @Query('scenario') scenario?: string,
  ): Promise<any> {
    const entityName = 'ContentVersion';
    let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
      entityName,
      request['allowedEntities'],
      'contentVersionById',
    );
    whereClause = ` where Id = '${contentVersionId}'`;
    await this.utilitiesService.recordHZULogs(
      contentVersionId,
      request,
      this.loggerEnum.Event.CONTENT_VERSION_BY_ID,
      'get content version by id',
      null,
      scenario,
      { fieldsRequired, whereClause },
      null,
    );
    const response =
      await this.hzuSalesforceContentVersionService.getContentVersionById(
        fieldsRequired,
        whereClause,
      );
    await this.utilitiesService.recordHZULogs(
      contentVersionId,
      request,
      this.loggerEnum.Event.CONTENT_VERSION_BY_CONTENT_DOCUMENT_ID,
      'get content version by content document id',
      null,
      scenario,
      { fieldsRequired, whereClause },
      response,
    );
    return response;
  }
}
