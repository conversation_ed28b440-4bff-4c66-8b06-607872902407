import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
import { Buffer } from 'buffer';
import axios from 'axios';
@Injectable()
export class HZUSalesforceContentVersionService {
  constructor(
    @Inject('HZU_SALESFORCE_SOURCE')
    private hzuSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) { }
  async getContentVersionByContentDocumentId(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const contentVersion = await this.hzuSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'ContentVersion',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    const title = contentVersion?.[0]?.['Title']
    const base64 = await this.getVersionDataBase64(
      contentVersion?.[0]?.['VersionData'],
    );
    const pathOnClient = contentVersion?.[0]?.['PathOnClient']

    return {
      title,
      base64,
      pathOnClient
    }
  }

  async getContentVersionById(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    console.log({
      queryCondition
    })
    const contentVersion = await this.hzuSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'ContentVersion',
      ),
      'GET',
      null,
      isTotalRequired,
    );

    console.log(contentVersion)
    const title = contentVersion?.[0]?.['Title']
    const base64 = await this.getVersionDataBase64(
      contentVersion?.[0]?.['VersionData'],
    );
    const pathOnClient = contentVersion?.[0]?.['PathOnClient']

    return {
      title,
      base64,
      pathOnClient
    }
  }

  async getVersionDataBase64(versionDataUrl: string): Promise<string | void> {
    const clientCredentials = await this.hzuSalesforceService.getCredentials();
    const instanceUrl = clientCredentials.instance_url;

    const fullUrl = `${instanceUrl}${versionDataUrl}`;

    try {
      const response = await axios.get(fullUrl, {
        headers: {
          Authorization: `Bearer ${clientCredentials.access_token}`,
          Accept: 'application/octet-stream',
        },
        responseType: 'arraybuffer',
      });
      console.log('response', response);

      if (response.status === 200) {
        const buffer = Buffer.from(response.data);
        const base64data = buffer.toString('base64');
        console.log('Base64 data:', base64data);
        return base64data;
      } else {
        console.error(
          `Failed to download file. Status code: ${response.status}`,
        );
      }
    } catch (error) {
      console.error('Error fetching base64 data:', error);
    }
  }
}
