import { Body, Controller, Get, Param, Req, Post, HttpException, HttpStatus } from '@nestjs/common';
import * as salesForceHelper from 'apps/libs/utils/utils';
import { Request } from 'express';
import { HZUSalesforceDocumentChecklistItemService } from './documentChecklistItem.service';
@Controller('/salesforce/hzu')
export class HZUSalesForceDocumentChecklistItemController {
    constructor(
        private readonly hzuSalesforceDocumentChecklistItemService: HZUSalesforceDocumentChecklistItemService,
    ) { }
    @Get('/documentChecklistItemDetails/:applicationId')
    async getDocumentChecklistItemByApplication(
        @Param('applicationId') applicationId: string,
        @Req() request: Request,
    ): Promise<any> {
        const entityName = 'DocumentChecklistItem';
        let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
            entityName,
            request['allowedEntities'],
            'checklistItemDetails',
        );
        console.log(fieldsRequired, whereClause)
        whereClause = ` where Application__c = '${applicationId}' `;
        return await this.hzuSalesforceDocumentChecklistItemService.getDocumentChecklistItemByApplicationId(
            fieldsRequired,
            whereClause,
        );
    }

    @Get('/documentChecklistItemDetailsById/:id')
    async getDocumentChecklistItemById(
        @Param('id') id: string,
        @Req() request: Request,
    ): Promise<any> {
        const entityName = 'DocumentChecklistItem';
        let { fieldsRequired, whereClause } = await salesForceHelper.queryBuilder(
            entityName,
            request['allowedEntities'],
            'checklistItemDetailsById',
        );
        whereClause = ` where Id = '${id}' `;
        return await this.hzuSalesforceDocumentChecklistItemService.getDocumentChecklistItemById(
            fieldsRequired,
            whereClause,
        );
    }
}

