import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class HZUSalesforceDocumentChecklistItemService {
    constructor(
        @Inject('HZU_SALESFORCE_SOURCE')
        private hzuSalesforceService: SalesforceService,
        private readonly utilitiesService: UtilitiesService,
    ) { }
    async getDocumentChecklistItemByApplicationId(
        queryFields,
        queryCondition = '',
        isTotalRequired = false,
    ): Promise<any> {
        const documentCheckListItemResponse = await this.hzuSalesforceService.executeAPI(
            this.utilitiesService.queryBuilder(
                queryFields,
                queryCondition,
                'DocumentChecklistItem',
            ),
            'GET',
            null,
            isTotalRequired,
        );
        return documentCheckListItemResponse;
    }

    async getDocumentChecklistItemById(
        queryFields,
        queryCondition = '',
        isTotalRequired = false,
    ): Promise<any> {
        const documentCheckListItemResponse = await this.hzuSalesforceService.executeAPI(
            this.utilitiesService.queryBuilder(
                queryFields,
                queryCondition,
                'DocumentChecklistItem',
            ),
            'GET',
            null,
            isTotalRequired,
        );
        return documentCheckListItemResponse[0];
    }
}
