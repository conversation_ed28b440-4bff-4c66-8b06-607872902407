import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class HZUSalesforceHedEducationHistoryService {
  constructor(
    @Inject('HZU_SALESFORCE_SOURCE')
    private hzuSalesforceService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getHedEducationHistory(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    const hedEducationHistory = await this.hzuSalesforceService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'hed__Education_History__c',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return hedEducationHistory;
  }
}
