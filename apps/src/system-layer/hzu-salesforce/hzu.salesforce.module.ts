import { Module } from '@nestjs/common';
import { HZUSalesforceCompositeService } from './composite/composite.service';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { HZUSalesForceOpportunityController } from 'apps/src/system-layer/hzu-salesforce/opportunity/opportunity.salesforce.controller';
import { HZUSalesforceOpportunityService } from 'apps/src/system-layer/hzu-salesforce/opportunity/opportunity.salesforce.service';
import { UtilitiesService } from './utilities.service';
import { HZUSalesforceHedEducationHistoryService } from './hedEducationHistory/hededucationhistory.service';
import { HZUSalesforceContactService } from './contact/contact.service';
import { HZUSalesforceApplicationService } from './application/application.service';
import { HZUSalesforceDocumentChecklistItemService } from './documentChecklistItem/documentChecklistItem.service';
import { HZUSalesForceApplicationController } from './application/application.controller';
import { HZUSalesForceDocumentChecklistItemController } from './documentChecklistItem/documentChecklistItem.controller';
import { HZUSalesForceContentDocumentLinkController } from './contentDocumentLink/contentDocumentLink.controller';
import { HZUSalesforceContentDocumentLinkService } from './contentDocumentLink/contentDocumentLink.service';
import { HZUSalesForceContentVersionController } from './contentVersion/contentVersion.controller';
import { HZUSalesforceContentVersionService } from './contentVersion/contentVersion.service';
import { ConfigModule } from '@nestjs/config';
import { LoggerModule } from '@gus-eip/loggers';
import { HZUSalesForceContentDocumentController } from './contentDocument/contentDocument.controller';
import { HZUSalesforceContentDocumentService } from './contentDocument/contentDocument.service';
@Module({
  providers: [
    HZUSalesforceOpportunityService,
    HZUSalesforceCompositeService,
    UtilitiesService,
    HZUSalesforceHedEducationHistoryService,
    HZUSalesforceContactService,
    HZUSalesforceApplicationService,
    HZUSalesforceDocumentChecklistItemService,
    HZUSalesforceContentDocumentLinkService,
    HZUSalesforceContentVersionService,
    HZUSalesforceContentDocumentService,
    {
      provide: 'HZU_SALESFORCE_SOURCE',
      useFactory: async () => {
        return new SalesforceService(
          process.env.HZU_SF_CLIENT_ID,
          process.env.HZU_SF_CLIENT_SECRET,
          process.env.HZU_AUTH_URL,
          process.env.HZU_ACCESS_TOKEN_SECRET,
          process.env.HZU_SF_GRANT_TYPE,
          process.env.HZU_SF_USERNAME,
          process.env.HZU_SF_PASSWORD,
        );
      },
      inject: [],
    },
  ],
  controllers: [
    HZUSalesForceOpportunityController,
    HZUSalesForceApplicationController,
    HZUSalesForceDocumentChecklistItemController,
    HZUSalesForceContentDocumentLinkController,
    HZUSalesForceContentVersionController,
    HZUSalesForceContentDocumentController
  ],
  exports: [
    HZUSalesforceCompositeService,
    HZUSalesforceOpportunityService,
    HZUSalesforceHedEducationHistoryService,
    HZUSalesforceContactService,
    HZUSalesforceApplicationService,
    HZUSalesforceDocumentChecklistItemService,
    HZUSalesforceContentDocumentService,
    UtilitiesService,
  ],
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    LoggerModule.forRoot({
      region: process.env.REGION,
      logGroupName: process.env.HZU_LOGGER_LOG_GROUP_NAME,
      teamWebhookUrl: process.env.TEAMS_WEBHOOK_URL,
      isAlertNeeded: false,
      options: 'HZUCloudWatchLogger',
    }),
  ],
})
export class HZUSalesforceModule { }
