import {
  Body,
  Controller,
  Get,
  Param,
  Req,
  Post,
  HttpException,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { HZUSalesforceOpportunityService } from './opportunity.salesforce.service';
import * as salesForceHelper from 'apps/libs/utils/utils';
import { UtilitiesService } from '../utilities.service';
import { Request } from 'express';
import { LoggerEnum } from '@gus-eip/loggers';
@Controller('/salesforce/hzu')
export class HZUSalesForceOpportunityController {
  constructor(
    private readonly HZUSalesforceOpportunityService: HZUSalesforceOpportunityService,
    private readonly utilitiesService: UtilitiesService,
    private readonly loggerEnum: LoggerEnum,
  ) { }
  @Get('/opportunityByApplicationId/:applicationId')
  async getOpportunities(
    @Param('applicationId') applicationId: string,
    @Req() request: Request,
    @Query('scenario') scenario?: string,
  ): Promise<any> {
    try {
      const entityName = 'Opportunity';
      const object = 'opportunityByApplicationId';
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          entityName,
          request['allowedEntities'],
          object,
        );
      let opportunityDetailsByIdCondition = ` where External_ID__c = '${applicationId}'`;
      console.log(opportunityDetailsByIdCondition, whereClause);
      if (whereClause) {
        opportunityDetailsByIdCondition = `${opportunityDetailsByIdCondition} and ${whereClause}`;
      }
      await this.utilitiesService.recordHZULogs(
        applicationId,
        request,
        this.loggerEnum.Event.OPPORTUNITY_BY_APPLICATIONID_INITIATED,
        'get opportunity by applicationId initiated',
        applicationId,
        'OPPORTUNITY',
        scenario,
        { fieldsRequired, opportunityDetailsByIdCondition },
      );
      const response =
        await this.HZUSalesforceOpportunityService.getOpportunities(
          fieldsRequired,
          opportunityDetailsByIdCondition,
        );
      await this.utilitiesService.recordHZULogs(
        applicationId,
        request,
        this.loggerEnum.Event.OPPORTUNITY_BY_APPLICATIONID_COMPLETED,
        'get opportunity by applicationId completed',
        applicationId,
        'OPPORTUNITY',
        scenario,
        null,
        response,
      );
      return response;
    } catch (error) {
      await this.utilitiesService.recordHZUErrorLogs(
        applicationId,
        request,
        'OPPORTUNITY_BY_APPLICATIONID_FAILED',
        error,
        applicationId,
        'OPPORTUNITY',
        scenario
      );
      throw error;
    }
  }
  @Get('/opportunityById/:opportunityId')
  async getOpportunityDetails(
    @Param('opportunityId') opportunityId: string,
    @Req() request: Request,
    @Query('scenario') scenario?: string,
  ): Promise<any> {
    try {
      const entityName = 'Opportunity';
      const object = 'opportunityById';
      const { fieldsRequired, whereClause } =
        await salesForceHelper.queryBuilder(
          entityName,
          request['allowedEntities'],
          object,
        );
      let opportunityDetailsByIdCondition = ` where Id = '${opportunityId}'`;
      console.log(opportunityDetailsByIdCondition, whereClause);
      if (whereClause) {
        opportunityDetailsByIdCondition = `${opportunityDetailsByIdCondition} and ${whereClause}`;
      }
      await this.utilitiesService.recordHZULogs(
        opportunityId,
        request,
        this.loggerEnum.Event.OPPORTUNITY_BY_OPPID_INITIATED,
        'get opportunity by oppId initiated',
        opportunityId,
        'OPPORTUNITY',
        scenario,
        { fieldsRequired, opportunityDetailsByIdCondition },
        null,
      );
      const response = {};
      const opportunityDetails =
        await this.HZUSalesforceOpportunityService.getOpportunities(
          fieldsRequired,
          opportunityDetailsByIdCondition,
        );
      response['Opportunity'] = opportunityDetails[0];
      await this.utilitiesService.recordHZULogs(
        opportunityId,
        request,
        this.loggerEnum.Event.OPPORTUNITY_BY_OPPID_COMPLETED,
        'get opportunity by oppId completed',
        opportunityId,
        'OPPORTUNITY',
        scenario,
        null,
        response,
      );
      return response;
    } catch (error) {
      await this.utilitiesService.recordHZUErrorLogs(
        opportunityId,
        request,
        this.loggerEnum.Event.OPPORTUNITY_BY_OPPID_FAILED,
        error,
        opportunityId,
        'OPPORTUNITY',
        scenario
      );
      throw error;
    }
  }

  @Post('/opportunity/:id')
  async updateApplicationFile(
    @Param('id') id: string,
    @Body() details: any,
    @Req() request: Request,
    @Query('scenario') scenario?: string
  ): Promise<any> {
    try {
      await this.utilitiesService.recordHZULogs(
        details,
        request,
        this.loggerEnum.Event.UPDATE_OPPORTUNITY_INITIATED,
        'update opportunity by oppId initiated',
        id,
        'OPPORTUNITY',
        scenario,
        null,
      );
      const response =
        await this.HZUSalesforceOpportunityService.updateOpportunity(
          id,
          details,
        );
      await this.utilitiesService.recordHZULogs(
        details,
        request,
        this.loggerEnum.Event.UPDATE_OPPORTUNITY_COMPLETED,
        'update opportunity by oppId completed successfully',
        'OPPORTUNITY',
        scenario,
        details,
        response,
      );
      return response;
    } catch (error) {
      await this.utilitiesService.recordHZUErrorLogs(
        id,
        request,
        this.loggerEnum.Event.UPDATE_OPPORTUNITY_FAILED,
        error,
        id,
        'OPPORTUNITY',
        scenario
      );
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
