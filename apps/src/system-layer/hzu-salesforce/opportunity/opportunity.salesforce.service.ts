import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { UtilitiesService } from '../utilities.service';
@Injectable()
export class HZUSalesforceOpportunityService {
  constructor(
    @Inject('HZU_SALESFORCE_SOURCE')
    private salesforceHZUService: SalesforceService,
    private readonly utilitiesService: UtilitiesService,
  ) {}
  async getOpportunities(
    queryFields,
    queryCondition = '',
    isTotalRequired = false,
  ): Promise<any> {
    console.log(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Opportunity',
      ),
    );
    const opportunity = await this.salesforceHZUService.executeAPI(
      this.utilitiesService.queryBuilder(
        queryFields,
        queryCondition,
        'Opportunity',
      ),
      'GET',
      null,
      isTotalRequired,
    );
    return opportunity;
  }
  async updateOpportunity(id, data): Promise<any> {
    const response = await this.salesforceHZUService.executeAPI(
      `sobjects/Opportunity/${id}`,
      'PATCH',
      data,
    );
    return {
      statusCode: response.status,
    };
  }
}
