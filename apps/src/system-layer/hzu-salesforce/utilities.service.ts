import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import { Inject, Injectable } from '@nestjs/common';
@Injectable()
export class UtilitiesService {
  constructor(
    @Inject('HZUCloudWatchLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly loggerEnum: LoggerEnum,
  ) { }
  queryBuilder(queryFields, queryCondition, sobject) {
    let query = `Select ${queryFields} from ${sobject} `;
    if (queryCondition) {
      query = query + queryCondition;
    }
    return encodeURIComponent(query);
  }
  async recordHZULogs(
    applicationDetails,
    request,
    event,
    logMessage,
    secondaryKey,
    object,
    usecase?,
    destinationRequest?,
    response?,
  ): Promise<any> {
    console.log('HEADER-->', request, request.headers);
    const requestId = request.headers['correlation-id']
      ? request.headers['correlation-id']
      : request['requestId'];
    const brand = request?.allowedEntities?.brand;
    await this.cloudWatchLoggerService.log(
      requestId,
      new Date().toISOString(),
      this.loggerEnum.Component.GUS_EIP_SERVICE,
      this.loggerEnum.Component
        .GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      this.loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.loggerEnum.UseCase[usecase] ?? usecase ?? this.loggerEnum.UseCase.GUS_SALESFORCE_OPERATION,
      applicationDetails,
      destinationRequest ? destinationRequest : applicationDetails,
      logMessage,
      brand,
      secondaryKey,
      `gus-middleware-service/${requestId}`,
      applicationDetails ? 'Application_Form_Id__c' : null,
      applicationDetails ? applicationDetails?.applicationId : null,
      object,
      null,
      object,
      null,
      response,
    );
  }
  async recordHZUErrorLogs(
    applicationDetails,
    request,
    event,
    errorMessage,
    secondaryKey,
    object,
    usecase?,
    destinationRequest?,
    response?,
  ): Promise<any> {
    console.log(request.headers);
    const requestId = request.headers['correlation-id']
      ? request.headers['correlation-id']
      : request['requestId'];
    const brand = request?.allowedEntities?.brand;
    await this.cloudWatchLoggerService.error(
      requestId,
      new Date().toISOString(),
      this.loggerEnum.Component.GUS_EIP_SERVICE,
      this.loggerEnum.Component
        .GUS_SALESFORCE_HZU_EDUCATIONCLOUD_INTEGRATION_HANDLER,
      this.loggerEnum.Component.HZU_EDUCATIONCLOUD,
      event,
      this.loggerEnum.UseCase[usecase] ?? usecase ?? this.loggerEnum.UseCase.GUS_SALESFORCE_OPERATION,
      applicationDetails,
      destinationRequest ? destinationRequest : applicationDetails,
      errorMessage,
      brand,
      secondaryKey,
      `gus-middleware-service/${requestId}`,
      applicationDetails ? 'Application_Form_Id__c' : null,
      applicationDetails ? applicationDetails?.applicationId : null,
      object,
      object,
      null,
      null,
      response,
    );
  }
}
