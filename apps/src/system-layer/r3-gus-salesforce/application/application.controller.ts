import { Controller, Body, Post, Patch, Get, Param } from '@nestjs/common';
import { R3GusSalesforceApplicationService } from './application.service';

@Controller('/salesforce/r3/gus')
export class R3GusSalesforceApplicationController {
  constructor(
    private readonly r3GusSalesforceApplicationService: R3GusSalesforceApplicationService,
  ) {}
  @Get('/applications/:applicationId')
  async getApplicationById(
    @Param('applicationId') applicationId: string,
  ): Promise<string> {
    try {
      return await this.r3GusSalesforceApplicationService.getApplicationById(
        applicationId,
      );
    } catch (error) {
      console.error('Error putting item:', error);
      throw error;
    }
  }
  @Patch('/applications')
  async updateApplication(@Body() requestBody): Promise<any> {
    return await this.r3GusSalesforceApplicationService.updatApplication(
      requestBody?.id,
      requestBody?.data,
    );
  }
}
