import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class R3GusSalesforceApplicationService {
  constructor(
    @Inject('R3_GUS_SALESFORCE_SOURCE')
    private r3SalesforceGusSource: SalesforceService,
  ) {}
  async getApplicationById(applicationId): Promise<any> {
    const query = `select R3_Picklist__c, First_Name__c, Last_Name__c, Email__c, Country__c, Mobile__c, Agent_Account__c from Application__c where Id = '${applicationId}'`;
    const applicationDetails = await this.r3SalesforceGusSource.executeAPI(
      query,
      'GET',
      null,
    );
    return applicationDetails;
  }
  async updatApplication(id, data): Promise<any> {
    const response = await this.r3SalesforceGusSource.executeAPI(
      `sobjects/Application__c/${id}`,
      'PATCH',
      data,
    );
    return {
      statusCode: response.status,
    };
  }
}
