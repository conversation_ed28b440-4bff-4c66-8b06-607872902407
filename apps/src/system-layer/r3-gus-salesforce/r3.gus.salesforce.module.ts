import { Module } from '@nestjs/common';
import { R3GusSalesforceApplicationController } from './application/application.controller';
import { R3GusSalesforceApplicationService } from './application/application.service';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Module({
  providers: [
    R3GusSalesforceApplicationController,
    R3GusSalesforceApplicationService,
    {
      provide: 'R3_GUS_SALESFORCE_SOURCE',
      useFactory: async () => {
        return new SalesforceService(
          process.env.R3_GUS_CONSUMER_KEY,
          process.env.R3_GUS_CONSUMER_SECRET,
          process.env.R3_GUS_AUTH_URL,
          process.env.R3_GUS_ACCESS_TOKEN_SECRET,
          process.env.R3_GUS_GRANT_TYPE,
          process.env.R3_GUS_USER_NAME,
          process.env.R3_GUS_PASSWORD,
        );
      },
      inject: [],
    },
  ],
  controllers: [R3GusSalesforceApplicationController],
  exports: [R3GusSalesforceApplicationController],
})
export class R3GusSalesforceModule {}
