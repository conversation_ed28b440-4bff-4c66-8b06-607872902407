import { <PERSON>, Body, Post, Patch, Get, Param } from '@nestjs/common';
import { R3SalesforceApplicationService } from './application.service';

@Controller('/salesforce/r3')
export class R3SalesforceApplicationController {
  constructor(
    private readonly r3SalesforceApplicationService: R3SalesforceApplicationService,
  ) {}
  @Post('/application')
  async createApplication(@Body() requestBody): Promise<any> {
    return await this.r3SalesforceApplicationService.createApplication(
      requestBody?.data,
    );
  }
  @Patch('/application')
  async updateApplication(@Body() requestBody): Promise<any> {
    return await this.r3SalesforceApplicationService.updateApplication(
      requestBody?.id,
      requestBody?.data,
    );
  }
  @Get('/application/:email')
  async getApplicationId(@Param('email') email: string): Promise<any> {
    return await this.r3SalesforceApplicationService.getApplicationId(email);
  }
  @Post('/gus/application')
  async getApplication(@Body() requestBody): Promise<any> {
    return await this.r3SalesforceApplicationService.getApplication(
      requestBody.fields,
      requestBody?.queryCondition,
    );
  }
}
