import {
  Injectable,
  Inject,
  InternalServerErrorException,
  HttpStatus,
} from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class R3SalesforceApplicationService {
  constructor(
    @Inject('R3_SALESFORCE_SOURCE')
    private salesforceR3Service: SalesforceService,
  ) {}
  async createApplication(data): Promise<any> {
    try {
      const response = await this.salesforceR3Service.executeAPI(
        `sobjects/Application__c`,
        'POST',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      const message = await this.checkErrorCode(error);
      const errorCode = error?.response?.message[0]?.errorCode;
      const statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      console.log(
        `[SALESFORCE ERROR]: ERROR IN CREATING APPLICATION FOR THIS CONTACTID ${data?.Applicant__c}, REQUEST: ${data}`,
      );
      throw new InternalServerErrorException(
        { message, errorCode, statusCode },
        error?.response?.data,
      );
    }
  }
  async updateApplication(id, data): Promise<any> {
    try {
      const response = await this.salesforceR3Service.executeAPI(
        `sobjects/Application__c/${id}`,
        'PATCH',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      const message = await this.checkErrorCode(error);
      const errorCode = error?.response?.message[0]?.errorCode;
      const statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      console.log(
        `[SALESFORCE ERROR]: ERROR IN UPDATING APPLICATION FOR ${id}, REQUEST: ${data}`,
      );
      throw new InternalServerErrorException(
        { message, errorCode, statusCode },
        error?.response?.data,
      );
    }
  }
  async getApplicationId(email): Promise<any> {
    try {
      const query = `select Id from Application__c where Email__c = '${email}'`;
      const applicationDetails = await this.salesforceR3Service.executeAPI(
        query,
        'GET',
        null,
      );
      if (applicationDetails[0]?.Id) {
        return applicationDetails[0]?.Id;
      } else {
        return null;
      }
    } catch (error) {
      const message = await this.checkErrorCode(error);
      const errorCode = error?.response?.message[0]?.errorCode;
      const statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      console.log(
        `[SALESFORCE ERROR]: ERROR IN GETTING APPLICATION FOR ${email}`,
      );
      throw new InternalServerErrorException(
        { message, errorCode, statusCode },
        error?.response?.data,
      );
    }
  }
  async getApplication(fields, querCondition = ''): Promise<any> {
    let query = `select ${fields} from Application__c `;
    try {
      if (querCondition != '') {
        query = query + querCondition;
      }
      const applicationDetails = await this.salesforceR3Service.executeAPI(
        query,
        'GET',
        null,
      );
      console.log('applicationDetails', applicationDetails);
      if (applicationDetails.length) {
        return applicationDetails[0];
      } else {
        return null;
      }
    } catch (error) {
      const message = await this.checkErrorCode(error);
      const errorCode = error?.response?.message[0]?.errorCode;
      const statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      console.log(`[SALESFORCE ERROR]: ERROR IN GETTING APPLICATION`);
      throw new InternalServerErrorException(
        { message, errorCode, statusCode },
        error?.response?.data,
      );
    }
  }
  async checkErrorCode(error): Promise<any> {
    const errorCodes = [
      'DUPLICATES_DETECTED',
      'FIELD_CUSTOM_VALIDATION_EXCEPTION',
    ];
    if (errorCodes.includes(error?.response?.message[0]?.errorCode)) {
      return error?.response?.message[0]?.message;
    } else {
      return 'There is an error occurred on saving application. Please contact administrator.';
    }
  }
}
