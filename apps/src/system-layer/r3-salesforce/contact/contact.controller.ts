import { <PERSON>, Body, Post, Patch, Get, Param } from '@nestjs/common';
import { R3SalesforceContactService } from './contact.service';

@Controller('/salesforce/r3')
export class R3SalesforceContactController {
  constructor(
    private readonly r3SalesforceContactService: R3SalesforceContactService,
  ) {}
  @Post('/contact')
  async createContact(@Body() requestBody): Promise<any> {
    return await this.r3SalesforceContactService.createContact(
      requestBody?.data,
    );
  }
  @Patch('/contact')
  async updateContact(@Body() requestBody): Promise<any> {
    return await this.r3SalesforceContactService.updateContact(
      requestBody?.id,
      requestBody?.data,
    );
  }
  @Get('/contact/:email')
  async getContactId(@Param('email') email: string): Promise<any> {
    return await this.r3SalesforceContactService.getContactId(email);
  }
}
