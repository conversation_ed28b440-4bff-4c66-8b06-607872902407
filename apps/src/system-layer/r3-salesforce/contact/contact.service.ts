import {
  Injectable,
  Inject,
  InternalServerErrorException,
  HttpStatus,
} from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';

@Injectable()
export class R3SalesforceContactService {
  constructor(
    @Inject('R3_SALESFORCE_SOURCE')
    private salesforceR3Service: SalesforceService,
  ) {}
  async createContact(data): Promise<any> {
    try {
      const response = await this.salesforceR3Service.executeAPI(
        `sobjects/Contact`,
        'POST',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      const message = await this.checkErrorCode(error);
      const errorCode = error?.response?.message[0]?.errorCode;
      const statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      console.log(
        `[SALESFORCE ERROR]: ERROR IN CREATING CONTACT FOR ${data?.Email}, REQUEST: ${data}`,
      );
      throw new InternalServerErrorException(
        { message, errorCode, statusCode },
        error?.response?.data,
      );
    }
  }
  async updateContact(id, data): Promise<any> {
    try {
      const response = await this.salesforceR3Service.executeAPI(
        `sobjects/Contact/${id}`,
        'PATCH',
        data,
      );
      return { data: response?.data };
    } catch (error) {
      const message = await this.checkErrorCode(error);
      const errorCode = error?.response?.message[0]?.errorCode;
      const statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      console.log(
        `[SALESFORCE ERROR]: ERROR IN UPDATING CONTACT FOR ${id}, REQUEST: ${data}`,
      );
      throw new InternalServerErrorException(
        { message, errorCode, statusCode },
        error?.response?.data,
      );
    }
  }
  async getContactId(email, querCondition = ''): Promise<any> {
    let query = `select Id from Contact where email = '${email}' `;
    try {
      if (querCondition != '') {
        query = query + `and ` + querCondition;
      }
      const contactDetails = await this.salesforceR3Service.executeAPI(
        query,
        'GET',
      );
      if (contactDetails[0]?.Id) {
        return contactDetails[0]?.Id;
      } else {
        return null;
      }
    } catch (error) {
      const message = await this.checkErrorCode(error);
      const errorCode = error?.response?.message[0]?.errorCode;
      const statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      throw new InternalServerErrorException(
        { message, errorCode, statusCode },
        error?.response?.data,
      );
    }
  }
  async checkErrorCode(error): Promise<any> {
    const errorCodes = [
      'DUPLICATES_DETECTED',
      'FIELD_CUSTOM_VALIDATION_EXCEPTION',
      'FIELD_INTEGRITY_EXCEPTION',
    ];
    if (errorCodes.includes(error?.response?.message[0]?.errorCode)) {
      return error?.response?.message[0]?.message;
    } else {
      return 'There is an error occurred on saving application. Please contact administrator.';
    }
  }
}
