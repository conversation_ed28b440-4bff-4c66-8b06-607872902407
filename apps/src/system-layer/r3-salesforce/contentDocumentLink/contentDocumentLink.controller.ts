import { Controller, Body, Post, Delete, Get, Param } from '@nestjs/common';
import { R3SalesforceContentDocumentLinkService } from './contentDocumentLink.service';

@Controller('/salesforce/r3')
export class R3SalesforceContentDocumentLinkController {
  constructor(
    private readonly r3SalesforceContentDocumentLinkService: R3SalesforceContentDocumentLinkService,
  ) {}
  @Post('/contentDocumentLink')
  async createContentDocumentLink(@Body() requestBody): Promise<any> {
    return await this.r3SalesforceContentDocumentLinkService.linkDocumentToObject(
      requestBody?.objectId,
      requestBody?.contentDocumentId,
    );
  }
  @Delete('/contentDocumentLink/:id')
  async deleteRecord(@Param('id') id: string): Promise<any> {
    try {
      await this.r3SalesforceContentDocumentLinkService.deleteContentDocumentLink(
        id,
      );
    } catch (error) {
      console.error('Error de application:', error);
      throw error;
    }
  }
}
