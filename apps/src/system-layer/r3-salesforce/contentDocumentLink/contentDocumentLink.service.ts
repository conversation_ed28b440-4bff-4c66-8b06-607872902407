import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class R3SalesforceContentDocumentLinkService {
  constructor(
    @Inject('R3_SALESFORCE_SOURCE')
    private salesforceR3Service: SalesforceService,
  ) {}
  async linkDocumentToObject(objectId, contentDocumentId) {
    const linkDocumentDetails = {
      LinkedEntityId: objectId,
      ContentDocumentId: contentDocumentId,
      Visibility: 'AllUsers',
    };
    const response = await this.salesforceR3Service.executeAPI(
      `sobjects/ContentDocumentLink`,
      'POST',
      linkDocumentDetails,
    );
    return response?.data?.id;
  }
  async deleteContentDocumentLink(id): Promise<any> {
    await this.salesforceR3Service.executeAPI(
      `sobjects/ContentDocumentLink/${id}`,
      'DELETE',
    );
  }
}
