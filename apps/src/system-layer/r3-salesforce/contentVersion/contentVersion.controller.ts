import { Controller, Body, Post, Patch, Get, Param } from '@nestjs/common';
import { R3SalesforceContentVersionService } from './contentVersion.service';

@Controller('/salesforce/r3')
export class R3SalesforceContentVersionController {
  constructor(
    private readonly r3SalesforceContentVersionService: R3SalesforceContentVersionService,
  ) {}
  @Post('/contentVersion')
  async createContentVersion(@Body() requestBody): Promise<any> {
    return await this.r3SalesforceContentVersionService.createContentVersion(
      requestBody?.data,
    );
  }
  @Get('/contentVersion/:id')
  async getContentDocumentDetails(@Param('id') id: string): Promise<any> {
    return await this.r3SalesforceContentVersionService.getContentDocumentDetails(
      id,
    );
  }
}
