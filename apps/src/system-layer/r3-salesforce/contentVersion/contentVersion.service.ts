import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class R3SalesforceContentVersionService {
  constructor(
    @Inject('R3_SALESFORCE_SOURCE')
    private salesforceR3Service: SalesforceService,
  ) {}
  async createContentVersion(fileJson): Promise<any> {
    const fileDetails = {
      PathOnClient: fileJson.path,
      Title: fileJson.pathName,
      ContentLocation: 'S',
      VersionData: fileJson.file,
    };
    const response = await this.salesforceR3Service.executeAPI(
      `sobjects/ContentVersion`,
      'POST',
      fileDetails,
    );
    return response.data.id;
  }
  async getContentDocumentDetails(documentId): Promise<any> {
    const query = `select Title,FileExtension,FileType,ContentDocumentId from ContentVersion where Id = '${documentId}'`;
    const documentDetails = await this.salesforceR3Service.executeAPI(
      query,
      'GET',
    );
    if (documentDetails[0]) {
      return documentDetails[0];
    } else {
      return null;
    }
  }
}
