import { <PERSON>, Body, Post, Patch, Get, Param } from '@nestjs/common';
import { R3SalesforceOrganizationService } from './organization.service';

@Controller('/salesforce/r3')
export class R3SalesforceOrganizationController {
  constructor(
    private readonly r3SalesforceOrganizationService: R3SalesforceOrganizationService,
  ) {}
  @Get('/organization')
  async getOrganizations(): Promise<any> {
    return await this.r3SalesforceOrganizationService.getOrganizations();
  }
}
