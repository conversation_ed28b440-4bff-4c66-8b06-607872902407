import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class R3SalesforceOrganizationService {
  constructor(
    @Inject('R3_SALESFORCE_SOURCE')
    private salesforceR3Service: SalesforceService,
  ) {}
  async getOrganizations() {
    const query = `SELECT Id,Name FROM Organization__c`;
    const organizations = await this.salesforceR3Service.executeAPI(
      query,
      'GET',
    );
    return organizations;
  }
}
