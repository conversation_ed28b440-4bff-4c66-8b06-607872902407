import { Controller, Get, Param } from '@nestjs/common';
import { R3SalesforcePicklistValueInfoService } from './picklistValueInfo.service';

@Controller('/salesforce/r3')
export class R3SalesforcePicklistValueInfoController {
  constructor(
    private readonly r3SalesforcePicklistValueInfoService: R3SalesforcePicklistValueInfoService,
  ) {}
  @Get('/picklist/countrycodes')
  async getCountryCode(): Promise<any> {
    return await this.r3SalesforcePicklistValueInfoService.getCountryCode();
  }
  @Get('/picklist/maritalstatus')
  async getMaritalStatus(): Promise<any> {
    return await this.r3SalesforcePicklistValueInfoService.getMaritalStatus();
  }
  @Get('/picklist/degree')
  async getDegree(): Promise<any> {
    return await this.r3SalesforcePicklistValueInfoService.getDegree();
  }
  @Get('/picklist/gender')
  async getGender(): Promise<any> {
    return await this.r3SalesforcePicklistValueInfoService.getGender();
  }
  @Get('/picklist/refferalsource')
  async getRefferalSource(): Promise<any> {
    return await this.r3SalesforcePicklistValueInfoService.getRefferalSource();
  }
  @Get('/picklist/citizenshipstatus')
  async getCitizenShipStatus(): Promise<any> {
    return await this.r3SalesforcePicklistValueInfoService.getCitizenShipStatus();
  }
  @Get('/picklist/schools')
  async getSchool(): Promise<any> {
    return await this.r3SalesforcePicklistValueInfoService.getSchools();
  }
  @Get('/picklist/programmes/:recordtypeId')
  async getProgrammes(
    @Param('recordtypeId') recordtypeId: string,
  ): Promise<any> {
    return await this.r3SalesforcePicklistValueInfoService.getProgrammes(
      recordtypeId,
    );
  }
  @Get('/picklist/intakes/:recordtypeId')
  async getIntakes(@Param('recordtypeId') recordtypeId: string): Promise<any> {
    return await this.r3SalesforcePicklistValueInfoService.getIntakes(
      recordtypeId,
    );
  }
}
