import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class R3SalesforcePicklistValueInfoService {
  constructor(
    @Inject('R3_SALESFORCE_SOURCE')
    private r3SalesforceSource: SalesforceService,
  ) {}
  async getCountryCode() {
    let countryCodesDetails;
    try {
      const query = `SELECT Label,Value from PicklistValueInfo where EntityParticle.DurableId = 'Contact.MailingAddress.MailingCountryCode' order by Label asc `;
      countryCodesDetails = await this.r3SalesforceSource.executeAPI(
        query,
        'GET',
      );
    } catch (error) {
      console.log(error);
    }
    return countryCodesDetails;
  }
  async getMaritalStatus() {
    let maritalStatus;
    try {
      const query = `SELECT Label,Value from PicklistValueInfo where EntityParticle.DurableId = '${process.env.R3_PICKLIST_MARITAL_STATUS_DURABLE_ID}' order by Label asc `;
      maritalStatus = await this.r3SalesforceSource.executeAPI(query, 'GET');
    } catch (error) {
      console.log(error);
    }
    return maritalStatus;
  }
  async getGender() {
    let gender;
    try {
      const query = `SELECT Label,Value from PicklistValueInfo where EntityParticle.DurableId = '${process.env.R3_PICKLIST_GENDER_DURABLE_ID}' order by Label asc `;
      gender = await this.r3SalesforceSource.executeAPI(query, 'GET');
    } catch (error) {
      console.log(error);
    }
    return gender;
  }
  async getRefferalSource() {
    let refferalSource;
    try {
      const query = `SELECT Label,Value from PicklistValueInfo where EntityParticle.DurableId = '${process.env.R3_PICKLIST_REFFERAL_SOURCE_DURABLE_ID}' order by Label asc `;
      refferalSource = await this.r3SalesforceSource.executeAPI(query, 'GET');
    } catch (error) {
      console.log(error);
    }
    return refferalSource;
  }
  async getDegree() {
    let degree;
    try {
      const query = `SELECT Label,Value from PicklistValueInfo where EntityParticle.DurableId = '${process.env.R3_PICKLIST_DEGREE_DURABLE_ID}' order by Label asc `;
      degree = await this.r3SalesforceSource.executeAPI(query, 'GET');
    } catch (error) {
      console.log(error);
    }
    return degree;
  }
  async getCitizenShipStatus() {
    let degree;
    try {
      const query = `SELECT Label,Value from PicklistValueInfo where EntityParticle.DurableId = '${process.env.R3_PICKLIST_CITIZENSHIP_DURABLE_ID}' order by Label asc `;
      degree = await this.r3SalesforceSource.executeAPI(query, 'GET');
    } catch (error) {
      console.log(error);
    }
    return degree;
  }
  async getSchools() {
    let schools;
    try {
      const query = `SELECT Name,Id FROM RecordType where SobjectType = 'Application__c' and IsActive = true `;
      schools = await this.r3SalesforceSource.executeAPI(query, 'GET');
    } catch (error) {
      console.log(error);
    }
    return schools;
  }
  async getProgrammes(recordtypeId: string) {
    const programmes = await this.r3SalesforceSource.executeAPI(
      `ui-api/object-info/Application__c/picklist-values/${recordtypeId}/Program_Name__c`,
      'GET',
      null,
      true,
      false,
    );

    return programmes.values ? programmes.values : [];
  }
  async getIntakes(recordtypeId: string) {
    const intakes = await this.r3SalesforceSource.executeAPI(
      `ui-api/object-info/Application__c/picklist-values/${recordtypeId}/Start_Term_Applying_For__c `,
      'GET',
      null,
      true,
      false,
    );

    return intakes.values ? intakes.values : [];
  }
}
