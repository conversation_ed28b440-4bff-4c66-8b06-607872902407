import { Controller, Body, Post, Patch, Delete, Param } from '@nestjs/common';
import { R3SalesforcePrerequesitesAndExperienceService } from './prerequesitesAndExperience.service';

@Controller('/salesforce/r3')
export class R3SalesforcePrerequesitesAndExperienceController {
  constructor(
    private readonly r3SalesforcePrerequesitesAndExperienceService: R3SalesforcePrerequesitesAndExperienceService,
  ) {}
  @Post('/prerequesitesAndExperience')
  async createPrerequesitesAndExperience(@Body() requestBody): Promise<any> {
    return await this.r3SalesforcePrerequesitesAndExperienceService.createPrerequesitesAndExperience(
      requestBody?.data,
    );
  }
  @Patch('/prerequesitesAndExperience')
  async updatePrerequesitesAndExperience(@Body() requestBody): Promise<any> {
    return await this.r3SalesforcePrerequesitesAndExperienceService.updatePrerequesitesAndExperience(
      requestBody?.id,
      requestBody?.data,
    );
  }
  @Delete('/prerequesitesAndExperience/:id')
  async deleteRecord(@Param('id') id: string): Promise<any> {
    try {
      await this.r3SalesforcePrerequesitesAndExperienceService.deletePrerequesitesAndExperience(
        id,
      );
    } catch (error) {
      console.error('Error de application:', error);
      throw error;
    }
  }
}
