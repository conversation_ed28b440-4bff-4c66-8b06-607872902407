import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class R3SalesforcePrerequesitesAndExperienceService {
  constructor(
    @Inject('R3_SALESFORCE_SOURCE')
    private salesforceR3Service: SalesforceService,
  ) {}
  async createPrerequesitesAndExperience(data): Promise<any> {
    const response = await this.salesforceR3Service.executeAPI(
      `sobjects/Prerequesites_and_Experience__c`,
      'POST',
      data,
    );
    return { data: response?.data };
  }
  async updatePrerequesitesAndExperience(id, data): Promise<any> {
    const response = await this.salesforceR3Service.executeAPI(
      `sobjects/Prerequesites_and_Experience__c/${id}`,
      'PATCH',
      data,
    );
    return { data: response?.data };
  }
  async deletePrerequesitesAndExperience(id): Promise<any> {
    await this.salesforceR3Service.executeAPI(
      `sobjects/Prerequesites_and_Experience__c/${id}`,
      'DELETE',
    );
  }
}
