import { Module } from '@nestjs/common';
import { R3SalesforceApplicationController } from './application/application.controller';
import { R3SalesforceApplicationService } from './application/application.service';
import { R3SalesforceContactController } from './contact/contact.controller';
import { R3SalesforceContactService } from './contact/contact.service';
import { SalesforceModule } from 'apps/libs/salesforce/salesforce.module';
import { R3SalesforcePrerequesitesAndExperienceService } from './prerequesitesAndExperience/prerequesitesAndExperience.service';
import { R3SalesforcePrerequesitesAndExperienceController } from './prerequesitesAndExperience/prerequesitesAndExperience.controller';
import { R3SalesforceRecommenderController } from './recommender/recommender.controller';
import { R3SalesforceRecommenderService } from './recommender/recommender.service';
import { R3SalesforceRecordTypeController } from './recordType/recordType.controller';
import { R3SalesforceRecordTypeService } from './recordType/recordType.service';
import { R3SalesforceContentVersionService } from './contentVersion/contentVersion.service';
import { R3SalesforceContentVersionController } from './contentVersion/contentVersion.controller';
import { R3SalesforceContentDocumentLinkService } from './contentDocumentLink/contentDocumentLink.service';
import { R3SalesforceContentDocumentLinkController } from './contentDocumentLink/contentDocumentLink.controller';
import { R3SalesforceOrganizationController } from './organization/organization.controller';
import { R3SalesforcePicklistValueInfoController } from './picklistValueInfo/picklistValueInfo.controller';
import { R3SalesforceOrganizationService } from './organization/organization.service';
import { R3SalesforcePicklistValueInfoService } from './picklistValueInfo/picklistValueInfo.service';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Module({
  providers: [
    R3SalesforceContactController,
    R3SalesforceApplicationController,
    R3SalesforceContactService,
    R3SalesforceApplicationService,
    R3SalesforcePrerequesitesAndExperienceService,
    R3SalesforcePrerequesitesAndExperienceController,
    R3SalesforceRecommenderController,
    R3SalesforceRecommenderService,
    R3SalesforceRecordTypeController,
    R3SalesforceRecordTypeService,
    R3SalesforceContentVersionService,
    R3SalesforceContentVersionController,
    R3SalesforceContentDocumentLinkService,
    R3SalesforceContentDocumentLinkController,
    R3SalesforceOrganizationController,
    R3SalesforcePicklistValueInfoController,
    R3SalesforceOrganizationService,
    R3SalesforcePicklistValueInfoService,
    {
      provide: 'R3_SALESFORCE_SOURCE',
      useFactory: async () => {
        return new SalesforceService(
          process.env.R3_CONSUMER_KEY,
          process.env.R3_CONSUMER_SECRET,
          process.env.R3_AUTH_URL,
          process.env.R3_ACCESS_TOKEN_SECRET,
          process.env.R3_GRANT_TYPE,
          process.env.R3_USER_NAME,
          process.env.R3_PASSWORD,
        );
      },
      inject: [],
    },
  ],
  controllers: [
    R3SalesforceContactController,
    R3SalesforceApplicationController,
    R3SalesforcePrerequesitesAndExperienceController,
    R3SalesforceRecommenderController,
    R3SalesforceRecordTypeController,
    R3SalesforceContentVersionController,
    R3SalesforceContentDocumentLinkController,
    R3SalesforceOrganizationController,
    R3SalesforcePicklistValueInfoController,
  ],
  exports: [
    R3SalesforceContactController,
    R3SalesforceApplicationController,
    R3SalesforceApplicationService,
    R3SalesforceContactService,
    R3SalesforcePrerequesitesAndExperienceService,
    R3SalesforceRecommenderService,
    R3SalesforceRecordTypeService,
    R3SalesforceContentVersionService,
    R3SalesforceContentDocumentLinkService,
  ],
  imports: [SalesforceModule],
})
export class R3SalesforceSystemModule {}
