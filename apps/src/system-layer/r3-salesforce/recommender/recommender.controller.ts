import { Controller, Body, Post, Patch, Delete, Param } from '@nestjs/common';
import { R3SalesforceRecommenderService } from './recommender.service';

@Controller('/salesforce/r3')
export class R3SalesforceRecommenderController {
  constructor(
    private readonly r3SalesforceRecommenderService: R3SalesforceRecommenderService,
  ) {}
  @Post('/recommender')
  async createRecommender(@Body() requestBody): Promise<any> {
    return await this.r3SalesforceRecommenderService.createRecommender(
      requestBody?.data,
    );
  }
  @Patch('/recommender')
  async updateRecommender(@Body() requestBody): Promise<any> {
    return await this.r3SalesforceRecommenderService.updateRecommender(
      requestBody?.id,
      requestBody?.data,
    );
  }
  @Delete('/recommender/:id')
  async deleteRecommender(@Param('id') id: string): Promise<any> {
    try {
      await this.r3SalesforceRecommenderService.deleteRecommender(id);
    } catch (error) {
      console.error('Error de application:', error);
      throw error;
    }
  }
}
