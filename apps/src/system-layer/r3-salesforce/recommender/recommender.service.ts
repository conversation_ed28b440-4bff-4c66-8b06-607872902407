import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class R3SalesforceRecommenderService {
  constructor(
    @Inject('R3_SALESFORCE_SOURCE')
    private salesforceR3Service: SalesforceService,
  ) {}
  async createRecommender(data): Promise<any> {
    const response = await this.salesforceR3Service.executeAPI(
      `sobjects/Recommender__c`,
      'POST',
      data,
    );
    return { data: response?.data };
  }
  async updateRecommender(id, data): Promise<any> {
    const response = await this.salesforceR3Service.executeAPI(
      `sobjects/Recommender__c/${id}`,
      'PATCH',
      data,
    );
    return { data: response?.data };
  }
  async deleteRecommender(id): Promise<any> {
    await this.salesforceR3Service.executeAPI(
      `sobjects/Recommender__c/${id}`,
      'DELETE',
    );
  }
}
