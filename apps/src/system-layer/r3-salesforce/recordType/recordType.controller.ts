import { Controller, Get, Param } from '@nestjs/common';
import { R3SalesforceRecordTypeService } from './recordType.service';

@Controller('/salesforce/r3')
export class R3SalesforceRecordTypeController {
  constructor(
    private readonly r3SalesforceRecordTypeService: R3SalesforceRecordTypeService,
  ) {}
  @Get('/recordtypeid/:type')
  async getRecordTypeIdByType(@Param('type') type: string): Promise<any> {
    return await this.r3SalesforceRecordTypeService.getRecordTypeIdByType(type);
  }
}
