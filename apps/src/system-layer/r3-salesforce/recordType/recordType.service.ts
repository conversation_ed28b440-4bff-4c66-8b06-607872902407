import { Injectable, Inject } from '@nestjs/common';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
@Injectable()
export class R3SalesforceRecordTypeService {
  constructor(
    @Inject('R3_SALESFORCE_SOURCE')
    private salesforceR3Service: SalesforceService,
  ) {}
  async getRecordTypeIdByType(type): Promise<any> {
    const query = `select Id from RecordType where DeveloperName = '${type}'`;
    const RecordTypeDetails = await this.salesforceR3Service.executeAPI(
      query,
      'GET',
    );
    if (RecordTypeDetails[0]?.Id) {
      return RecordTypeDetails[0]?.Id;
    } else {
      return null;
    }
  }
}
