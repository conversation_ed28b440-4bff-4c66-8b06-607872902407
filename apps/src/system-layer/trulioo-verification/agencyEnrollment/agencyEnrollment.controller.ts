import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Request } from 'express';
import { AgencyEnrollmentService } from './agencyEnrollment.service';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { AgencyEnrollmentDto } from './dtos/agencyEnrollment.dto';

@Controller('gus/trulioo/agencykyb')
@UsePipes(new ValidationPipe({ whitelist: true }))
export class AgencyEnrollmentController {
  constructor(
    private readonly agencyEnrollmentService: AgencyEnrollmentService,
    private readonly parameterStoreCacheService: ParameterStoreCacheService,
  ) {}

  @Post('enroll/:enquiryTypeId')
  async EnrollKYBMonitoring(
    @Param('enquiryTypeId') enquiryTypeId: string,
    @Body() enrollmentData: AgencyEnrollmentDto,
    @Req() request: Request,
  ): Promise<any> {
    try {
      enrollmentData.callbackUrl =
        (await this.parameterStoreCacheService.getParameter(
          'TRULIOO_CALLBACK_URL',
        )) as string;
      return await this.agencyEnrollmentService.enrollKYBMonitoring(
        enrollmentData,
        request['requestId'],
        request['account-Id'],
      );
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('enrollment/history/:enquiryTypeId')
  async getKYBEnrollmentData(
    @Param('enquiryTypeId') enquiryTypeId: string,
    @Query('enrollmentID') enrollmentID: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.agencyEnrollmentService.getKYBEnrollmentData(
        enrollmentID,
        request['requestId'],
        request['account-Id'],
      );
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
