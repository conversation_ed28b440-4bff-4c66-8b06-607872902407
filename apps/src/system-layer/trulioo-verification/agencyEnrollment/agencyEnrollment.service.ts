import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { UtilsService } from '../utils.service';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { makeHTTPRequest } from 'apps/libs/utils/utils';

@Injectable()
export class AgencyEnrollmentService {
  constructor(
    private readonly httpService: HttpService,
    private readonly utilsService: UtilsService,
    @Inject('KYBKYCLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly loggerEnum: LoggerEnum,
    private readonly parameterStoreCacheService: ParameterStoreCacheService,
  ) {}

  async enrollKYBMonitoring(
    enrollmentData: any,
    correlationId: string,
    accountId: string,
  ): Promise<any> {
    try {
      await this.cloudWatchLoggerService.log(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.REGISTER_KYB_ENROLLMENT,
        this.loggerEnum.UseCase.KYB_ENROLLMENT,
        enrollmentData,
        null,
        'Enroll Ongoing KYB',
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );
      const url = `${await this.parameterStoreCacheService.getParameter(
        'TRULIOO_IDENTITY_ENROLLMENT_URL',
      )}/Enrollment/CreateEnrollmentWithSubject`;

      const token = await this.utilsService.getAuthToken();
      let config = {
        headers: {
          Authorization: `Bearer ${token}`,
          packageId: await this.parameterStoreCacheService.getParameter(
            'TRULIOO_KYB_PACKAGE_ID',
          ),
        },
      };
      return (
        await makeHTTPRequest<any>(
          this.httpService,
          'post',
          url,
          enrollmentData,
          config,
        )
      ).data;
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.REGISTER_KYB_ENROLLMENT,
        this.loggerEnum.UseCase.KYB_ENROLLMENT,
        enrollmentData,
        error,
        error,
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );
      throw error;
    }
  }

  async getKYBEnrollmentData(
    enrollmentId: any,
    correlationId: string,
    accountId: string,
  ): Promise<any> {
    try {
      await this.cloudWatchLoggerService.log(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.GET_KYB_ENROLLMENT_HISTORY,
        this.loggerEnum.UseCase.KYB_ENROLLMENT,
        enrollmentId,
        null,
        'Fetch KYB Enrollment History',
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );
      const url = `${await this.parameterStoreCacheService.getParameter(
        'TRULIOO_IDENTITY_ENROLLMENT_URL',
      )}/Enrollment/History/${enrollmentId}`;

      const token = await this.utilsService.getAuthToken();
      let config = {
        headers: {
          Authorization: `Bearer ${token}`,
          packageId: await this.parameterStoreCacheService.getParameter(
            'TRULIOO_KYB_PACKAGE_ID',
          ),
        },
      };
      return (
        await makeHTTPRequest<any>(this.httpService, 'get', url, null, config)
      ).data;
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_KYB_ENROLLMENT_JOB,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.GET_KYB_ENROLLMENT_HISTORY,
        this.loggerEnum.UseCase.KYB_ENROLLMENT,
        enrollmentId,
        error,
        error,
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );
      throw error;
    }
  }
}
