import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString, IsUrl } from 'class-validator';

class BusinessEntityToMonitorDto {
  @IsNotEmpty()
  @IsString()
  Subject: string;
}

export class AgencyEnrollmentDto {
  @IsOptional()
  @IsUrl()
  callbackUrl?: string;

  @IsNotEmpty()
  @IsString()
  frequency: string;

  @IsNotEmpty()
  @Type(() => BusinessEntityToMonitorDto)
  BusinessEntityToMonitor: BusinessEntityToMonitorDto;

  @IsNotEmpty()
  @IsString()
  CustomerReferenceId: string;

  @IsNotEmpty()
  @IsString()
  serviceName: string;
}
