import {
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Query,
  Req,
} from '@nestjs/common';
import { AgencyScreeningService } from './agencyScreening.service';
import { Request } from 'express';

@Controller('gus/trulioo/agencykyb')
export class AgencyScreeningController {
  constructor(
    private readonly agencyScreeningService: AgencyScreeningService,
  ) {}

  @Get('profilefeedback/:enquiryTypeId')
  async getWatchlistFeedback(
    @Param('enquiryTypeId') enquiryTypeId: string,
    @Query('clientId') clientId: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.agencyScreeningService.getWatchlistFeedback(
        clientId,
        request['requestId'],
        request['account-Id'],
      );
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Patch('feedbackstatus/:enquiryTypeId')
  async updateFeedbackStatus(
    @Param('enquiryTypeId') enquiryTypeId: string,
    @Query() query: any,
    @Body() body: any,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.agencyScreeningService.updateFeedbackStatus(
        body.Id,
        query.serviceType,
        request['requestId'],
        request['account-Id'],
      );
    } catch (error) {
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
