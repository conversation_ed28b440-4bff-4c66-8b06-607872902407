import { Inject, Injectable } from '@nestjs/common';
import * as utils from 'apps/libs/utils/utils';
import { UtilsService } from '../utils.service';
import { HttpService } from '@nestjs/axios';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';
import { retry } from 'rxjs';
@Injectable()
export class AgencyScreeningService {
  constructor(
    private readonly httpService: HttpService,
    private readonly utilsService: UtilsService,
    @Inject('KYBKYCLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly loggerEnum: LoggerEnum,
    private readonly parameterStoreCacheService: ParameterStoreCacheService,
    @Inject('GUS_SALESFORCE_SOURCE')
    private gusSalesforceService: SalesforceService,
  ) {}

  async getWatchlistFeedback(
    ClientID: string,
    correlationId: string,
    accountId: string,
  ): Promise<any> {
    try {
      await this.cloudWatchLoggerService.log(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.GET_KYB_SCREENING_FEEDBACK,
        this.loggerEnum.UseCase.KYB_SCREENING,
        ClientID,
        null,
        'Screening KYB Results',
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );

      const url = `${await this.parameterStoreCacheService.getParameter(
        'TRULIOO_KYB_SCREENING_URL',
      )}/${ClientID}?includeFullServiceDetails=true`;

      const token = await this.utilsService.getAuthToken();
      let config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };

      return (
        await utils.makeHTTPRequest<any>(
          this.httpService,
          'get',
          url,
          '',
          config,
        )
      ).data;
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.GET_KYB_SCREENING_FEEDBACK,
        this.loggerEnum.UseCase.KYB_SCREENING,
        ClientID,
        error.message,
        error,
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );

      throw error;
    }
  }

  async updateFeedbackStatus(
    Id: string,
    serviceType: string,
    correlationId: string,
    accountId: string,
  ): Promise<any> {
    try {
      await this.cloudWatchLoggerService.log(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.UPDATE_KYB_FEEDBACK_STATUS,
        this.loggerEnum.UseCase.KYB_SCREENING,
        { Id, serviceType },
        null,
        'Save Feedback Reception status',
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );

      const response = await this.gusSalesforceService.executeAPI(
        `sobjects/Screening_Enquiry__c/${Id}`,
        'PATCH',
        serviceType === 'BusinessWatchlistScreening'
          ? { WL_Ready__c: 'true' }
          : { BE_Ready__c: 'true' },
      );
      return response?.data === '' ? 'success' : response?.data;
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component.GUS_KYB_KYC_FEEDBACK_INTEGRATION_QUEUE,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.UPDATE_KYB_FEEDBACK_STATUS,
        this.loggerEnum.UseCase.KYB_SCREENING,
        { Id, serviceType },
        error.message,
        error,
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );

      throw error;
    }
  }
}
