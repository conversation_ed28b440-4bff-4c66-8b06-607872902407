import { HttpService } from '@nestjs/axios';
import { Inject, Injectable } from '@nestjs/common';
import { UtilsService } from '../utils.service';
import * as utils from 'apps/libs/utils/utils';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';

@Injectable()
export class AgencyUnEnrollmentService {
  constructor(
    private readonly httpService: HttpService,
    private readonly utilsService: UtilsService,
    @Inject('KYBKYCLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly loggerEnum: LoggerEnum,
    private readonly parameterStoreCacheService: ParameterStoreCacheService,
  ) {}

  async unEnrollKYBMonitoring(
    EnrollmentID: string,
    correlationId: string,
    accountId: string,
  ): Promise<any> {
    try {
      await this.cloudWatchLoggerService.log(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component
          .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.REMOVE_KYB_ENROLLMENT,
        this.loggerEnum.UseCase.KYB_UNENROLLMENT,
        EnrollmentID,
        null,
        'Unenroll Ongoing KYB',
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );
      const url = `${await this.parameterStoreCacheService.getParameter(
        'TRULIOO_IDENTITY_ENROLLMENT_URL',
      )}/Enrollment/${EnrollmentID}`;

      const token = await this.utilsService.getAuthToken();
      let config = {
        headers: {
          Authorization: `Bearer ${token}`,
          packageId: await this.parameterStoreCacheService.getParameter(
            'TRULIOO_KYB_PACKAGE_ID',
          ),
        },
      };

      return (
        await utils.makeHTTPRequest<any>(
          this.httpService,
          'delete',
          url,
          '',
          config,
        )
      ).data;
    } catch (error) {
      await this.cloudWatchLoggerService.error(
        correlationId,
        new Date().toISOString(),
        this.loggerEnum.Component.GUS_EIP_SERVICE,
        this.loggerEnum.Component
          .GUS_SALESFORCE_KYB_KYC_PLATFORM_EVENT_LISTENER,
        this.loggerEnum.Component.SALESFORCE,
        this.loggerEnum.Event.REMOVE_KYB_ENROLLMENT,
        this.loggerEnum.UseCase.KYB_UNENROLLMENT,
        EnrollmentID,
        error.message,
        error,
        null,
        null,
        `gus-middleware-service/kybVerification/${accountId}/${correlationId}`,
        'accountId',
        accountId,
      );
      throw error;
    }
  }
}
