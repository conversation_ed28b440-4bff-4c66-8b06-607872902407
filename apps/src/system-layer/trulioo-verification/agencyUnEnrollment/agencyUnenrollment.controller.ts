import { Body, Controller, Delete, HttpException, HttpStatus, Param, Query, Req } from '@nestjs/common';
import { AgencyUnEnrollmentService } from './agencyUnEnrollement.service';
import { Request } from 'express';

@Controller('gus/trulioo/agencykyb')
export class AgencyUnenrollmentController {
    constructor(private readonly agencyUnEnrollment: AgencyUnEnrollmentService){}

    @Delete('unenroll/:enquiryTypeId')
    async unEnrollKYBMonitoring(
        @Param('enquiryTypeId') enquiryTypeId: string,
        @Query('EnrollmentId') EnrollmentId: string,
        @Req() request: Request
    ): Promise<any>{
        try{
            return await this.agencyUnEnrollment.unEnrollKYBMonitoring(EnrollmentId, request['requestId'], request['account-Id']);
        }catch(error){
            throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
