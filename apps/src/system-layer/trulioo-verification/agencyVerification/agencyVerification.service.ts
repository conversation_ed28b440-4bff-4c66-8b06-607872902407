import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import * as utils from 'apps/libs/utils/utils';
import { UtilsService } from '../utils.service';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';

@Injectable()
export class AgencyVerificationService {
    constructor(
      private readonly httpService: HttpService,
      private readonly utilsService: UtilsService,
      private readonly parameterStoreCacheService: ParameterStoreCacheService,
    ){}
    
    async submitKYBVerificationData(data: any): Promise<any> {
      try{
        const url = `${await this.parameterStoreCacheService.getParameter('TRULIOO_KYB_URL')}/submit/${await this.parameterStoreCacheService.getParameter('TRULIOO_KYB_FLOW_ID')}`;
        const token = await this.utilsService.getAuthToken()

        let config = {
                          headers: {
                            Authorization: `Bearer ${token}`,
                          }
                       } 
        const response = await utils.makeHTTPRequest<any>(this.httpService, 'post', url, data, config)
        return {
          data: response.data,
          headers: response.headers
        }
      }catch(error){
        throw error; 
      }
    }
  
}
