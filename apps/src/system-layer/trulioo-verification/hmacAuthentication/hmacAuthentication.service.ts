import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';
import { Injectable } from '@nestjs/common';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import * as crypto from 'crypto';

@Injectable()
export class HmacAuthenticationService {
  constructor(
    private readonly parameterStoreCacheService: ParameterStoreCacheService,
  ){}

  async verifyHmacSignature(payload: any, receivedSignature: string): Promise<boolean> {
    try{
      const client = new SecretsManagerClient({
        region: process.env.REGION
      });  
      const response: any = await client.send(
        new GetSecretValueCommand({
          SecretId: await this.parameterStoreCacheService.getParameter('TRULIOO_HMAC_SECRET_NAME') as string
        })
      );
      const calculatedHmac = await crypto
        .createHmac('sha256', response.SecretString)
        .update(payload)
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(calculatedHmac),
        Buffer.from(receivedSignature),
      );
    }catch(error){
      throw error;
    }
  }
}
