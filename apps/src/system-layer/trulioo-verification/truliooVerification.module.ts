import { DynamicModule, Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AgencyVerificationService } from './agencyVerification/agencyVerification.service';
import { OauthModule } from 'apps/src/common/oauth/oauth.module';
import { AgencyUnEnrollmentService } from './agencyUnEnrollment/agencyUnEnrollement.service';
import { UtilsService } from './utils.service';
import { AgencyUnenrollmentController } from './agencyUnEnrollment/agencyUnenrollment.controller';
import { LoggerModule } from '@gus-eip/loggers';
import { AgencyScreeningService } from './agencyScreening/agencyScreening.service';
import { AgencyScreeningController } from './agencyScreening/agencyScreening.controller';
import { HmacAuthenticationService } from './hmacAuthentication/hmacAuthentication.service';
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';
import { AgencyEnrollmentService } from './agencyEnrollment/agencyEnrollment.service';
import { AgencyEnrollmentController } from './agencyEnrollment/agencyEnrollment.controller';
import { SalesforceService } from 'apps/libs/salesforce/salesforce.service';

@Module({})
export class TruliooVerificationModule {
  static async forRoot(
    parameterStoreCacheService: ParameterStoreCacheService,
  ): Promise<DynamicModule> {
    const logGroupname = (await parameterStoreCacheService.getParameter(
      'KYB_KYC_LOGGER_LOG_GROUP_NAME',
    )) as string;

    return {
      module: TruliooVerificationModule,
      imports: [
        OauthModule,
        HttpModule,
        LoggerModule.forRoot({
          region: process.env.REGION,
          logGroupName: logGroupname,
          options: 'KYBKYCLogger',
          teamWebhookUrl: '',
          isAlertNeeded: false,
        }),
      ],
      providers: [
        UtilsService,
        AgencyVerificationService,
        AgencyUnEnrollmentService,
        AgencyScreeningService,
        HmacAuthenticationService,
        AgencyEnrollmentService,
        {
          provide: 'GUS_SALESFORCE_SOURCE',
          useFactory: async () => {
            return new SalesforceService(
              process.env.GUS_CONSUMER_KEY,
              process.env.GUS_CONSUMER_SECRET,
              process.env.GUS_AUTH_URL,
              process.env.ACCESS_TOKEN_SECRET,
              process.env.GUS_GRANT_TYPE,
              process.env.GUS_USER_NAME,
              process.env.GUS_PASSWORD,
            );
          },
          inject: [],
        },
      ],
      exports: [AgencyVerificationService, HmacAuthenticationService],
      controllers: [
        AgencyUnenrollmentController,
        AgencyScreeningController,
        AgencyEnrollmentController,
      ],
    };
  }
}
