import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { OauthService } from 'apps/src/common/oauth/oauth.service';
import {SecretsManagerClient, GetSecretValueCommand,} from "@aws-sdk/client-secrets-manager";
import { ParameterStoreCacheService } from 'apps/src/common/parameterStoreCache/parameterStoreCache.service';

@Injectable()
export class UtilsService {
    constructor(
      private readonly httpService: HttpService,
      private readonly oauthService: OauthService,
      private readonly parameterStoreCacheService: ParameterStoreCacheService,
    ){}

    async getAuthToken(){
      try{
        const client = new SecretsManagerClient({
          region: process.env.REGION
        });  
        const response: any = await client.send(
          new GetSecretValueCommand({
            SecretId: await this.parameterStoreCacheService.getParameter('TRULIOO_OAUTH_CREDS_SECRET_NAME') as string
          })
        );
        const secret = JSON.parse(response.SecretString);
        const token = await this.oauthService.generateToken(
          secret.access_token_url,
          secret.client_id,
          secret.client_secret,
          'client_credentials')
        return token;
      }catch(error){
        throw new HttpException(
          `Failed to retrieve token: ${error.message}`,
           HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }
  
}
