stage: prod
role_arn: arn:aws:iam::${aws:accountId}:role/lambda-exec-role-${self:provider.stage}
variables:
  SALESFORCE_API_VERSION: v60.0
  GUS_CONSUMER_KEY: 3MVG9I5UQ_0k_hTlxg9Ks4vR.y9A_7OVQjFs2WVLeuC4yCkJCjlyzTavFKb5xohv3kJ1soWp9E4PcoisP5F5W
  GUS_CONSUMER_SECRET: ****************************************************************
  GUS_AUTH_URL: https://iapro.my.salesforce.com/services/oauth2/token
  REGION: eu-west-1
  GUS_USER_NAME: <EMAIL>
  GUS_PASSWORD: 5@{`bdT!!W0zjLACxWqHDwEYk1EiYQRg2AL
  GUS_GRANT_TYPE: password
  R3_AUTH_URL: https://r3edu.my.salesforce.com/services/oauth2/token
  R3_GRANT_TYPE: password
  R3_USER_NAME: <EMAIL>
  R3_PASSWORD: 6Q7!8kv$YiZOlHzXv9s2ZcXEhySiDWOyGvcU
  R3_CONSUMER_KEY: 3MVG9I5UQ_0k_hTlxg9Ks4vR.y9A_7OVQjFs2WVLeuC4yCkJCjlyzTavFKb5xohv3kJ1soWp9E4PcoisP5F5W
  R3_CONSUMER_SECRET: ****************************************************************
  LEAD_TABLE: gus-eip-lead-${self:provider.stage}
  CONSUMER_CONFIG_TABLE: gus-eip-consumer-${self:provider.stage}
  ACCESS_TOKEN_SECRET: salesforce-gus-${self:provider.stage}-access-token
  R3_ACCESS_TOKEN_SECRET: salesforce-R3-${self:provider.stage}-access-token
  APPHERO_LOOKUP_TABLE: apphero-lookup-${self:provider.stage}
  APPHERO_APPLICATION_BASKET_TABLE: apphero-application-basket-${self:provider.stage}
  PERSON_ACCOUNT_RECORDTYPE_ID: 0120O0000007L8XQAU
  APPHERO_COMPARE_PROGRAMME_BASKET_TABLE: apphero-compare-programme-basket-${self:provider.stage}
  GUS_MIDDLEWARE_SF_INSTITUTION_CACHE_TABLE: oap-sf-institution-cache-${self:provider.stage}
  S3_BUCKET_ACCESS_ROLE_ARN: arn:aws:iam::************:role/s3CrossAccountAccessRole-prod
  REVIEW_CENTER_BUCKET_NAME: reviewcenter
  LEAD_SALES_QUEUE_ID: 00G0O000004GrIT
  PROFILE_PICTURE_BUCKET: apphero-profile-picture-${self:provider.stage}
  R3_GUS_CONSUMER_KEY: 3MVG9I5UQ_0k_hTlxg9Ks4vR.y9A_7OVQjFs2WVLeuC4yCkJCjlyzTavFKb5xohv3kJ1soWp9E4PcoisP5F5W
  R3_GUS_CONSUMER_SECRET: ****************************************************************
  R3_GUS_AUTH_URL: https://iapro.my.salesforce.com/services/oauth2/token
  R3_GUS_USER_NAME: <EMAIL>
  R3_GUS_PASSWORD: 5@{`bdT!!W0zjLACxWqHDwEYk1EiYQRg2AL
  R3_GUS_GRANT_TYPE: password
  HZU_SF_USERNAME: <EMAIL>
  HZU_SF_PASSWORD: xz1zE6lTVHTRNRia9IWk1CVXjNq8jWlsDTB9TjJ
  HZU_SF_CLIENT_ID: 3MVG9I5UQ_0k_hTlxg9Ks4vR.y9A_7OVQjFs2WVLeuC4yCkJCjlyzTavFKb5xohv3kJ1soWp9E4PcoisP5F5W
  HZU_SF_CLIENT_SECRET: ****************************************************************
  HZU_SF_GRANT_TYPE: password
  HZU_AUTH_URL: https://herzinguniversity.my.salesforce.com/services/oauth2/token
  HZU_ACCESS_TOKEN_SECRET: salesforce-hzu-prod-access-secret-token
  R3_GUS_ACCESS_TOKEN_SECRET: salesforce-gus-${self:provider.stage}-access-token
  API_ID: 1b8vt5pych
  R3_PICKLIST_MARITAL_STATUS_DURABLE_ID: Contact.00N4P00000GLVtH
  R3_PICKLIST_GENDER_DURABLE_ID: Contact.00N4P00000GLVtC
  R3_PICKLIST_CITIZENSHIP_DURABLE_ID: Contact.00N4P00000GLVtW
  R3_PICKLIST_REFFERAL_SOURCE_DURABLE_ID: 01I4P000001m0yC.00N4P00000GLWLV
  R3_PICKLIST_DEGREE_DURABLE_ID: 01I4P000001m0yR.00N4P00000GLgZ3
  ENVIRONMENT_TAG: PROD
  STUDENT_DOC_S3_BUCKET: gus-student-profile-${self:provider.stage}
  EIP_API_ID: ycwq8lve4d
  STUDENT_DETAILS_API: https://profile-api.apphero.io
  LOGGER_VERSION: v1
  LOGGER_LOG_GROUP_NAME: oap-loggers-prod
  HZU_LOGGER_LOG_GROUP_NAME: eip-integration-logs-prod
  STAGE: prod
  SES_SENDER_EMAIL: <EMAIL>
  TO_EMAIL: <EMAIL>
  TO_CC: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
  APPHERO_API: https://api.apphero.io
  S3_CROSS_ACCESS_ROLE_ARN: arn:aws:iam::************:role/s3CrossAccountAccessRole-prod
  TEAMS_WEBHOOK_URL: https://gus.webhook.office.com/webhookb2/915b40cd-36b9-407e-acfe-0c8902cda66a@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/fb94eb8cbead4a3b86c56c692dbed77b/859e2344-7909-4d60-8e88-0301ddcc3d46
