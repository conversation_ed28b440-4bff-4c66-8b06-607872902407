{"name": "gus-apphero-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.414.0", "@aws-sdk/client-sns": "^3.744.0", "@aws-sdk/client-sqs": "^3.632.0", "@aws-sdk/client-ssm": "^3.637.0", "@gus-eip/loggers": "^4.0.6", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^9.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^9.0.0", "@nestjs/graphql": "^12.0.8", "@nestjs/platform-express": "^9.0.0", "@nestjs/swagger": "^7.1.8", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1428.0", "aws-serverless-express": "^3.4.0", "aws-xray-sdk": "^3.5.1", "aws-xray-sdk-core": "^3.5.1", "axios": "^1.7.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto": "^1.0.1", "date-fns": "^2.30.0", "express": "^4.18.2", "http": "0.0.1-security", "https": "^1.0.0", "jsonwebtoken": "^9.0.2", "loggers@4.0.3": "link:gus-eip/loggers@4.0.3", "nestjs": "^0.0.1", "raw-body": "^3.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-aws-cloudwatch": "^3.0.0"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/express": "^4.17.13", "@types/jest": "29.5.0", "@types/node": "18.15.11", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "dotenv": "^16.4.5", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.5.0", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}