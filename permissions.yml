Resources:  
   SalesforceAPIGWAPIPermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:gus-eip-services-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.EIP_API_ID}/*/GET/salesforce/api" 
   SalesforceRootAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:gus-eip-services-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:eu-west-1:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.EIP_API_ID}/*/*/salesforce/*"     
   StudentDetailsRootAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:gus-eip-services-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:eu-west-1:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.EIP_API_ID}/*/*/profile/getstudentdetail/*"    
   IBATPipelineRootAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:gus-eip-services-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:eu-west-1:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.EIP_API_ID}/*/*/ibat/studentapplications"
   IBATPipelineValidationApiRootAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:gus-eip-services-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:eu-west-1:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.EIP_API_ID}/*/*/ibat/gusidvalidation"
   CobrandingAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:gus-eip-services-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:eu-west-1:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.EIP_API_ID}/*/*/apphero/cobrandedurl"
   TruliooAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:gus-eip-services-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:eu-west-1:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.EIP_API_ID}/*/*/gus/trulioo/*"
   CertValidationAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:gus-eip-services-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:eu-west-1:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.EIP_API_ID}/*/*/gus/student/certificate/*"
   OCRAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:gus-eip-services-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:eu-west-1:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.EIP_API_ID}/*/*/gus/ocr/*"
   ProficiencyQualificationTestProviderAuthCredsGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:gus-eip-services-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:eu-west-1:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.EIP_API_ID}/*/*/gus/proficiencyQualification/*"
