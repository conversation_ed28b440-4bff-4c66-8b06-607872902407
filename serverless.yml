service: gus-eip-service

provider:
  name: aws
  runtime: nodejs16.x
  stage: ${opt:stage, 'dev'}
  region: eu-west-1
  role: ${file(env.${self:provider.stage}.yml):role_arn}
  ecr:
    images:
      appimage:
        path: ./
resources:
  - ${file(tables.yml)}
  - ${file(permissions.yml)}
  - ${file(log-groups.yml)}
functions:
  gus-eip-service:
    name: 'gus-eip-services-${self:provider.stage}'
    architecture: x86_64
    image:
      name: appimage
      command:
        - dist/src/main.handler
      entryPoint:
        - '/lambda-entrypoint.sh'
    timeout: 160
    environment: ${file(env.${self:provider.stage}.yml):variables}
    tags:
      ENVIRONMENT: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
      TEAM: EIP Development Team
      PROJECT: EIP
