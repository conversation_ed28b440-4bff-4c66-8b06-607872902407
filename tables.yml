Resources:
  LeadTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-eip-lead-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: EIP
  consumerTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-eip-consumer-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: EIP
  certValidationKeysTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-eip-cert-validation-keys-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK #(brand)
          AttributeType: S
        - AttributeName: SK #(validationType)
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: EIP
  localizationPicklistTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-eip-picklist-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: EIP
